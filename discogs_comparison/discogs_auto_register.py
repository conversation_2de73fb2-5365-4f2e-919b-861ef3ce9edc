#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Discogs 自动注册脚本
支持多账号批量注册，包含完善的错误处理和日志记录
"""

import sys
import os
import time
import random
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# Selenium相关导入
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.firefox.service import Service as FirefoxService
    from selenium.webdriver.edge.service import Service as EdgeService
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.firefox.options import Options as FirefoxOptions
    from selenium.webdriver.edge.options import Options as EdgeOptions
    from selenium.common.exceptions import (
        TimeoutException, NoSuchElementException, WebDriverException,
        ElementNotInteractableException, StaleElementReferenceException
    )
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from webdriver_manager.firefox import GeckoDriverManager
        from webdriver_manager.microsoft import EdgeChromiumDriverManager
        WEBDRIVER_MANAGER_AVAILABLE = True
    except ImportError:
        WEBDRIVER_MANAGER_AVAILABLE = False
except ImportError as e:
    print(f"❌ 错误: 缺少必要的依赖包: {e}")
    print("请运行: pip install -r requirements.txt")
    sys.exit(1)

# 导入项目模块
from batch_processor import BatchLogger


class DiscogsAutoRegister:
    """Discogs自动注册器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化注册器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path or "config/register_config.yaml"
        self.config = self._load_config()
        
        # 初始化日志系统
        log_config = self.config.get('logging', {})
        self.logger = BatchLogger(
            name="discogs_register",
            log_dir="logs",
            config=log_config
        )
        
        self.driver = None
        self.wait = None
        
        self.logger.info("Discogs自动注册器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config_file = Path(self.config_path)
        
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_file}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            raise Exception(f"配置文件加载失败: {e}")
    
    def _setup_webdriver(self) -> webdriver.Remote:
        """设置WebDriver"""
        browser_config = self.config.get('browser', {})
        browser_type = browser_config.get('type', 'chrome').lower()
        
        self.logger.info(f"正在初始化{browser_type}浏览器...")
        
        try:
            if browser_type == 'chrome':
                return self._setup_chrome_driver(browser_config)
            elif browser_type == 'firefox':
                return self._setup_firefox_driver(browser_config)
            elif browser_type == 'edge':
                return self._setup_edge_driver(browser_config)
            elif browser_type == 'safari':
                return self._setup_safari_driver(browser_config)
            else:
                raise ValueError(f"不支持的浏览器类型: {browser_type}")
        except Exception as e:
            self.logger.error(f"WebDriver初始化失败: {e}")
            raise
    
    def _setup_chrome_driver(self, browser_config: Dict[str, Any]) -> webdriver.Chrome:
        """设置Chrome WebDriver"""
        options = ChromeOptions()

        # 基本配置
        if not browser_config.get('show_window', True):
            options.add_argument('--headless')

        # 窗口大小
        window_size = browser_config.get('window_size', {})
        width = window_size.get('width', 1280)
        height = window_size.get('height', 720)
        options.add_argument(f'--window-size={width},{height}')

        # User-Agent
        user_agents = browser_config.get('user_agents', [])
        if user_agents:
            user_agent = random.choice(user_agents)
            options.add_argument(f'--user-agent={user_agent}')

        # 其他Chrome选项
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--disable-extensions')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # 代理配置
        proxy_config = self.config.get('proxy', {})
        if proxy_config.get('enabled', False):
            proxy_server = proxy_config.get('server')
            if proxy_server:
                options.add_argument(f'--proxy-server={proxy_server}')

        # 创建WebDriver
        try:
            if WEBDRIVER_MANAGER_AVAILABLE:
                # 使用webdriver-manager自动管理
                service = ChromeService(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=options)
            else:
                # 手动查找ChromeDriver或使用系统Chrome
                self.logger.info("webdriver-manager不可用，尝试手动查找ChromeDriver...")

                # 尝试常见的ChromeDriver路径
                possible_paths = [
                    '/usr/local/bin/chromedriver',
                    '/usr/bin/chromedriver',
                    './chromedriver',
                    'chromedriver'
                ]

                driver_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        driver_path = path
                        break

                if driver_path:
                    service = ChromeService(driver_path)
                    driver = webdriver.Chrome(service=service, options=options)
                    self.logger.info(f"使用ChromeDriver: {driver_path}")
                else:
                    # 尝试不指定service路径（依赖PATH环境变量）
                    self.logger.info("尝试使用PATH中的chromedriver...")
                    driver = webdriver.Chrome(options=options)

        except Exception as e:
            self.logger.error(f"Chrome WebDriver初始化失败: {e}")
            raise

        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        return driver
    
    def _setup_firefox_driver(self, browser_config: Dict[str, Any]) -> webdriver.Firefox:
        """设置Firefox WebDriver"""
        options = FirefoxOptions()
        
        if not browser_config.get('show_window', True):
            options.add_argument('--headless')
        
        # User-Agent
        user_agents = browser_config.get('user_agents', [])
        if user_agents:
            user_agent = random.choice(user_agents)
            options.set_preference("general.useragent.override", user_agent)
        
        service = FirefoxService(GeckoDriverManager().install())
        return webdriver.Firefox(service=service, options=options)
    
    def _setup_edge_driver(self, browser_config: Dict[str, Any]) -> webdriver.Edge:
        """设置Edge WebDriver"""
        options = EdgeOptions()

        if not browser_config.get('show_window', True):
            options.add_argument('--headless')

        # 窗口大小
        window_size = browser_config.get('window_size', {})
        width = window_size.get('width', 1280)
        height = window_size.get('height', 720)
        options.add_argument(f'--window-size={width},{height}')

        # User-Agent
        user_agents = browser_config.get('user_agents', [])
        if user_agents:
            user_agent = random.choice(user_agents)
            options.add_argument(f'--user-agent={user_agent}')

        # 其他Edge选项
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        # 代理配置
        proxy_config = self.config.get('proxy', {})
        if proxy_config.get('enabled', False):
            proxy_server = proxy_config.get('server')
            if proxy_server:
                options.add_argument(f'--proxy-server={proxy_server}')

        # 创建WebDriver
        try:
            if WEBDRIVER_MANAGER_AVAILABLE:
                # 使用webdriver-manager自动管理
                service = EdgeService(EdgeChromiumDriverManager().install())
                driver = webdriver.Edge(service=service, options=options)
                self.logger.info("使用webdriver-manager管理的EdgeDriver")
            else:
                # 手动查找EdgeDriver或使用系统Edge
                self.logger.info("webdriver-manager不可用，尝试手动查找EdgeDriver...")

                # 尝试常见的EdgeDriver路径
                possible_paths = [
                    '/usr/local/bin/msedgedriver',
                    '/usr/bin/msedgedriver',
                    './msedgedriver',
                    'msedgedriver'
                ]

                driver_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        driver_path = path
                        break

                if driver_path:
                    service = EdgeService(driver_path)
                    driver = webdriver.Edge(service=service, options=options)
                    self.logger.info(f"使用EdgeDriver: {driver_path}")
                else:
                    # 尝试不指定service路径（依赖PATH环境变量）
                    self.logger.info("尝试使用PATH中的msedgedriver...")
                    driver = webdriver.Edge(options=options)

        except Exception as e:
            self.logger.error(f"Edge WebDriver初始化失败: {e}")
            raise

        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        return driver

    def _setup_safari_driver(self, browser_config: Dict[str, Any]) -> webdriver.Safari:
        """设置Safari WebDriver"""
        # Safari不支持很多选项，但是macOS系统自带，不需要额外驱动
        self.logger.info("使用Safari浏览器（macOS系统自带）")
        return webdriver.Safari()
    
    def _navigate_to_registration_page(self) -> bool:
        """导航到注册页面（通过登录页面点击Sign up链接）"""
        registration_config = self.config.get('registration', {})
        login_url = registration_config.get('login_url')
        backup_url = registration_config.get('url')

        if not login_url:
            self.logger.error("登录页面URL未配置")
            return False

        try:
            # 第一步：访问登录页面
            self.logger.info(f"正在访问登录页面: {login_url}")
            self.driver.get(login_url)

            # 等待登录页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            self._random_delay()

            self.logger.info("登录页面加载成功")

            # 第二步：查找并点击Sign up链接
            self.logger.info("正在查找Sign up链接...")

            # 尝试多种可能的Sign up链接选择器
            signup_selectors = [
                ("css", "a[href*='signup']"),
                ("css", "a[href*='/u/signup']"),
                ("xpath", "//a[contains(text(), 'Sign up')]"),
                ("xpath", "//a[contains(text(), 'sign up')]"),
                ("xpath", "//a[contains(text(), 'Sign Up')]"),
                ("xpath", "//a[contains(@href, 'signup')]"),
                ("xpath", "//button[contains(text(), 'Sign up')]"),
                ("xpath", "//span[contains(text(), 'Sign up')]/parent::a")
            ]

            signup_link = None
            for selector_type, selector in signup_selectors:
                try:
                    if selector_type == "xpath":
                        signup_link = self.driver.find_element(By.XPATH, selector)
                    else:
                        signup_link = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if signup_link and signup_link.is_displayed():
                        self.logger.info(f"找到Sign up链接: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"选择器 {selector} 未找到元素: {e}")
                    continue

            if not signup_link:
                self.logger.warning("未找到Sign up链接，尝试使用备用注册URL")
                if backup_url:
                    self.driver.get(backup_url)
                    self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                    self._random_delay()
                    self.logger.info("使用备用URL访问注册页面成功")
                    return True
                else:
                    self.logger.error("未配置备用注册URL")
                    return False

            # 点击Sign up链接
            self.logger.info("正在点击Sign up链接...")
            signup_link.click()

            # 等待注册页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            self._random_delay()

            # 验证是否成功跳转到注册页面
            current_url = self.driver.current_url
            if 'signup' in current_url.lower():
                self.logger.info(f"成功跳转到注册页面: {current_url}")
                return True
            else:
                self.logger.warning(f"跳转后的URL不是注册页面: {current_url}")
                return False

        except TimeoutException:
            self.logger.error("页面加载超时")
            return False
        except Exception as e:
            self.logger.error(f"导航到注册页面失败: {e}")
            return False
    
    def _random_delay(self):
        """随机延迟"""
        automation_config = self.config.get('automation', {})
        min_delay = automation_config.get('min_delay', 1)
        max_delay = automation_config.get('max_delay', 3)

        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def _type_text_slowly(self, element, text: str):
        """模拟人工输入文字"""
        automation_config = self.config.get('automation', {})
        typing_delay = automation_config.get('typing_delay', 0.1)

        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(typing_delay * 0.5, typing_delay * 1.5))

    def _fill_registration_form(self, account: Dict[str, Any]) -> bool:
        """填写注册表单"""
        selectors = self.config.get('selectors', {})

        try:
            self.logger.info(f"开始填写注册表单，用户名: {account['username']}")

            # 填写用户名
            username_selector = selectors.get('username_field', "input[name='username']")
            username_field = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, username_selector))
            )
            self._type_text_slowly(username_field, account['username'])
            self.logger.info("用户名填写完成")

            self._random_delay()

            # 填写邮箱
            email_selector = selectors.get('email_field', "input[name='email']")
            try:
                email_field = self.driver.find_element(By.CSS_SELECTOR, email_selector)
                self._type_text_slowly(email_field, account['email'])
                self.logger.info("邮箱填写完成")
            except NoSuchElementException:
                self.logger.warning("未找到邮箱字段，可能不需要填写")

            self._random_delay()

            # 填写密码
            password_selector = selectors.get('password_field', "input[name='password']")
            password_field = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, password_selector))
            )
            self._type_text_slowly(password_field, account['password'])
            self.logger.info("密码填写完成")

            self._random_delay()

            # 填写确认密码（如果存在）
            confirm_password_selector = selectors.get('confirm_password_field', "input[name='password_confirmation']")
            try:
                confirm_password_field = self.driver.find_element(By.CSS_SELECTOR, confirm_password_selector)
                self._type_text_slowly(confirm_password_field, account['password'])
                self.logger.info("确认密码填写完成")
            except NoSuchElementException:
                self.logger.info("未找到确认密码字段")

            self._random_delay()

            self.logger.info("注册表单填写完成")
            return True

        except TimeoutException:
            self.logger.error("表单字段加载超时")
            return False
        except Exception as e:
            self.logger.error(f"填写注册表单失败: {e}")
            return False

    def _check_for_captcha(self) -> bool:
        """检查是否存在验证码"""
        selectors = self.config.get('selectors', {})
        captcha_selector = selectors.get('captcha_container', ".captcha, .recaptcha, [id*='captcha'], [class*='captcha']")

        try:
            captcha_elements = self.driver.find_elements(By.CSS_SELECTOR, captcha_selector)

            # 检查是否有可见的验证码元素
            for element in captcha_elements:
                if element.is_displayed():
                    self.logger.warning("检测到验证码")
                    return True

            return False

        except Exception as e:
            self.logger.error(f"验证码检查失败: {e}")
            return False

    def _handle_captcha(self) -> bool:
        """处理验证码"""
        captcha_config = self.config.get('automation', {}).get('captcha_handling', {})

        if not captcha_config.get('enabled', True):
            self.logger.warning("验证码处理已禁用")
            return False

        wait_time = captcha_config.get('wait_time', 60)
        manual_intervention = captcha_config.get('manual_intervention', True)

        if manual_intervention:
            self.logger.info(f"检测到验证码，请在{wait_time}秒内手动完成验证码")
            print(f"\n⚠️  检测到验证码！")
            print(f"请在浏览器中手动完成验证码，然后按回车键继续...")
            print(f"等待时间: {wait_time}秒")

            try:
                # 等待用户输入或超时
                import select
                import sys

                if sys.platform == 'win32':
                    # Windows系统
                    input("按回车键继续...")
                else:
                    # Unix系统
                    ready, _, _ = select.select([sys.stdin], [], [], wait_time)
                    if ready:
                        input()
                    else:
                        self.logger.warning("验证码处理超时")
                        return False

                self.logger.info("用户确认验证码已完成")
                return True

            except Exception as e:
                self.logger.error(f"验证码处理失败: {e}")
                return False
        else:
            self.logger.warning("需要手动处理验证码，但已禁用人工干预")
            return False

    def _submit_registration_form(self) -> bool:
        """提交注册表单"""
        selectors = self.config.get('selectors', {})
        submit_selector = selectors.get('submit_button', "button[type='submit'], input[type='submit']")

        try:
            self.logger.info("正在提交注册表单...")

            # 查找提交按钮
            submit_button = self.wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, submit_selector))
            )

            # 随机延迟后点击
            self._random_delay()
            submit_button.click()

            self.logger.info("注册表单已提交")
            return True

        except TimeoutException:
            self.logger.error("未找到提交按钮或按钮不可点击")
            return False
        except Exception as e:
            self.logger.error(f"提交注册表单失败: {e}")
            return False

    def _verify_registration_result(self) -> bool:
        """验证注册结果"""
        try:
            self.logger.info("正在验证注册结果...")

            # 等待页面响应
            time.sleep(5)

            # 获取当前URL和页面内容
            current_url = self.driver.current_url
            page_source = self.driver.page_source.lower()
            page_title = self.driver.title.lower()

            self.logger.info(f"注册后URL: {current_url}")
            self.logger.info(f"注册后页面标题: {page_title}")

            # 首先检查是否有明显的错误消息
            error_selectors = [
                ".error", ".alert-danger", "[class*='error']",
                ".warning", ".alert-warning", "[class*='warning']",
                "[class*='ulp-validator-error']", ".ulp-error"
            ]

            for selector in error_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.text.strip():
                            error_text = element.text.strip()
                            self.logger.error(f"注册失败，错误信息: {error_text}")
                            return False
                except:
                    continue

            # 检查是否仍在注册页面（表示注册失败）
            if 'signup' in current_url.lower():
                # 如果仍在注册页面，检查页面内容是否显示"Sign up to Discogs"
                if 'sign up to discogs' in page_source:
                    self.logger.warning("仍在注册页面，注册失败")
                    return False

            # 检查是否跳转到了登录页面（可能表示需要验证邮箱）
            if 'login' in current_url.lower():
                # 如果跳转到登录页面，可能是注册成功但需要邮箱验证
                if 'log in to discogs' in page_source:
                    self.logger.info("跳转到登录页面，注册成功但需要邮箱验证")
                    return True

            # 检查是否有邮箱验证提示
            verification_indicators = [
                'verify your email', 'check your email', 'verification email',
                'confirm your email', 'email verification', 'activate your account',
                'please check your email', 'verification link'
            ]

            for indicator in verification_indicators:
                if indicator in page_source:
                    self.logger.info(f"检测到邮箱验证指示器: {indicator}")
                    return True

            # 检查是否跳转到了主页或其他成功页面
            success_urls = ['discogs.com', 'dashboard', 'profile', 'account']
            for url_part in success_urls:
                if url_part in current_url.lower() and 'login' not in current_url.lower():
                    self.logger.info(f"跳转到成功页面: {current_url}")
                    return True

            # 如果URL发生了变化且不是错误页面，认为可能成功
            registration_url = self.config['registration']['url']
            if current_url != registration_url:
                if 'error' not in current_url.lower() and 'signup' not in current_url.lower():
                    self.logger.info("URL已跳转且非错误页面，可能注册成功")
                    return True

            self.logger.warning("无法确定注册结果，默认为失败")
            return False

        except Exception as e:
            self.logger.error(f"验证注册结果失败: {e}")
            return False

    def _test_login(self, account: Dict[str, Any]) -> bool:
        """测试登录以验证注册是否成功"""
        try:
            username = account.get('username', '')
            password = account.get('password', '')

            self.logger.info(f"开始登录测试账号: {username}")

            # 获取登录页面URL
            registration_config = self.config.get('registration', {})
            login_url = registration_config.get('login_url')

            if not login_url:
                self.logger.error("登录页面URL未配置")
                return False

            # 访问登录页面
            self.logger.info(f"正在访问登录页面: {login_url}")
            self.driver.get(login_url)

            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            self._random_delay()

            # 查找用户名字段
            username_selectors = [
                "input[name='username']",
                "input[id='username']",
                "input[type='text']",
                "input[placeholder*='username']",
                "input[placeholder*='email']"
            ]

            username_field = None
            for selector in username_selectors:
                try:
                    username_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if username_field.is_displayed():
                        break
                except:
                    continue

            if not username_field:
                self.logger.error("未找到用户名输入字段")
                return False

            # 查找密码字段
            password_selectors = [
                "input[name='password']",
                "input[id='password']",
                "input[type='password']"
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if password_field.is_displayed():
                        break
                except:
                    continue

            if not password_field:
                self.logger.error("未找到密码输入字段")
                return False

            # 填写登录信息
            self.logger.info("正在填写登录信息...")
            username_field.clear()
            self._type_like_human(username_field, username)
            self._random_delay(0.5, 1.5)

            password_field.clear()
            self._type_like_human(password_field, password)
            self._random_delay(0.5, 1.5)

            # 查找并点击登录按钮
            login_button_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('Continue')",
                "button:contains('Log in')",
                "button:contains('Login')",
                "//button[contains(text(), 'Continue')]",
                "//button[contains(text(), 'Log in')]"
            ]

            login_button = None
            for selector in login_button_selectors:
                try:
                    if selector.startswith("//"):
                        login_button = self.driver.find_element(By.XPATH, selector)
                    else:
                        login_button = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if login_button and login_button.is_displayed():
                        break
                except:
                    continue

            if not login_button:
                self.logger.error("未找到登录按钮")
                return False

            # 点击登录按钮
            self.logger.info("正在提交登录表单...")
            login_button.click()

            # 等待页面响应
            time.sleep(5)

            # 检查登录结果
            current_url = self.driver.current_url
            page_source = self.driver.page_source.lower()

            self.logger.info(f"登录后URL: {current_url}")

            # 检查是否有错误消息
            error_indicators = [
                'unknown username', 'invalid username', 'incorrect password',
                'login failed', 'authentication failed', 'wrong credentials',
                'user not found', 'invalid credentials'
            ]

            for indicator in error_indicators:
                if indicator in page_source:
                    self.logger.error(f"登录失败: {indicator}")
                    return False

            # 检查是否仍在登录页面
            if 'login' in current_url.lower() and 'log in to discogs' in page_source:
                self.logger.error("仍在登录页面，登录失败")
                return False

            # 检查是否成功登录（URL变化或页面内容变化）
            success_indicators = [
                'dashboard', 'profile', 'account', 'collection', 'wantlist'
            ]

            for indicator in success_indicators:
                if indicator in current_url.lower():
                    self.logger.info(f"登录成功，跳转到: {current_url}")
                    return True

            # 如果URL发生变化且不是错误页面，认为登录成功
            if current_url != login_url and 'error' not in current_url.lower():
                self.logger.info(f"登录成功，URL已变化: {current_url}")
                return True

            self.logger.warning("无法确定登录结果")
            return False

        except Exception as e:
            self.logger.error(f"登录测试失败: {e}")
            return False

    def register_single_account(self, account: Dict[str, Any]) -> bool:
        """注册单个账号"""
        if not account.get('enabled', True):
            self.logger.info(f"账号 {account.get('username', 'Unknown')} 已禁用，跳过注册")
            return True

        username = account.get('username', 'Unknown')
        self.logger.info(f"开始注册账号: {username}")

        try:
            # 初始化WebDriver
            self.driver = self._setup_webdriver()
            browser_config = self.config.get('browser', {})

            # 设置等待
            implicit_wait = browser_config.get('implicit_wait', 10)
            page_load_timeout = browser_config.get('page_load_timeout', 30)

            self.driver.implicitly_wait(implicit_wait)
            self.driver.set_page_load_timeout(page_load_timeout)

            self.wait = WebDriverWait(self.driver, implicit_wait)

            # 导航到注册页面
            if not self._navigate_to_registration_page():
                return False

            # 填写注册表单
            if not self._fill_registration_form(account):
                return False

            # 检查验证码
            if self._check_for_captcha():
                if not self._handle_captcha():
                    self.logger.error("验证码处理失败")
                    return False

            # 提交表单
            if not self._submit_registration_form():
                return False

            # 验证注册结果
            if self._verify_registration_result():
                self.logger.info(f"账号 {username} 初步注册成功，开始登录测试验证...")

                # 进行登录测试验证
                if self._test_login(account):
                    self.logger.info(f"账号 {username} 注册成功并通过登录验证！")
                    return True
                else:
                    self.logger.error(f"账号 {username} 注册后登录测试失败，注册可能未成功")
                    return False
            else:
                self.logger.error(f"账号 {username} 注册失败")
                return False

        except Exception as e:
            self.logger.error(f"注册账号 {username} 时发生异常: {e}", exc_info=True)
            return False
        finally:
            # 清理WebDriver
            if self.driver:
                try:
                    self.driver.quit()
                except Exception:
                    pass
                self.driver = None
                self.wait = None

    def register_multiple_accounts(self) -> Dict[str, bool]:
        """批量注册多个账号"""
        accounts = self.config.get('accounts', [])

        if not accounts:
            self.logger.warning("未配置任何账号信息")
            return {}

        results = {}
        total_accounts = len([acc for acc in accounts if acc.get('enabled', True)])

        self.logger.info(f"开始批量注册，共 {total_accounts} 个账号")

        for i, account in enumerate(accounts, 1):
            if not account.get('enabled', True):
                continue

            username = account.get('username', f'Account_{i}')
            self.logger.info(f"正在处理第 {i}/{total_accounts} 个账号: {username}")

            # 重试机制
            registration_config = self.config.get('registration', {})
            retry_attempts = registration_config.get('retry_attempts', 3)
            retry_delay = registration_config.get('retry_delay_seconds', 5)

            success = False
            for attempt in range(retry_attempts):
                if attempt > 0:
                    self.logger.info(f"第 {attempt + 1} 次重试注册账号: {username}")
                    time.sleep(retry_delay)

                try:
                    success = self.register_single_account(account)
                    if success:
                        break
                except Exception as e:
                    self.logger.error(f"注册账号 {username} 第 {attempt + 1} 次尝试失败: {e}")
                    if attempt == retry_attempts - 1:
                        self.logger.error(f"账号 {username} 所有重试均失败")

            results[username] = success

            # 账号间延迟
            if i < total_accounts:
                delay = random.uniform(5, 15)
                self.logger.info(f"等待 {delay:.1f} 秒后处理下一个账号...")
                time.sleep(delay)

        return results

    def run(self) -> bool:
        """运行注册程序"""
        start_time = datetime.now()
        self.logger.info("=" * 50)
        self.logger.info("Discogs 自动注册程序启动")
        self.logger.info("=" * 50)

        try:
            # 记录配置信息
            self.logger.log_config_info("注册", self.config)

            # 执行批量注册
            results = self.register_multiple_accounts()

            # 统计结果
            total_accounts = len(results)
            successful_accounts = sum(1 for success in results.values() if success)
            failed_accounts = total_accounts - successful_accounts

            # 记录结果
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            self.logger.info("=" * 50)
            self.logger.info("注册任务完成")
            self.logger.info(f"总账号数: {total_accounts}")
            self.logger.info(f"成功注册: {successful_accounts}")
            self.logger.info(f"注册失败: {failed_accounts}")
            self.logger.info(f"总耗时: {duration:.2f} 秒")
            self.logger.info("=" * 50)

            # 详细结果
            for username, success in results.items():
                status = "✅ 成功" if success else "❌ 失败"
                self.logger.info(f"{username}: {status}")

            return failed_accounts == 0

        except KeyboardInterrupt:
            self.logger.warning("用户中断了注册程序")
            return False
        except Exception as e:
            self.logger.error(f"注册程序执行失败: {e}", exc_info=True)
            return False


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Discogs 自动注册工具')
    parser.add_argument(
        '--config', '-c',
        default='config/register_config.yaml',
        help='配置文件路径 (默认: config/register_config.yaml)'
    )
    parser.add_argument(
        '--account', '-a',
        help='仅注册指定用户名的账号'
    )
    parser.add_argument(
        '--dry-run', '-d',
        action='store_true',
        help='试运行模式，仅验证配置不执行注册'
    )

    args = parser.parse_args()

    try:
        # 创建注册器
        register = DiscogsAutoRegister(args.config)

        if args.dry_run:
            print("🔍 试运行模式 - 验证配置...")
            accounts = register.config.get('accounts', [])
            enabled_accounts = [acc for acc in accounts if acc.get('enabled', True)]

            print(f"✅ 配置文件加载成功: {args.config}")
            print(f"📊 配置的账号数量: {len(accounts)}")
            print(f"🎯 启用的账号数量: {len(enabled_accounts)}")

            for i, account in enumerate(enabled_accounts, 1):
                username = account.get('username', f'Account_{i}')
                email = account.get('email', 'N/A')
                print(f"   {i}. {username} ({email})")

            print("✅ 配置验证完成")
            return 0

        if args.account:
            # 注册指定账号
            accounts = register.config.get('accounts', [])
            target_account = None

            for account in accounts:
                if account.get('username') == args.account and account.get('enabled', True):
                    target_account = account
                    break

            if not target_account:
                print(f"❌ 错误: 未找到启用的账号 '{args.account}'")
                return 1

            print(f"🎯 注册指定账号: {args.account}")
            success = register.register_single_account(target_account)
            return 0 if success else 1
        else:
            # 批量注册
            success = register.run()
            return 0 if success else 1

    except FileNotFoundError as e:
        print(f"❌ 错误: {e}")
        return 1
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
