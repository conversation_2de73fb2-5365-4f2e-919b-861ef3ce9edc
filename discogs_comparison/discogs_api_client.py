#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API 客户端 - 使用python3-discogs-client库

功能：
1. 使用官方python3-discogs-client库获取Discogs release数据
2. 支持多账号轮换，避免API限制
3. 智能错误处理和重试机制
4. 批量处理多个release ID
5. 数据格式与现有流程完全兼容

特性：
- 多账号自动轮换
- 智能重试机制（区分不同错误类型）
- 完整的错误处理和日志记录
- 批量数据获取和处理
- 与现有数据库结构兼容
- 进度跟踪和统计报告

作者：AI Assistant
创建时间：2025-08-03
"""

import os
import sys
import time
import json
import logging
import random
import re
from datetime import datetime, timezone
from typing import List, Dict, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import signal

# 第三方库
try:
    import discogs_client
    from discogs_client.exceptions import HTTPError
except ImportError:
    print("❌ 错误：未安装python3-discogs-client库")
    print("请运行：pip install python3-discogs-client")
    sys.exit(1)

# 导入现有的枚举类（如果存在）
try:
    from release.enums import Permissions, Status, Source
except ImportError:
    # 如果导入失败，定义本地枚举
    class Permissions:
        ALL_VISIBLE = 1
    class Status:
        ACTIVE = 1
    class Source:
        DISCOGS = 1

# 配置参数
DEFAULT_CONFIG = {
    'api': {
        'rate_limit': 1.0,  # 1秒1次请求
        'timeout': 30,      # 请求超时时间
        'max_retries': 3,   # 最大重试次数
        'retry_delay': 2.0, # 重试延迟
    },
    'batch': {
        'size': 100,        # 批量处理大小
        'progress_interval': 10,  # 进度报告间隔
    },
    'logging': {
        'level': 'INFO',
        'format': '%(asctime)s - %(levelname)s - %(message)s',
        'file': 'discogs_api_client.log'
    }
}

# Discogs账号配置
DISCOGS_ACCOUNTS = [
    {
        'email': '<EMAIL>',
        'token': 'hhKxjHkkcVYSbsWEscUlBekYWLafpUbvzbUGLtPz',
        'user_agent': 'DiscogsAPIClient/1.0 +https://example.com/contact'
    },
    {
        'email': '<EMAIL>', 
        'token': 'zXwURCsYotIjXROUfIngknavKuMWCjcmODCXuEJs',
        'user_agent': 'DiscogsAPIClient/1.0 +https://example.com/contact'
    }
]

# 全局变量
should_stop = False

def signal_handler(signum, frame):
    """信号处理器，用于优雅停止"""
    global should_stop
    should_stop = True
    print("\n🛑 接收到停止信号，正在安全退出...")

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format=DEFAULT_CONFIG['logging']['format'],
    handlers=[
        logging.FileHandler(DEFAULT_CONFIG['logging']['file'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ErrorType(Enum):
    """错误类型枚举"""
    NOT_FOUND = "404"
    RATE_LIMITED = "429" 
    NETWORK_ERROR = "network"
    AUTH_ERROR = "auth"
    UNKNOWN_ERROR = "unknown"

@dataclass
class FetchResult:
    """获取结果数据类"""
    success: bool
    data: Optional[Dict] = None
    error_type: Optional[ErrorType] = None
    error_message: Optional[str] = None
    release_id: Optional[int] = None

@dataclass
class BatchStats:
    """批量处理统计数据类"""
    total: int = 0
    successful: int = 0
    not_found: int = 0
    rate_limited: int = 0
    errors: int = 0
    start_time: float = 0
    
    def __post_init__(self):
        if self.start_time == 0:
            self.start_time = time.time()
    
    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time
    
    @property
    def success_rate(self) -> float:
        return (self.successful / self.total * 100) if self.total > 0 else 0
    
    @property
    def processing_rate(self) -> float:
        return (self.total / self.elapsed_time) if self.elapsed_time > 0 else 0

class DiscogsClientManager:
    """Discogs客户端管理器 - 处理多账号轮换"""
    
    def __init__(self, accounts: List[Dict], test_connection: bool = True):
        """
        初始化客户端管理器

        Args:
            accounts: 账号配置列表
            test_connection: 是否测试连接（默认True）
        """
        self.accounts = accounts
        self.current_account_index = 0
        self.clients = {}
        self.last_request_times = {}
        self.test_connection = test_connection
        self._initialize_clients()
    
    def _initialize_clients(self):
        """初始化所有客户端"""
        for i, account in enumerate(self.accounts):
            try:
                client = discogs_client.Client(
                    user_agent=account['user_agent'],
                    token=account['token']
                )

                # 添加延迟避免429错误
                if i > 0:
                    time.sleep(2.0)  # 账号间延迟2秒

                # 测试连接（如果启用）
                if self.test_connection:
                    max_init_retries = 3
                    for attempt in range(max_init_retries):
                        try:
                            client.identity()
                            break
                        except HTTPError as e:
                            if e.status_code == 429 and attempt < max_init_retries - 1:
                                wait_time = 60 * (attempt + 1)  # 递增等待时间
                                logger.warning(f"⏳ 账号 {i+1} 遇到429错误，等待 {wait_time} 秒后重试...")
                                time.sleep(wait_time)
                            else:
                                raise
                else:
                    logger.info(f"⚠️ 跳过账号 {i+1} 的连接测试")

                self.clients[i] = client
                self.last_request_times[i] = time.time()
                logger.info(f"✅ 账号 {i+1} ({account['email']}) 初始化成功")

            except Exception as e:
                logger.error(f"❌ 账号 {i+1} ({account['email']}) 初始化失败: {e}")
                # 如果是429错误，建议等待
                if "429" in str(e):
                    logger.warning(f"💡 建议：账号 {i+1} 遇到频率限制，请稍后再试")

        if not self.clients:
            logger.error("❌ 没有可用的Discogs账号")
            logger.error("💡 可能的原因：")
            logger.error("   1. Token无效或过期")
            logger.error("   2. API请求过于频繁（429错误）")
            logger.error("   3. 网络连接问题")
            logger.error("💡 建议：等待几分钟后重试，或检查Token是否有效")
            raise Exception("❌ 没有可用的Discogs账号")

        logger.info(f"📊 成功初始化 {len(self.clients)} 个Discogs客户端")
    
    def get_client(self) -> Tuple[discogs_client.Client, int]:
        """
        获取当前可用的客户端
        
        Returns:
            Tuple[客户端实例, 账号索引]
        """
        # 轮换到下一个账号
        available_accounts = list(self.clients.keys())
        if not available_accounts:
            raise Exception("❌ 没有可用的客户端")
        
        self.current_account_index = (self.current_account_index + 1) % len(available_accounts)
        account_index = available_accounts[self.current_account_index]
        
        return self.clients[account_index], account_index
    
    def wait_for_rate_limit(self, account_index: int):
        """等待满足API频率限制"""
        elapsed = time.time() - self.last_request_times.get(account_index, 0)
        if elapsed < DEFAULT_CONFIG['api']['rate_limit']:
            time.sleep(DEFAULT_CONFIG['api']['rate_limit'] - elapsed)
        self.last_request_times[account_index] = time.time()
    
    def get_account_info(self, account_index: int) -> str:
        """获取账号信息"""
        if account_index < len(self.accounts):
            return self.accounts[account_index]['email']
        return f"账号{account_index + 1}"

class ErrorHandler:
    """错误处理器 - 智能重试和错误分类"""

    def __init__(self, max_retries: int = 3, retry_delay: float = 2.0):
        """
        初始化错误处理器

        Args:
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.error_stats = {
            ErrorType.NOT_FOUND: 0,
            ErrorType.RATE_LIMITED: 0,
            ErrorType.NETWORK_ERROR: 0,
            ErrorType.AUTH_ERROR: 0,
            ErrorType.UNKNOWN_ERROR: 0
        }

    def classify_error(self, exception: Exception) -> ErrorType:
        """
        分类错误类型

        Args:
            exception: 异常对象

        Returns:
            ErrorType: 错误类型
        """
        if isinstance(exception, HTTPError):
            if exception.status_code == 404:
                return ErrorType.NOT_FOUND
            if exception.status_code == 429:
                return ErrorType.RATE_LIMITED
            if exception.status_code in [401, 403]:
                return ErrorType.AUTH_ERROR

        # 检查网络相关错误
        error_msg = str(exception).lower()
        if any(keyword in error_msg for keyword in ['timeout', 'connection', 'network', 'dns']):
            return ErrorType.NETWORK_ERROR

        return ErrorType.UNKNOWN_ERROR

    def should_retry(self, error_type: ErrorType, attempt: int) -> bool:
        """
        判断是否应该重试

        Args:
            error_type: 错误类型
            attempt: 当前尝试次数

        Returns:
            bool: 是否应该重试
        """
        if attempt >= self.max_retries:
            return False

        # 404错误不重试
        if error_type == ErrorType.NOT_FOUND:
            return False

        # 认证错误不重试
        if error_type == ErrorType.AUTH_ERROR:
            return False

        # 其他错误可以重试
        return True

    def get_retry_delay(self, error_type: ErrorType, attempt: int) -> float:
        """
        获取重试延迟时间

        Args:
            error_type: 错误类型
            attempt: 当前尝试次数

        Returns:
            float: 延迟时间（秒）
        """
        base_delay = self.retry_delay

        # 429错误使用更长的延迟
        if error_type == ErrorType.RATE_LIMITED:
            base_delay = 60.0  # 1分钟

        # 指数退避
        return base_delay * (2 ** (attempt - 1))

    def record_error(self, error_type: ErrorType):
        """记录错误统计"""
        self.error_stats[error_type] += 1

    def get_error_stats(self) -> Dict:
        """获取错误统计"""
        return dict(self.error_stats)

class DataConverter:
    """数据转换器 - 将API响应转换为数据库兼容格式"""

    @staticmethod
    def safe_string_value(value, default=''):
        """安全地处理字符串值"""
        if value is None:
            return default

        str_value = str(value).strip()
        if not str_value:
            return default

        # 移除控制字符
        str_value = re.sub(r'[\x00-\x1F\x7F]', '', str_value)
        str_value = str_value.replace('\r\n', ' ').replace('\r', ' ').replace('\n', ' ')
        str_value = str_value.replace('\t', ' ')
        str_value = re.sub(r'\s+', ' ', str_value)

        return str_value.strip() or default

    @staticmethod
    def safe_integer_value(value, default=None):
        """安全地处理整数值"""
        if value is None:
            return default

        try:
            return int(value)
        except (ValueError, TypeError):
            return default

    @staticmethod
    def convert_artists(artists_data: List) -> List[Dict]:
        """转换艺术家数据"""
        artists = []
        for artist in artists_data:
            artist_doc = {
                'artist_id': DataConverter.safe_integer_value(artist.id),
                'name': DataConverter.safe_string_value(artist.name, ''),
                'role': DataConverter.safe_string_value(getattr(artist, 'role', ''), 'Primary')
            }

            # 处理anv字段
            anv_value = DataConverter.safe_string_value(getattr(artist, 'anv', ''))
            if anv_value:
                artist_doc['anv'] = anv_value

            artists.append(artist_doc)

        return artists

    @staticmethod
    def convert_labels(labels_data: List) -> List[Dict]:
        """转换标签数据"""
        labels = []
        for label in labels_data:
            labels.append({
                'name': DataConverter.safe_string_value(getattr(label, 'name', ''), ''),
                'catno': DataConverter.safe_string_value(getattr(label, 'catno', ''), ''),
                'id': str(getattr(label, 'id', ''))
            })
        return labels

    @staticmethod
    def convert_formats(formats_data: List) -> List[Dict]:
        """转换格式数据"""
        formats = []
        for format_item in formats_data:
            format_doc = {
                'name': DataConverter.safe_string_value(getattr(format_item, 'name', ''), ''),
                'qty': str(getattr(format_item, 'qty', '')),
                'text': DataConverter.safe_string_value(getattr(format_item, 'text', ''), ''),
                'descriptions': getattr(format_item, 'descriptions', [])
            }
            formats.append(format_doc)
        return formats

    @staticmethod
    def convert_identifiers(identifiers_data: List) -> List[Dict]:
        """转换标识符数据"""
        identifiers = []
        for identifier in identifiers_data:
            identifiers.append({
                'type': DataConverter.safe_string_value(getattr(identifier, 'type', ''), ''),
                'value': DataConverter.safe_string_value(getattr(identifier, 'value', ''), ''),
                'description': DataConverter.safe_string_value(getattr(identifier, 'description', ''), '')
            })
        return identifiers

    @staticmethod
    def convert_tracklist(tracklist_data: List) -> List[Dict]:
        """转换曲目列表数据"""
        tracklist = []
        for track in tracklist_data:
            track_doc = {
                'position': DataConverter.safe_string_value(getattr(track, 'position', ''), ''),
                'title': DataConverter.safe_string_value(getattr(track, 'title', ''), '')
            }

            duration = getattr(track, 'duration', None)
            if duration:
                track_doc['duration'] = DataConverter.safe_string_value(duration, '')

            tracklist.append(track_doc)
        return tracklist

    @staticmethod
    def convert_release_to_document(release, db_images: List = None) -> Dict:
        """
        将Discogs release对象转换为数据库文档格式

        Args:
            release: Discogs release对象
            db_images: 从数据库获取的图片数据

        Returns:
            Dict: 数据库兼容的文档格式
        """
        try:
            # 基础字段
            doc = {
                'id': DataConverter.safe_integer_value(release.id),
                'title': DataConverter.safe_string_value(release.title, ''),
                'country': DataConverter.safe_string_value(getattr(release, 'country', ''), ''),
                'master_id': DataConverter.safe_integer_value(getattr(release, 'master', {}).get('id') if hasattr(release, 'master') and release.master else None),
                'year': DataConverter.safe_integer_value(getattr(release, 'year', None)),
                'notes': DataConverter.safe_string_value(getattr(release, 'notes', ''), ''),
                'discogs_status': DataConverter.safe_string_value(getattr(release, 'status', ''), 'unknown'),

                # 系统字段
                'images_permissions': Permissions.ALL_VISIBLE,
                'delete_status': Status.ACTIVE,
                'permissions': Permissions.ALL_VISIBLE,
                'source': Source.DISCOGS,
                'created_at': datetime.now(timezone.utc),
                'updated_at': datetime.now(timezone.utc)
            }

            # 处理艺术家
            doc['artists'] = DataConverter.convert_artists(getattr(release, 'artists', []))
            doc['extra_artists'] = DataConverter.convert_artists(getattr(release, 'extraartists', []))

            # 处理标签和格式
            doc['labels'] = DataConverter.convert_labels(getattr(release, 'labels', []))
            doc['formats'] = DataConverter.convert_formats(getattr(release, 'formats', []))

            # 处理分类
            doc['genres'] = getattr(release, 'genres', [])
            doc['styles'] = getattr(release, 'styles', [])

            # 处理标识符和曲目
            doc['identifiers'] = DataConverter.convert_identifiers(getattr(release, 'identifiers', []))
            doc['tracklist'] = DataConverter.convert_tracklist(getattr(release, 'tracklist', []))

            # 处理图片（优先使用数据库中的图片）
            doc['images'] = db_images if db_images is not None else []

            # 处理公司信息（如果存在）
            companies = []
            if hasattr(release, 'companies') and release.companies:
                for company in release.companies:
                    company_doc = {
                        'name': DataConverter.safe_string_value(getattr(company, 'name', ''), ''),
                    }
                    if hasattr(company, 'id'):
                        company_doc['id'] = str(company.id)
                    if hasattr(company, 'entity_type'):
                        company_doc['entity_type'] = DataConverter.safe_string_value(company.entity_type, '')
                    if hasattr(company, 'entity_type_name'):
                        company_doc['entity_type_name'] = DataConverter.safe_string_value(company.entity_type_name, '')
                    companies.append(company_doc)
            doc['companies'] = companies

            return doc

        except Exception as e:
            logger.error(f"❌ 数据转换失败: {e}")
            return None

class ReleaseDataFetcher:
    """Release数据获取器 - 核心获取逻辑"""

    def __init__(self, accounts: List[Dict] = None):
        """
        初始化数据获取器

        Args:
            accounts: Discogs账号配置列表
        """
        self.accounts = accounts or DISCOGS_ACCOUNTS
        self.client_manager = DiscogsClientManager(self.accounts)
        self.error_handler = ErrorHandler()
        self.stats = BatchStats()

    def fetch_single_release(self, release_id: int, db_images: List = None) -> FetchResult:
        """
        获取单个release数据

        Args:
            release_id: Release ID
            db_images: 从数据库获取的图片数据

        Returns:
            FetchResult: 获取结果
        """
        for attempt in range(1, self.error_handler.max_retries + 1):
            if should_stop:
                return FetchResult(
                    success=False,
                    error_type=ErrorType.UNKNOWN_ERROR,
                    error_message="用户中断",
                    release_id=release_id
                )

            try:
                # 获取客户端
                client, account_index = self.client_manager.get_client()

                # 等待频率限制
                self.client_manager.wait_for_rate_limit(account_index)

                # 获取release数据
                release = client.release(release_id)

                # 转换为文档格式
                doc = DataConverter.convert_release_to_document(release, db_images)

                if doc is None:
                    return FetchResult(
                        success=False,
                        error_type=ErrorType.UNKNOWN_ERROR,
                        error_message="数据转换失败",
                        release_id=release_id
                    )

                logger.debug(f"✅ 成功获取 Release {release_id} (账号: {self.client_manager.get_account_info(account_index)})")

                return FetchResult(
                    success=True,
                    data=doc,
                    release_id=release_id
                )

            except Exception as e:
                error_type = self.error_handler.classify_error(e)
                self.error_handler.record_error(error_type)

                logger.warning(f"⚠️ Release {release_id} 获取失败 (尝试 {attempt}/{self.error_handler.max_retries}): {e}")

                # 判断是否应该重试
                if not self.error_handler.should_retry(error_type, attempt):
                    return FetchResult(
                        success=False,
                        error_type=error_type,
                        error_message=str(e),
                        release_id=release_id
                    )

                # 等待重试
                if attempt < self.error_handler.max_retries:
                    delay = self.error_handler.get_retry_delay(error_type, attempt)
                    logger.info(f"⏳ 等待 {delay:.1f} 秒后重试...")
                    time.sleep(delay)

        # 所有重试都失败了
        return FetchResult(
            success=False,
            error_type=ErrorType.UNKNOWN_ERROR,
            error_message="所有重试都失败",
            release_id=release_id
        )

    def fetch_batch_releases(self, release_ids: List[int], progress_callback=None) -> List[FetchResult]:
        """
        批量获取release数据

        Args:
            release_ids: Release ID列表
            progress_callback: 进度回调函数

        Returns:
            List[FetchResult]: 获取结果列表
        """
        results = []
        self.stats = BatchStats()
        self.stats.total = len(release_ids)

        logger.info(f"🚀 开始批量获取 {len(release_ids)} 个Release数据")

        for i, release_id in enumerate(release_ids):
            if should_stop:
                logger.info("🛑 收到停止信号，终止批量处理")
                break

            result = self.fetch_single_release(release_id)
            results.append(result)

            # 更新统计
            if result.success:
                self.stats.successful += 1
            elif result.error_type == ErrorType.NOT_FOUND:
                self.stats.not_found += 1
            elif result.error_type == ErrorType.RATE_LIMITED:
                self.stats.rate_limited += 1
            else:
                self.stats.errors += 1

            # 进度报告
            if (i + 1) % DEFAULT_CONFIG['batch']['progress_interval'] == 0:
                self._report_progress(i + 1, len(release_ids))

            # 调用进度回调
            if progress_callback:
                progress_callback(i + 1, len(release_ids), result)

        # 最终报告
        self._report_final_stats()

        return results

    def _report_progress(self, current: int, total: int):
        """报告进度"""
        logger.info(f"📊 进度: {current}/{total} "
                   f"(成功: {self.stats.successful}, "
                   f"404: {self.stats.not_found}, "
                   f"429: {self.stats.rate_limited}, "
                   f"错误: {self.stats.errors}, "
                   f"速度: {self.stats.processing_rate:.2f}/秒)")

    def _report_final_stats(self):
        """报告最终统计"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 批量获取完成 - 最终统计")
        logger.info("=" * 60)
        logger.info(f"总处理数量: {self.stats.total}")
        logger.info(f"成功获取: {self.stats.successful}")
        logger.info(f"404未找到: {self.stats.not_found}")
        logger.info(f"429限制: {self.stats.rate_limited}")
        logger.info(f"其他错误: {self.stats.errors}")
        logger.info(f"成功率: {self.stats.success_rate:.2f}%")
        logger.info(f"处理速度: {self.stats.processing_rate:.2f} 条/秒")
        logger.info(f"总耗时: {self.stats.elapsed_time:.2f} 秒")

        # 错误统计
        error_stats = self.error_handler.get_error_stats()
        if any(error_stats.values()):
            logger.info("\n📋 错误类型统计:")
            for error_type, count in error_stats.items():
                if count > 0:
                    logger.info(f"  {error_type.value}: {count}")

        logger.info("=" * 60)

    def get_stats(self) -> BatchStats:
        """获取统计信息"""
        return self.stats

def save_results_to_json(results: List[FetchResult], filename: str):
    """
    保存结果到JSON文件

    Args:
        results: 获取结果列表
        filename: 输出文件名
    """
    output_data = {
        'metadata': {
            'total_count': len(results),
            'successful_count': sum(1 for r in results if r.success),
            'error_count': sum(1 for r in results if not r.success),
            'generated_at': datetime.now().isoformat()
        },
        'releases': []
    }

    for result in results:
        if result.success and result.data:
            # 转换datetime对象为字符串
            release_data = result.data.copy()
            for key, value in release_data.items():
                if isinstance(value, datetime):
                    release_data[key] = value.isoformat()
            output_data['releases'].append(release_data)

    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        logger.info(f"✅ 结果已保存到: {filename}")
    except Exception as e:
        logger.error(f"❌ 保存结果失败: {e}")

def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(
        description='Discogs API 客户端 - 获取Release数据',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --id 123456                    # 获取单个release
  %(prog)s --ids 123456,789012,345678     # 获取多个release
  %(prog)s --file release_ids.txt         # 从文件读取ID列表
  %(prog)s --id 123456 --output result.json  # 指定输出文件
        """
    )

    # 输入参数
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--id', type=int, help='单个Release ID')
    input_group.add_argument('--ids', type=str, help='多个Release ID，用逗号分隔')
    input_group.add_argument('--file', type=str, help='包含Release ID的文件路径（每行一个ID）')

    # 输出参数
    parser.add_argument('--output', type=str, help='输出JSON文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')

    # 配置参数
    parser.add_argument('--rate-limit', type=float, default=1.0, help='API请求频率限制（秒）')
    parser.add_argument('--max-retries', type=int, default=3, help='最大重试次数')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 更新配置
    DEFAULT_CONFIG['api']['rate_limit'] = args.rate_limit
    DEFAULT_CONFIG['api']['max_retries'] = args.max_retries

    try:
        # 准备Release ID列表
        release_ids = []

        if args.id:
            release_ids = [args.id]
        elif args.ids:
            release_ids = [int(id.strip()) for id in args.ids.split(',')]
        elif args.file:
            with open(args.file, 'r', encoding='utf-8') as f:
                release_ids = [int(line.strip()) for line in f if line.strip().isdigit()]

        if not release_ids:
            logger.error("❌ 没有找到有效的Release ID")
            return

        logger.info(f"📋 准备获取 {len(release_ids)} 个Release数据")

        # 创建获取器并执行
        fetcher = ReleaseDataFetcher()

        if len(release_ids) == 1:
            # 单个获取
            result = fetcher.fetch_single_release(release_ids[0])
            results = [result]
        else:
            # 批量获取
            results = fetcher.fetch_batch_releases(release_ids)

        # 保存结果
        if args.output:
            save_results_to_json(results, args.output)
        else:
            # 输出到控制台
            successful_results = [r for r in results if r.success]
            if successful_results:
                print("\n📋 获取成功的Release数据:")
                for result in successful_results:
                    print(f"Release {result.release_id}: {result.data['title']}")

        logger.info("🏁 程序执行完成")

    except KeyboardInterrupt:
        logger.info("🛑 用户中断程序")
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        raise

if __name__ == "__main__":
    main()
