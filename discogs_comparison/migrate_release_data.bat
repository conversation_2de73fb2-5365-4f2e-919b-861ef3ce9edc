@echo off
chcp 65001 >nul
REM MongoDB数据迁移脚本启动器 (Windows)

echo ========================================
echo MongoDB数据迁移脚本
echo ========================================
echo.

echo ⚠️  警告: 此脚本将迁移MongoDB中的数据！
echo 📋 源集合: release_copy
echo 📋 目标集合: release_new
echo 📄 筛选条件: status != 429
echo.

echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
for /f "tokens=*" %%i in ('python --version') do echo Python版本: %%i

echo.
echo 📦 检查依赖包...
python -c "import pymongo" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  pymongo未安装，正在安装依赖...
    pip install pymongo
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请手动运行: pip install pymongo
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查通过

echo.
echo ⚠️  最后警告: 此操作将永久迁移数据！
echo 📊 脚本将会：
echo    1. 从release_copy集合查询status != 429的记录
echo    2. 将这些记录插入到release_new集合
echo    3. 插入成功后从release_copy集合删除对应记录
echo    4. 使用事务确保数据一致性
echo.

set /p confirm="确定要继续吗？(输入 Y 继续，任意其他键取消): "
if /i not "%confirm%"=="Y" (
    echo ❌ 操作已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始执行数据迁移脚本...
echo.

python migrate_release_data.py

echo.
if errorlevel 1 (
    echo ❌ 迁移操作失败，请查看日志文件 migrate_release_data.log
) else (
    echo ✅ 迁移操作完成！
    echo 📋 详细日志: migrate_release_data.log
)

echo.
echo 按任意键退出...
pause >nul
