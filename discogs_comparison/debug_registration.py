#!/usr/bin/env python3
"""
Discogs注册调试脚本
用于调试注册过程中的问题
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def debug_registration():
    """调试注册过程"""
    print("🔍 开始调试Discogs注册过程...")
    
    # 启动Safari浏览器
    driver = webdriver.Safari()
    
    try:
        # 访问注册页面
        registration_url = "https://login.discogs.com/u/signup?state=hKFo2SBkWkFnT2lGLVpXQjI5S2tSZmtjT2tfS1ItSmJ3X2x0d6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFpOVcwQnVlNmE5eFRpN0ZSX2NUMGs2S3E5YTNuQmRko2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg"
        print(f"📍 访问注册页面: {registration_url}")
        driver.get(registration_url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 检查当前URL
        current_url = driver.current_url
        print(f"📍 当前URL: {current_url}")
        
        # 检查页面标题
        page_title = driver.title
        print(f"📄 页面标题: {page_title}")
        
        # 查找所有输入框
        input_elements = driver.find_elements(By.TAG_NAME, "input")
        print(f"🔍 找到 {len(input_elements)} 个输入框:")
        for i, element in enumerate(input_elements):
            try:
                input_type = element.get_attribute("type")
                input_name = element.get_attribute("name")
                input_id = element.get_attribute("id")
                input_placeholder = element.get_attribute("placeholder")
                print(f"  {i+1}. type={input_type}, name={input_name}, id={input_id}, placeholder={input_placeholder}")
            except:
                print(f"  {i+1}. 无法获取属性")
        
        # 查找所有按钮
        button_elements = driver.find_elements(By.TAG_NAME, "button")
        print(f"🔍 找到 {len(button_elements)} 个按钮:")
        for i, element in enumerate(button_elements):
            try:
                button_text = element.text.strip()
                button_type = element.get_attribute("type")
                print(f"  {i+1}. text='{button_text}', type={button_type}")
            except:
                print(f"  {i+1}. 无法获取属性")
        
        # 查找表单
        form_elements = driver.find_elements(By.TAG_NAME, "form")
        print(f"🔍 找到 {len(form_elements)} 个表单:")
        for i, element in enumerate(form_elements):
            try:
                form_action = element.get_attribute("action")
                form_method = element.get_attribute("method")
                print(f"  {i+1}. action={form_action}, method={form_method}")
            except:
                print(f"  {i+1}. 无法获取属性")
        
        # 检查是否有错误消息
        error_selectors = [
            ".error", ".alert-danger", "[class*='error']",
            ".warning", ".alert-warning", "[class*='warning']",
            ".message", ".alert", "[class*='alert']"
        ]
        
        for selector in error_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.text.strip():
                        print(f"⚠️ 发现消息 ({selector}): {element.text.strip()}")
            except:
                pass
        
        # 保存页面截图
        screenshot_path = "debug_registration_screenshot.png"
        driver.save_screenshot(screenshot_path)
        print(f"📸 页面截图已保存: {screenshot_path}")
        
        # 保存页面源码
        with open("debug_registration_source.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("📄 页面源码已保存: debug_registration_source.html")
        
        # 等待用户查看
        input("按回车键继续...")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
    
    finally:
        driver.quit()
        print("🔚 调试完成")

if __name__ == "__main__":
    debug_registration()
