#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批处理系统测试脚本
用于验证批处理系统的各个组件是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from batch_processor import BatchConfig, BatchLogger, OSSClient, BatchExecutor
        print("✅ 批处理模块导入成功")
    except ImportError as e:
        print(f"❌ 批处理模块导入失败: {e}")
        return False
    
    try:
        from get_diff import DiffAPI
        print("✅ DiffAPI模块导入成功")
    except ImportError as e:
        print(f"❌ DiffAPI模块导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置管理"""
    print("\n🔍 测试配置管理...")
    
    try:
        from batch_processor import BatchConfig
        
        config = BatchConfig()
        
        # 测试批处理配置
        batch_config = config.get_batch_config()
        if batch_config:
            print("✅ 批处理配置加载成功")
        else:
            print("❌ 批处理配置加载失败")
            return False
        
        # 测试OSS配置
        oss_config = config.get_oss_config()
        if oss_config:
            print("✅ OSS配置加载成功")
        else:
            print("❌ OSS配置加载失败")
            return False
        
        # 测试配置验证
        is_valid = config.validate_oss_config()
        if is_valid:
            print("✅ OSS配置验证通过")
        else:
            print("⚠️  OSS配置不完整（这是正常的，需要用户填写）")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_logger():
    """测试日志系统"""
    print("\n🔍 测试日志系统...")
    
    try:
        from batch_processor import BatchLogger
        
        logger = BatchLogger("test_logger")
        
        # 测试各种日志级别
        logger.info("这是一条测试信息日志")
        logger.warning("这是一条测试警告日志")
        logger.debug("这是一条测试调试日志")
        
        # 测试带额外数据的日志
        logger.info("测试额外数据日志", {"test_key": "test_value"})
        
        print("✅ 日志系统测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False

def test_diff_api():
    """测试DiffAPI"""
    print("\n🔍 测试DiffAPI...")
    
    try:
        from get_diff import DiffAPI
        
        api = DiffAPI()
        
        # 测试配置
        if api.config:
            print("✅ DiffAPI配置加载成功")
            print(f"   支持的数据类型: {list(api.config.keys())}")
        else:
            print("❌ DiffAPI配置加载失败")
            return False
        
        # 测试脚本文件存在性
        missing_scripts = []
        for data_type, config in api.config.items():
            script_dir = api.base_dir / config['script_dir']
            script_path = script_dir / config['script_name']
            
            if script_path.exists():
                print(f"✅ {data_type} 脚本存在: {script_path}")
            else:
                print(f"❌ {data_type} 脚本缺失: {script_path}")
                missing_scripts.append(data_type)
        
        if missing_scripts:
            print(f"⚠️  缺失脚本: {missing_scripts}")
        
        return True
        
    except Exception as e:
        print(f"❌ DiffAPI测试失败: {e}")
        return False

def test_oss_client():
    """测试OSS客户端（仅测试初始化，不测试实际连接）"""
    print("\n🔍 测试OSS客户端...")
    
    try:
        from batch_processor import BatchConfig, OSSClient, BatchLogger
        
        config = BatchConfig()
        logger = BatchLogger("test_oss")
        
        # 检查OSS配置
        if not config.validate_oss_config():
            print("⚠️  OSS配置不完整，跳过OSS客户端测试")
            print("   这是正常的，需要用户填写OSS配置")
            return True
        
        # 尝试初始化OSS客户端
        try:
            oss_client = OSSClient(config.get_oss_config(), logger)
            print("✅ OSS客户端初始化成功")
            return True
        except Exception as e:
            print(f"❌ OSS客户端初始化失败: {e}")
            print("   请检查OSS配置是否正确")
            return False
        
    except Exception as e:
        print(f"❌ OSS客户端测试失败: {e}")
        return False

def test_batch_executor():
    """测试批处理执行器"""
    print("\n🔍 测试批处理执行器...")
    
    try:
        from batch_processor import BatchExecutor
        
        executor = BatchExecutor()
        
        # 测试状态报告
        status_report = executor.get_status_report()
        if status_report and 'diff_files' in status_report:
            print("✅ 批处理执行器初始化成功")
            print(f"   支持的数据类型: {status_report.get('supported_types', [])}")
            print(f"   OSS配置状态: {'已配置' if status_report.get('oss_configured') else '未配置'}")
        else:
            print("❌ 批处理执行器状态报告失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 批处理执行器测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n🔍 测试依赖包...")
    
    required_packages = [
        ('yaml', 'PyYAML'),
        ('oss2', 'oss2'),
        ('psutil', 'psutil'),
        ('pathlib', 'pathlib (内置)'),
        ('datetime', 'datetime (内置)'),
        ('json', 'json (内置)')
    ]
    
    missing_packages = []
    
    for package, display_name in required_packages:
        try:
            __import__(package)
            print(f"✅ {display_name}")
        except ImportError:
            print(f"❌ {display_name}")
            missing_packages.append(display_name)
    
    if missing_packages:
        print(f"\n⚠️  缺失的依赖包: {missing_packages}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def test_file_structure():
    """测试文件结构"""
    print("\n🔍 测试文件结构...")
    
    required_files = [
        'batch_run.py',
        'get_diff.py',
        'requirements.txt',
        'batch_processor/__init__.py',
        'batch_processor/config.py',
        'batch_processor/logger.py',
        'batch_processor/oss_client.py',
        'batch_processor/batch_executor.py'
    ]
    
    required_dirs = [
        'config',
        'logs',
        'batch_processor',
        'release_comparison',
        'master_comparison',
        'artists_comparison',
        'label_comparison'
    ]
    
    missing_files = []
    missing_dirs = []
    
    # 检查文件
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    # 检查目录
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/")
            missing_dirs.append(dir_path)
    
    if missing_files or missing_dirs:
        print(f"\n⚠️  缺失的文件: {missing_files}")
        print(f"⚠️  缺失的目录: {missing_dirs}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🧪 Discogs 数据比较批处理系统测试")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("依赖包", test_dependencies),
        ("模块导入", test_imports),
        ("配置管理", test_config),
        ("日志系统", test_logger),
        ("DiffAPI", test_diff_api),
        ("OSS客户端", test_oss_client),
        ("批处理执行器", test_batch_executor)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_name} 时发生异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查上述错误信息。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
