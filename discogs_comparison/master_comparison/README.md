# Discogs Masters 数据比较工具

## 概述

这是一个用于比较两个不同日期的 Discogs Masters XML 数据文件的工具。该工具能够识别数据变化并生成包含 CREATE、UPDATE、DELETE 操作的 CSV 报告。

## 功能特性

- 🔍 **智能数据比较**: 基于哈希值比较，准确识别数据变化
- 📊 **详细统计报告**: 提供 CREATE、UPDATE、DELETE 操作的详细统计
- 🚀 **测试模式**: 支持少量数据测试，便于验证功能
- 💾 **高效处理**: 使用 SQLite 临时数据库，支持大数据集处理
- 📈 **进度显示**: 实时显示处理进度和状态
- 🗂️ **标准输出**: 生成标准 CSV 格式的比较结果

## 文件结构

```
master_comparison/
├── README.md                           # 本文档
├── compare_master.py                    # 主程序脚本
├── discogs_20250501_masters.xml.gz     # 旧版本数据文件
├── discogs_20250601_masters.xml.gz     # 新版本数据文件
└── master_comparison_diff.csv          # 输出的比较结果（运行后生成）
```

## 数据字段

工具提取并比较以下业务字段（参考 process_master.py）：

| 字段名           | 描述            | 示例                 |
| ---------------- | --------------- | -------------------- |
| id               | Master 记录 ID  | "113"                |
| y_id             | 内部序列 ID     | "YM1"                |
| key_release      | 主要发行版本 ID | "116925"             |
| title            | 专辑标题        | "Moments In Time"    |
| year             | 发行年份        | "2002"               |
| artists          | 艺术家信息      | JSON 数组            |
| genres           | 音乐类型        | JSON 数组            |
| styles           | 音乐风格        | JSON 数组            |
| notes            | 备注信息        | 字符串               |
| videos           | 视频信息        | JSON 数组            |
| images           | 图片信息        | JSON 数组            |
| created_at       | 创建时间        | ISO 格式时间戳       |
| operation_status | 操作状态        | CREATE/UPDATE/DELETE |

## 安装要求

### Python 版本

- Python 3.6 或更高版本

### 依赖库

```bash
# 标准库，无需额外安装
import sqlite3
import csv
import re
import hashlib
import json
import time
import datetime
import os
import gzip
```

## 使用方法

### 1. 准备数据文件

确保以下文件存在于 `master_comparison` 目录中：

- `discogs_20250501_masters.xml.gz` (旧版本数据)
- `discogs_20250601_masters.xml.gz` (新版本数据)

### 2. 配置运行模式

编辑 `compare_master.py` 文件中的配置参数：

```python
# 测试模式：设置为True时只处理少量数据，False时处理全部数据
TEST_MODE = True  # 改为False以处理完整数据集
MAX_TEST_RECORDS = 1000  # 测试模式下每个文件最大处理记录数
```

### 3. 运行程序

```bash
cd master_comparison
python3 compare_master.py
```

## 运行模式

### 测试模式 (TEST_MODE = True)

- 每个文件最多处理 1000 条记录
- 适用于功能验证和快速测试
- 运行时间：通常几秒钟

### 完整模式 (TEST_MODE = False)

- 处理所有数据记录
- 适用于生产环境的完整数据比较
- 运行时间：几分钟到几十分钟（取决于数据量）

## 输出结果

### 控制台输出示例

```
============================================================
Discogs Masters 数据比较工具 (测试模式)
============================================================
测试模式：每个文件最多处理 1000 条记录

第一步：加载20250501数据...
正在处理文件: discogs_20250501_masters.xml.gz
测试模式：已达到最大记录数 1000，停止处理
文件 discogs_20250501_masters.xml.gz 处理完成，共处理 1000 条记录

第二步：加载20250601数据...
正在处理文件: discogs_20250601_masters.xml.gz
测试模式：已达到最大记录数 1000，停止处理
文件 discogs_20250601_masters.xml.gz 处理完成，共处理 1000 条记录

数据加载完成:
20250501: 1000 条记录
20250601: 1000 条记录

第三步：比较数据并生成CSV...
开始比较数据...
共找到 449 个唯一ID
比较完成，共发现 22 条变化记录
正在写入CSV文件: master_comparison_diff.csv
CSV文件生成完成: master_comparison_diff.csv
共写入 22 条变化记录

操作统计:
CREATE: 2 条
UPDATE: 18 条
DELETE: 2 条
总计: 22 条

总处理时间: 0.28 秒
============================================================
```

### CSV 输出文件

生成的 `master_comparison_diff.csv` 文件包含：

- 表头：所有业务字段名称
- 数据行：只包含有变化的记录
- 最后一列：operation_status (CREATE/UPDATE/DELETE)

## 操作状态说明

| 状态   | 描述                                 | 数据来源      |
| ------ | ------------------------------------ | ------------- |
| CREATE | 新增记录（仅在新文件中存在）         | 20250601 数据 |
| UPDATE | 更新记录（两个文件都存在但内容不同） | 20250601 数据 |
| DELETE | 删除记录（仅在旧文件中存在）         | 20250501 数据 |

**注意**:

- CREATE 和 UPDATE 操作使用最新数据（20250601）
- DELETE 操作使用旧数据（20250501）但标记为删除状态
- 无变化的记录不会出现在输出 CSV 中

## 性能优化

### 内存使用

- 使用 SQLite 临时数据库存储数据，避免内存溢出
- 流式处理 XML 文件，逐行读取
- 定期提交数据库事务，优化性能

### 处理速度

- 测试模式：1000 条记录约 0.3 秒
- 完整模式：预计每 10 万条记录需要几分钟

## 故障排除

### 常见问题

1. **文件不存在错误**

   ```
   错误：文件 discogs_20250501_masters.xml.gz 不存在
   ```

   **解决方案**: 确保数据文件存在于正确的目录中

2. **内存不足**

   ```
   MemoryError: ...
   ```

   **解决方案**: 确保 TEST_MODE=True 进行测试，或增加系统内存

3. **权限错误**
   ```
   PermissionError: ...
   ```
   **解决方案**: 确保对目录有读写权限

### 调试模式

如需调试，可以修改代码中的进度显示频率：

```python
# 更频繁的进度显示
if processed_count % 1000 == 0:  # 原来是10000
    print(f"已处理 {processed_count} 条记录...")
```

## 技术实现

### 核心算法

1. **XML 解析**: 使用正则表达式解析 XML 结构
2. **数据哈希**: MD5 哈希值比较检测数据变化
3. **数据库操作**: SQLite 临时存储和查询
4. **流式处理**: 逐行处理大文件，避免内存问题

### 数据流程

```
XML文件 → 解析提取 → 计算哈希 → 存储到SQLite → 比较分析 → 生成CSV
```

## 版本历史

- **v1.0** (2025-06-29): 初始版本，支持基本的数据比较功能

## 许可证

本项目仅供内部使用。

## 重要提示

### 数据完整性

- 工具会自动清理临时数据库文件
- 原始 XML 文件不会被修改
- 建议在处理前备份重要数据

### 性能建议

- 首次使用建议先运行测试模式验证结果
- 完整模式处理大数据集时建议在服务器环境运行
- 确保有足够的磁盘空间存储临时数据库

### 数据质量

- 工具基于 XML 结构解析，确保输入文件格式正确
- 哈希比较能准确检测字段级别的数据变化
- 输出 CSV 使用 UTF-8 编码，支持多语言字符

## 示例用法

### 快速测试

```bash
# 1. 确认文件存在
ls -la *.gz

# 2. 运行测试模式
python3 compare_master.py

# 3. 查看结果
head -5 master_comparison_diff.csv
wc -l master_comparison_diff.csv
```

### 完整处理

```bash
# 1. 修改配置为完整模式
sed -i 's/TEST_MODE = True/TEST_MODE = False/' compare_master.py

# 2. 运行完整比较（可能需要较长时间）
nohup python3 compare_master.py > processing.log 2>&1 &

# 3. 监控进度
tail -f processing.log
```

## 联系方式

如有问题或建议，请联系开发团队。
