#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import csv
import re
import hashlib
import json
import time
from datetime import datetime
import os
import gzip

# 配置参数
FILE_MASTER_OLD = 'discogs_20250501_masters.xml.gz'
FILE_MASTER_NEW = 'discogs_20250601_masters.xml.gz'
OUTPUT_CSV = 'master_comparison_diff.csv'
TEMP_DB = 'temp_masters_comparison.db'


# 测试模式：设置为True时只处理少量数据，False时处理全部数据
# 注意：处理完整数据集可能需要较长时间（几分钟到几十分钟）
TEST_MODE = True  # 改为False以处理完整数据集
MAX_TEST_RECORDS = 100000  # 测试模式下每个文件最大处理记录数

# CSV字段定义（只包含用户需要的字段）
CSV_FIELDS = [
    'id', 'y_id', 'key_release', 'notes', 'created_at', 'operation_status'
]

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None

def extract_notes(content):
    """提取notes字段"""
    notes = extract_field(content, 'notes')
    return notes.strip() if notes else None

def process_master_content(buffer, sequential_id):
    """处理单个master标签的内容"""
    # 提取ID
    master_id = extract_field(buffer, 'id')
    if not master_id:
        return None

    # 创建master文档（只包含用户需要的字段）
    master_doc = {
        'id': master_id,
        'y_id': f"YM{sequential_id}",
        'key_release': extract_field(buffer, 'main_release'),  # main_release保存为key_release
        'notes': extract_notes(buffer),
        'created_at': datetime.now().isoformat(),
    }

    return master_doc

def create_hash(master_data):
    """为master数据创建哈希值，用于比较"""
    # 排除时间戳和y_id字段，只对业务数据计算哈希
    hash_data = {
        'id': master_data.get('id', ''),
        'key_release': master_data.get('key_release', ''),
        'notes': master_data.get('notes', ''),
    }

    hash_string = json.dumps(hash_data, sort_keys=True, ensure_ascii=False)
    return hashlib.md5(hash_string.encode('utf-8')).hexdigest()

def load_masters_to_db(filename, table_name, conn):
    """从gz文件加载masters数据到SQLite数据库"""
    print(f"正在处理文件: {filename}")

    cursor = conn.cursor()

    # 创建表（只包含需要的字段）
    cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS {table_name} (
            id TEXT PRIMARY KEY,
            y_id TEXT,
            key_release TEXT,
            notes TEXT,
            created_at TEXT,
            hash TEXT
        )
    ''')

    processed_count = 0
    sequential_id = 0
    max_records = MAX_TEST_RECORDS if TEST_MODE else float('inf')

    try:
        with gzip.open(filename, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_master = False
            total_lines = 0

            for line in f:
                total_lines += 1
                if total_lines % 1000000 == 0:
                    print(f"已读取 {total_lines} 行，处理了 {processed_count} 条记录")

                if '<master' in line and 'id=' in line:
                    buffer = line
                    in_master = True
                elif '</master>' in line and in_master:
                    buffer += line
                    in_master = False

                    # 处理master内容，使用连续的序号作为y_id
                    sequential_id += 1
                    master_doc = process_master_content(buffer, sequential_id)
                    if master_doc:
                        # 计算哈希值
                        hash_value = create_hash(master_doc)

                        # 插入数据库（只插入需要的字段）
                        cursor.execute(f'''
                            INSERT OR REPLACE INTO {table_name}
                            (id, y_id, key_release, notes, created_at, hash)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ''', (
                            master_doc['id'], master_doc['y_id'], master_doc['key_release'],
                            master_doc['notes'], master_doc['created_at'], hash_value
                        ))

                        processed_count += 1

                        # 显示进度
                        if processed_count % 10000 == 0:
                            print(f"已处理 {processed_count} 条记录...")
                            conn.commit()  # 定期提交

                        # 测试模式下达到最大记录数时退出
                        if processed_count >= max_records:
                            print(f"测试模式：已达到最大记录数 {max_records}，停止处理")
                            break

                    # 清空缓冲区
                    buffer = ""
                elif in_master:
                    buffer += line

                # 测试模式下达到最大记录数时退出外层循环
                if processed_count >= max_records:
                    break

    except Exception as e:
        print(f"处理文件 {filename} 时出错: {e}")
        return 0

    conn.commit()
    print(f"文件 {filename} 处理完成，共处理 {processed_count} 条记录")
    return processed_count

def compare_and_generate_csv(conn):
    """比较两个数据集并生成CSV文件"""
    print("开始比较数据...")

    cursor = conn.cursor()

    # 获取所有唯一的ID
    cursor.execute('''
        SELECT DISTINCT id FROM (
            SELECT id FROM masters_old
            UNION
            SELECT id FROM masters_new
        )
    ''')

    all_ids = [row[0] for row in cursor.fetchall()]
    print(f"共找到 {len(all_ids)} 个唯一ID")

    changes = []
    processed = 0

    for master_id in all_ids:
        # 获取两个版本的数据
        cursor.execute('SELECT * FROM masters_old WHERE id = ?', (master_id,))
        old_data = cursor.fetchone()

        cursor.execute('SELECT * FROM masters_new WHERE id = ?', (master_id,))
        new_data = cursor.fetchone()

        if old_data and new_data:
            # 两个文件都存在，检查是否有变化
            old_hash = old_data[-1]  # hash字段在最后
            new_hash = new_data[-1]

            if old_hash != new_hash:
                # 数据有变化，记录UPDATE，使用最新数据（20250601）
                record = list(new_data[:-1])  # 排除hash字段
                record.append('UPDATE')  # 添加operation_status
                changes.append(record)
        elif new_data:
            # 只在新文件中存在，记录CREATE
            record = list(new_data[:-1])  # 排除hash字段
            record.append('CREATE')  # 添加operation_status
            changes.append(record)
        elif old_data:
            # 只在旧文件中存在，记录DELETE，使用旧数据但标记为删除
            record = list(old_data[:-1])  # 排除hash字段
            record.append('DELETE')  # 添加operation_status
            changes.append(record)

        processed += 1
        if processed % 10000 == 0:
            print(f"已比较 {processed}/{len(all_ids)} 条记录...")

    print(f"比较完成，共发现 {len(changes)} 条变化记录")

    # 写入CSV文件
    print(f"正在写入CSV文件: {OUTPUT_CSV}")
    with open(OUTPUT_CSV, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)

        # 写入表头
        writer.writerow(CSV_FIELDS)

        # 写入数据
        for record in changes:
            writer.writerow(record)

    print(f"CSV文件生成完成: {OUTPUT_CSV}")
    print(f"共写入 {len(changes)} 条变化记录")

    # 统计各种操作类型的数量
    create_count = sum(1 for record in changes if record[-1] == 'CREATE')
    update_count = sum(1 for record in changes if record[-1] == 'UPDATE')
    delete_count = sum(1 for record in changes if record[-1] == 'DELETE')

    print("\n操作统计:")
    print(f"CREATE: {create_count} 条")
    print(f"UPDATE: {update_count} 条")
    print(f"DELETE: {delete_count} 条")
    print(f"总计: {len(changes)} 条")

def main():
    """主函数"""
    start_time = time.time()

    mode_text = "测试模式" if TEST_MODE else "完整数据集模式"
    print("="*60)
    print(f"Discogs Masters 数据比较工具 ({mode_text})")
    print("="*60)

    if TEST_MODE:
        print(f"测试模式：每个文件最多处理 {MAX_TEST_RECORDS} 条记录")
    else:
        print("完整模式：处理所有数据")

    # 检查输入文件是否存在
    if not os.path.exists(FILE_MASTER_OLD):
        print(f"错误：文件 {FILE_MASTER_OLD} 不存在")
        return

    if not os.path.exists(FILE_MASTER_NEW):
        print(f"错误：文件 {FILE_MASTER_NEW} 不存在")
        return

    # 删除旧的临时数据库和输出文件
    if os.path.exists(TEMP_DB):
        os.remove(TEMP_DB)
    if os.path.exists(OUTPUT_CSV):
        os.remove(OUTPUT_CSV)

    try:
        # 创建SQLite连接
        conn = sqlite3.connect(TEMP_DB)

        # 加载两个文件的数据
        print("\n第一步：加载20250501数据...")
        count1 = load_masters_to_db(FILE_MASTER_OLD, 'masters_old', conn)

        print("\n第二步：加载20250601数据...")
        count2 = load_masters_to_db(FILE_MASTER_NEW, 'masters_new', conn)

        print(f"\n数据加载完成:")
        print(f"20250501: {count1} 条记录")
        print(f"20250601: {count2} 条记录")

        # 比较数据并生成CSV
        print("\n第三步：比较数据并生成CSV...")
        compare_and_generate_csv(conn)

        # 关闭数据库连接
        conn.close()

        # 清理临时数据库
        if os.path.exists(TEMP_DB):
            os.remove(TEMP_DB)
            print(f"临时数据库 {TEMP_DB} 已清理")

    except Exception as e:
        print(f"处理过程中出错: {e}")
        # 确保清理临时文件
        if os.path.exists(TEMP_DB):
            os.remove(TEMP_DB)

    # 计算总处理时间
    total_time = time.time() - start_time
    print(f"\n总处理时间: {total_time:.2f} 秒")
    print("="*60)

if __name__ == "__main__":
    main()
