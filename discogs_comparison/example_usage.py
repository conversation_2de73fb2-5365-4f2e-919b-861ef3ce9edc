#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API 客户端使用示例

本文件展示了如何使用 discogs_api_client.py 获取 Discogs release 数据的各种方法。

作者：AI Assistant
创建时间：2025-08-03
"""

import sys
import json
from discogs_api_client import ReleaseDataFetcher, save_results_to_json

def example_single_release():
    """示例1: 获取单个 Release"""
    print("=" * 60)
    print("示例1: 获取单个 Release")
    print("=" * 60)
    
    # 创建获取器
    fetcher = ReleaseDataFetcher()
    
    # 获取单个 release（使用一个已知存在的 ID）
    release_id = 249504  # Pink Floyd - The Dark Side Of The Moon
    
    print(f"正在获取 Release ID: {release_id}")
    result = fetcher.fetch_single_release(release_id)
    
    if result.success:
        print(f"✅ 获取成功!")
        print(f"标题: {result.data['title']}")
        print(f"国家: {result.data['country']}")
        print(f"年份: {result.data['year']}")
        print(f"艺术家数量: {len(result.data['artists'])}")
        print(f"曲目数量: {len(result.data['tracklist'])}")
    else:
        print(f"❌ 获取失败: {result.error_message}")
        print(f"错误类型: {result.error_type}")

def example_batch_releases():
    """示例2: 批量获取多个 Release"""
    print("\n" + "=" * 60)
    print("示例2: 批量获取多个 Release")
    print("=" * 60)
    
    # 创建获取器
    fetcher = ReleaseDataFetcher()
    
    # 定义要获取的 release ID 列表（使用一些已知存在的 ID）
    release_ids = [
        249504,   # Pink Floyd - The Dark Side Of The Moon
        1,        # The Persuader - The Persuader
        2,        # Mr. James Barth & A.D. - Knockin' Boots
        ********* # 不存在的 ID，用于测试错误处理
    ]
    
    print(f"正在批量获取 {len(release_ids)} 个 Release")
    
    # 定义进度回调函数
    def progress_callback(current, total, result):
        status = "✅" if result.success else "❌"
        print(f"  {status} 进度: {current}/{total} - Release {result.release_id}")
    
    # 批量获取
    results = fetcher.fetch_batch_releases(release_ids, progress_callback)
    
    # 分析结果
    successful_results = [r for r in results if r.success]
    failed_results = [r for r in results if not r.success]
    
    print(f"\n📊 批量获取结果:")
    print(f"成功: {len(successful_results)}")
    print(f"失败: {len(failed_results)}")
    
    if successful_results:
        print("\n✅ 成功获取的 Release:")
        for result in successful_results:
            print(f"  - {result.release_id}: {result.data['title']}")
    
    if failed_results:
        print("\n❌ 获取失败的 Release:")
        for result in failed_results:
            print(f"  - {result.release_id}: {result.error_message} ({result.error_type.value})")

def example_save_to_json():
    """示例3: 保存结果到 JSON 文件"""
    print("\n" + "=" * 60)
    print("示例3: 保存结果到 JSON 文件")
    print("=" * 60)
    
    # 创建获取器
    fetcher = ReleaseDataFetcher()
    
    # 获取一些 release 数据
    release_ids = [249504, 1, 2]
    print(f"正在获取 {len(release_ids)} 个 Release 并保存到文件")
    
    results = fetcher.fetch_batch_releases(release_ids)
    
    # 保存到 JSON 文件
    output_file = "example_releases.json"
    save_results_to_json(results, output_file)
    
    print(f"✅ 结果已保存到: {output_file}")
    
    # 读取并显示文件内容摘要
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"\n📋 文件内容摘要:")
        print(f"总数量: {data['metadata']['total_count']}")
        print(f"成功数量: {data['metadata']['successful_count']}")
        print(f"错误数量: {data['metadata']['error_count']}")
        print(f"生成时间: {data['metadata']['generated_at']}")
        print(f"Release 数据条数: {len(data['releases'])}")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

def example_custom_accounts():
    """示例4: 使用自定义账号配置"""
    print("\n" + "=" * 60)
    print("示例4: 使用自定义账号配置")
    print("=" * 60)
    
    # 注意：这里使用的是示例配置，实际使用时需要替换为真实的账号信息
    custom_accounts = [
        {
            'email': '<EMAIL>',
            'token': 'your_token_here_1',
            'user_agent': 'YourApp/1.0 +https://yoursite.com/contact'
        },
        {
            'email': '<EMAIL>',
            'token': 'your_token_here_2',
            'user_agent': 'YourApp/1.0 +https://yoursite.com/contact'
        }
    ]
    
    print("注意：此示例使用虚拟账号配置，实际运行会失败")
    print("请替换为真实的 Discogs 账号信息")
    
    try:
        # 创建使用自定义账号的获取器
        fetcher = ReleaseDataFetcher(custom_accounts)
        print("✅ 自定义账号配置成功")
        
        # 这里会失败，因为使用的是虚拟账号
        # result = fetcher.fetch_single_release(249504)
        
    except Exception as e:
        print(f"❌ 自定义账号配置失败（预期结果）: {e}")

def example_error_handling():
    """示例5: 错误处理演示"""
    print("\n" + "=" * 60)
    print("示例5: 错误处理演示")
    print("=" * 60)
    
    # 创建获取器
    fetcher = ReleaseDataFetcher()
    
    # 测试不同类型的错误
    test_cases = [
        (*********, "不存在的 Release ID（404错误）"),
        (0, "无效的 Release ID"),
        (-1, "负数 Release ID")
    ]
    
    for release_id, description in test_cases:
        print(f"\n测试: {description}")
        result = fetcher.fetch_single_release(release_id)
        
        if result.success:
            print(f"  ✅ 意外成功: {result.data['title']}")
        else:
            print(f"  ❌ 预期失败: {result.error_message}")
            print(f"  错误类型: {result.error_type.value}")

def example_data_structure():
    """示例6: 数据结构展示"""
    print("\n" + "=" * 60)
    print("示例6: 数据结构展示")
    print("=" * 60)
    
    # 创建获取器
    fetcher = ReleaseDataFetcher()
    
    # 获取一个 release
    release_id = 249504
    result = fetcher.fetch_single_release(release_id)
    
    if result.success:
        data = result.data
        print(f"Release {release_id} 数据结构:")
        print(f"  基础信息:")
        print(f"    - ID: {data['id']}")
        print(f"    - 标题: {data['title']}")
        print(f"    - 国家: {data['country']}")
        print(f"    - 年份: {data['year']}")
        print(f"    - Master ID: {data['master_id']}")
        
        print(f"  艺术家信息:")
        print(f"    - 主要艺术家: {len(data['artists'])} 个")
        print(f"    - 额外艺术家: {len(data['extra_artists'])} 个")
        
        print(f"  分类信息:")
        print(f"    - 风格: {data['genres']}")
        print(f"    - 子风格: {data['styles']}")
        
        print(f"  技术信息:")
        print(f"    - 格式: {len(data['formats'])} 个")
        print(f"    - 标签: {len(data['labels'])} 个")
        print(f"    - 曲目: {len(data['tracklist'])} 个")
        
        print(f"  系统字段:")
        print(f"    - 权限: {data['permissions']}")
        print(f"    - 来源: {data['source']}")
        print(f"    - 创建时间: {data['created_at']}")
    else:
        print(f"❌ 无法获取数据用于展示: {result.error_message}")

def main():
    """主函数 - 运行所有示例"""
    print("🚀 Discogs API 客户端使用示例")
    print("本示例将演示各种使用方法")
    
    try:
        # 运行所有示例
        example_single_release()
        example_batch_releases()
        example_save_to_json()
        example_custom_accounts()
        example_error_handling()
        example_data_structure()
        
        print("\n" + "=" * 60)
        print("🏁 所有示例运行完成")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断示例运行")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        raise

if __name__ == "__main__":
    main()
