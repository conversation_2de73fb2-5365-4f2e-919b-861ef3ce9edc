#!/usr/bin/env python3
"""
验证Discogs注册结果的脚本
通过实际登录测试来验证账号是否真的注册成功
"""

import time
import yaml
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>riverWait
from selenium.webdriver.support import expected_conditions as EC

def load_config():
    """加载配置文件"""
    config_file = Path("config/register_config.yaml")
    with open(config_file, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def test_login(username, password):
    """测试账号登录"""
    print(f"🔍 测试账号登录: {username}")
    
    # 启动Safari浏览器
    driver = webdriver.Safari()
    
    try:
        # 访问登录页面
        login_url = "https://login.discogs.com/u/login?state=hKFo2SB1UlJjMmIzS1lqc01FS0tMc0JiQVpiZ0FpdlExUXJ5ZKFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIF9MWnNkVWJ2MGVaMUo3Szd1aDJEcUJXa3VrWmZGdkUyo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg"
        print(f"📍 访问登录页面...")
        driver.get(login_url)
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        
        # 填写用户名
        username_field = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input[name='username']")))
        username_field.clear()
        username_field.send_keys(username)
        print(f"✅ 用户名已填写: {username}")
        
        # 填写密码
        password_field = driver.find_element(By.CSS_SELECTOR, "input[name='password']")
        password_field.clear()
        password_field.send_keys(password)
        print(f"✅ 密码已填写")
        
        # 点击登录按钮
        submit_button = driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        submit_button.click()
        print(f"✅ 登录按钮已点击")
        
        # 等待页面响应
        time.sleep(5)
        
        # 检查登录结果
        current_url = driver.current_url
        page_source = driver.page_source.lower()
        
        # 检查是否有错误消息
        error_selectors = [
            ".error", ".alert-danger", "[class*='error']",
            ".warning", ".alert-warning", "[class*='warning']"
        ]
        
        has_error = False
        error_message = ""
        
        for selector in error_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed() and element.text.strip():
                        error_message = element.text.strip()
                        if "unknown username" in error_message.lower() or "unknown email" in error_message.lower():
                            has_error = True
                            break
                if has_error:
                    break
            except:
                pass
        
        # 判断登录结果
        if has_error:
            print(f"❌ 登录失败: {error_message}")
            return False
        elif "login" not in current_url.lower() and ("discogs.com" in current_url):
            print(f"✅ 登录成功！重定向到: {current_url}")
            return True
        elif "unknown username" in page_source or "unknown email" in page_source:
            print(f"❌ 登录失败: 用户名或邮箱不存在")
            return False
        else:
            print(f"⚠️ 登录结果不确定，当前URL: {current_url}")
            # 保存页面截图用于调试
            driver.save_screenshot(f"login_test_{username}.png")
            print(f"📸 页面截图已保存: login_test_{username}.png")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    
    finally:
        driver.quit()

def main():
    """主函数"""
    print("🎯 开始验证Discogs注册结果...")
    
    # 加载配置
    config = load_config()
    accounts = config.get('accounts', [])
    
    if not accounts:
        print("❌ 未找到账号配置")
        return
    
    # 测试每个账号
    results = {}
    for account in accounts:
        if not account.get('enabled', True):
            continue
            
        username = account.get('username')
        password = account.get('password')
        
        if not username or not password:
            print(f"⚠️ 账号配置不完整: {account}")
            continue
        
        print(f"\n{'='*50}")
        result = test_login(username, password)
        results[username] = result
        
        # 等待一下再测试下一个账号
        time.sleep(2)
    
    # 输出总结
    print(f"\n{'='*50}")
    print("📊 验证结果总结:")
    print(f"{'='*50}")
    
    success_count = 0
    for username, success in results.items():
        status = "✅ 注册成功" if success else "❌ 注册失败"
        print(f"  {username}: {status}")
        if success:
            success_count += 1
    
    total_count = len(results)
    print(f"\n📈 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == 0:
        print("\n⚠️ 所有账号都注册失败，建议检查注册脚本的逻辑")
    elif success_count < total_count:
        print(f"\n⚠️ 有 {total_count - success_count} 个账号注册失败，建议重新运行注册脚本")
    else:
        print("\n🎉 所有账号都注册成功！")

if __name__ == "__main__":
    main()
