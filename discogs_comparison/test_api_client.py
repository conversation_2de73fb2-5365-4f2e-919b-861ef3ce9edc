#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API 客户端测试脚本

用于测试 discogs_api_client.py 的基本功能，避免初始化时的429错误。

作者：AI Assistant
创建时间：2025-08-03
"""

import sys
import time
from discogs_api_client import DiscogsClientManager, ReleaseDataFetcher, DISCOGS_ACCOUNTS

def test_client_initialization():
    """测试客户端初始化（跳过连接测试）"""
    print("=" * 60)
    print("测试1: 客户端初始化（跳过连接测试）")
    print("=" * 60)
    
    try:
        # 创建客户端管理器，跳过连接测试
        manager = DiscogsClientManager(DISCOGS_ACCOUNTS, test_connection=False)
        print(f"✅ 客户端管理器初始化成功，共 {len(manager.clients)} 个客户端")
        
        # 获取客户端
        client, account_index = manager.get_client()
        print(f"✅ 获取客户端成功，当前账号: {manager.get_account_info(account_index)}")
        
        return manager
        
    except Exception as e:
        print(f"❌ 客户端初始化失败: {e}")
        return None

def test_single_release_gentle(manager):
    """温和地测试单个release获取"""
    print("\n" + "=" * 60)
    print("测试2: 温和地获取单个Release")
    print("=" * 60)
    
    if not manager:
        print("❌ 跳过测试，因为客户端管理器未初始化")
        return
    
    try:
        # 创建获取器，使用现有的管理器
        fetcher = ReleaseDataFetcher()
        fetcher.client_manager = manager  # 使用已初始化的管理器
        
        # 选择一个已知存在的release ID
        release_id = 249504  # Pink Floyd - The Dark Side Of The Moon
        
        print(f"正在获取 Release ID: {release_id}")
        print("⏳ 请耐心等待，可能需要一些时间...")
        
        # 添加额外延迟
        time.sleep(3)
        
        result = fetcher.fetch_single_release(release_id)
        
        if result.success:
            print(f"✅ 获取成功!")
            print(f"标题: {result.data['title']}")
            print(f"国家: {result.data['country']}")
            print(f"年份: {result.data['year']}")
            print(f"艺术家: {result.data['artists'][0]['name'] if result.data['artists'] else 'N/A'}")
            print(f"数据字段数量: {len(result.data)}")
        else:
            print(f"❌ 获取失败: {result.error_message}")
            print(f"错误类型: {result.error_type.value}")
            
            # 如果是429错误，给出建议
            if result.error_type.value == "429":
                print("💡 建议: 遇到429错误，请等待几分钟后重试")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_data_structure():
    """测试数据结构转换"""
    print("\n" + "=" * 60)
    print("测试3: 数据结构转换测试")
    print("=" * 60)
    
    try:
        from discogs_api_client import DataConverter
        
        # 测试字符串处理
        test_string = "  Test String\t\n  "
        cleaned = DataConverter.safe_string_value(test_string)
        print(f"字符串清理测试: '{test_string}' -> '{cleaned}'")
        
        # 测试整数处理
        test_values = [123, "456", None, "invalid"]
        for value in test_values:
            result = DataConverter.safe_integer_value(value, -1)
            print(f"整数转换测试: {value} -> {result}")
        
        print("✅ 数据结构转换测试通过")
        
    except Exception as e:
        print(f"❌ 数据结构转换测试失败: {e}")

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试4: 错误处理测试")
    print("=" * 60)
    
    try:
        from discogs_api_client import ErrorHandler, ErrorType
        
        handler = ErrorHandler()
        
        # 模拟不同类型的错误
        print("错误分类测试:")
        
        # 测试错误统计
        handler.record_error(ErrorType.NOT_FOUND)
        handler.record_error(ErrorType.RATE_LIMITED)
        handler.record_error(ErrorType.NETWORK_ERROR)
        
        stats = handler.get_error_stats()
        print(f"错误统计: {stats}")
        
        # 测试重试逻辑
        should_retry_404 = handler.should_retry(ErrorType.NOT_FOUND, 1)
        should_retry_network = handler.should_retry(ErrorType.NETWORK_ERROR, 1)
        
        print(f"404错误是否重试: {should_retry_404}")
        print(f"网络错误是否重试: {should_retry_network}")
        
        print("✅ 错误处理测试通过")
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")

def test_configuration():
    """测试配置"""
    print("\n" + "=" * 60)
    print("测试5: 配置测试")
    print("=" * 60)
    
    try:
        from discogs_api_client import DEFAULT_CONFIG, DISCOGS_ACCOUNTS
        
        print("默认配置:")
        print(f"  API频率限制: {DEFAULT_CONFIG['api']['rate_limit']} 秒")
        print(f"  最大重试次数: {DEFAULT_CONFIG['api']['max_retries']}")
        print(f"  请求超时: {DEFAULT_CONFIG['api']['timeout']} 秒")
        
        print(f"\n账号配置:")
        for i, account in enumerate(DISCOGS_ACCOUNTS):
            print(f"  账号 {i+1}: {account['email']}")
            print(f"    Token: {account['token'][:10]}...")
        
        print("✅ 配置测试通过")
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 Discogs API 客户端测试")
    print("本测试将验证各个组件的基本功能")
    print("注意：为避免429错误，测试会跳过连接验证")
    
    try:
        # 运行所有测试
        manager = test_client_initialization()
        test_data_structure()
        test_error_handling()
        test_configuration()
        
        # 如果用户同意，进行实际API测试
        print("\n" + "=" * 60)
        user_input = input("是否进行实际API测试？这可能触发429错误 (y/N): ").strip().lower()
        
        if user_input in ['y', 'yes']:
            test_single_release_gentle(manager)
        else:
            print("⏭️ 跳过实际API测试")
        
        print("\n" + "=" * 60)
        print("🏁 所有测试完成")
        print("=" * 60)
        
        if manager:
            print("💡 提示：客户端已成功初始化，可以尝试使用命令行工具：")
            print("   python discogs_api_client.py --id 249504")
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise

if __name__ == "__main__":
    main()
