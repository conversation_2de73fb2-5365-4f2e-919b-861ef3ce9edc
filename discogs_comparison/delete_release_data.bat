@echo off
chcp 65001 >nul
echo ========================================
echo MongoDB数据删除脚本
echo ========================================
echo.

echo ⚠️  警告: 此脚本将删除MongoDB中的数据！
echo 📋 目标数据库: music_test
echo 📋 目标集合: release_new
echo 📄 参考文件: release_data_july_29_30.csv
echo.

echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 检查依赖包...
python -c "import pymongo" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  pymongo未安装，正在安装依赖...
    pip install pymongo
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请手动运行: pip install pymongo
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查通过

echo.
echo 📄 检查CSV文件...
if not exist "release_data_july_29_30.csv" (
    echo ❌ 错误: 未找到CSV文件 release_data_july_29_30.csv
    echo    请确保CSV文件存在于当前目录
    pause
    exit /b 1
)

echo ✅ CSV文件检查通过

echo.
echo ⚠️  最后警告: 此操作将永久删除数据，无法撤销！
echo 📊 脚本将会：
echo    1. 读取CSV文件中的id列表
echo    2. 在MongoDB中查找匹配的记录
echo    3. 显示删除统计信息
echo    4. 要求您确认后执行删除
echo.

set /p confirm="确定要继续吗？(输入 Y 继续，任意其他键取消): "
if /i not "%confirm%"=="Y" (
    echo ❌ 操作已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始执行数据删除脚本...
echo.

python delete_release_data.py

echo.
if errorlevel 1 (
    echo ❌ 删除操作失败，请查看日志文件 delete_release_data.log
) else (
    echo ✅ 脚本执行完成！
    echo 📋 详细日志: delete_release_data.log
)

echo.
echo 按任意键退出...
pause >nul
