#!/usr/bin/env python3
"""
简化的浏览器测试脚本
用于验证不同浏览器的WebDriver是否能正常工作
"""

import sys
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.edge.options import Options as EdgeOptions
from selenium.webdriver.safari.options import Options as SafariOptions

def test_chrome():
    """测试Chrome浏览器"""
    print("🔍 测试Chrome浏览器...")
    try:
        options = ChromeOptions()
        options.add_argument('--headless')  # 无头模式
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=options)
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ Chrome测试成功: {title}")
        return True
    except Exception as e:
        print(f"❌ Chrome测试失败: {e}")
        return False

def test_firefox():
    """测试Firefox浏览器"""
    print("🔍 测试Firefox浏览器...")
    try:
        options = FirefoxOptions()
        options.add_argument('--headless')  # 无头模式
        
        driver = webdriver.Firefox(options=options)
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ Firefox测试成功: {title}")
        return True
    except Exception as e:
        print(f"❌ Firefox测试失败: {e}")
        return False

def test_edge():
    """测试Edge浏览器"""
    print("🔍 测试Edge浏览器...")
    try:
        options = EdgeOptions()
        options.add_argument('--headless')  # 无头模式
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Edge(options=options)
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ Edge测试成功: {title}")
        return True
    except Exception as e:
        print(f"❌ Edge测试失败: {e}")
        return False

def test_safari():
    """测试Safari浏览器"""
    print("🔍 测试Safari浏览器...")
    try:
        # Safari不支持无头模式
        driver = webdriver.Safari()
        driver.get("https://www.google.com")
        title = driver.title
        time.sleep(2)  # 给用户看到浏览器窗口
        driver.quit()
        
        print(f"✅ Safari测试成功: {title}")
        return True
    except Exception as e:
        print(f"❌ Safari测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始浏览器兼容性测试...")
    print("=" * 50)
    
    browsers = [
        ("Chrome", test_chrome),
        ("Firefox", test_firefox), 
        ("Edge", test_edge),
        ("Safari", test_safari)
    ]
    
    working_browsers = []
    
    for browser_name, test_func in browsers:
        if test_func():
            working_browsers.append(browser_name)
        print("-" * 30)
    
    print("\n📊 测试结果总结:")
    print("=" * 50)
    
    if working_browsers:
        print(f"✅ 可用的浏览器: {', '.join(working_browsers)}")
        print(f"\n💡 建议使用: {working_browsers[0]}")
        
        # 更新配置文件建议
        browser_mapping = {
            "Chrome": "chrome",
            "Firefox": "firefox", 
            "Edge": "edge",
            "Safari": "safari"
        }
        
        recommended_config = browser_mapping.get(working_browsers[0], "chrome")
        print(f"📝 配置文件设置建议: type: \"{recommended_config}\"")
        
    else:
        print("❌ 没有可用的浏览器")
        print("💡 建议:")
        print("   1. 安装Chrome浏览器和对应版本的ChromeDriver")
        print("   2. 或者启用Safari的远程自动化功能")
        print("   3. 或者安装Firefox浏览器和GeckoDriver")

if __name__ == "__main__":
    main()
