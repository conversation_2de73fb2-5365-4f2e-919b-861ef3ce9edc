#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批处理日志记录模块
提供结构化的日志记录功能
"""

import logging
import logging.handlers
import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional


class BatchLogger:
    """批处理日志记录器"""
    
    def __init__(self, name: str = "batch_processor", log_dir: Optional[str] = None, config: Optional[Dict[str, Any]] = None):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
            log_dir: 日志文件目录
            config: 日志配置字典
        """
        self.name = name
        
        # 设置日志目录
        if log_dir is None:
            self.log_dir = Path(__file__).parent.parent / "logs"
        else:
            self.log_dir = Path(log_dir)
        
        # 确保日志目录存在
        self.log_dir.mkdir(exist_ok=True)
        
        # 默认配置
        default_config = {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_max_size_mb': 100,
            'backup_count': 5
        }
        
        # 合并配置
        self.config = {**default_config, **(config or {})}
        
        # 创建日志记录器
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(self.name)
        logger.setLevel(getattr(logging, self.config['level'].upper()))
        
        # 清除现有的处理器
        logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(self.config['format'])
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器（轮转）
        log_file = self.log_dir / f"{self.name}.log"
        max_bytes = self.config['file_max_size_mb'] * 1024 * 1024
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=self.config['backup_count'],
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, self.config['level'].upper()))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_log_file = self.log_dir / f"{self.name}_error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=max_bytes,
            backupCount=self.config['backup_count'],
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        logger.addHandler(error_handler)
        
        return logger
    
    def info(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录信息日志"""
        self._log_with_extra(logging.INFO, message, extra_data)
    
    def warning(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录警告日志"""
        self._log_with_extra(logging.WARNING, message, extra_data)
    
    def error(self, message: str, extra_data: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """记录错误日志"""
        self._log_with_extra(logging.ERROR, message, extra_data, exc_info)
    
    def debug(self, message: str, extra_data: Optional[Dict[str, Any]] = None):
        """记录调试日志"""
        self._log_with_extra(logging.DEBUG, message, extra_data)
    
    def _log_with_extra(self, level: int, message: str, extra_data: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """带额外数据的日志记录"""
        if extra_data:
            # 将额外数据转换为JSON字符串
            extra_str = json.dumps(extra_data, ensure_ascii=False, default=str)
            full_message = f"{message} | Extra: {extra_str}"
        else:
            full_message = message
        
        self.logger.log(level, full_message, exc_info=exc_info)
    
    def log_execution_start(self, data_type: str, force_regenerate: bool = False):
        """记录执行开始"""
        self.info(
            f"开始执行数据比较任务",
            {
                'data_type': data_type,
                'force_regenerate': force_regenerate,
                'start_time': datetime.now().isoformat()
            }
        )
    
    def log_execution_end(self, data_type: str, success: bool, duration_seconds: float, output_file: Optional[str] = None):
        """记录执行结束"""
        level = logging.INFO if success else logging.ERROR
        message = f"数据比较任务{'成功' if success else '失败'}"
        
        extra_data = {
            'data_type': data_type,
            'success': success,
            'duration_seconds': duration_seconds,
            'end_time': datetime.now().isoformat()
        }
        
        if output_file:
            extra_data['output_file'] = output_file
        
        self._log_with_extra(level, message, extra_data)
    
    def log_oss_upload_start(self, local_file: str, oss_key: str):
        """记录OSS上传开始"""
        self.info(
            f"开始上传文件到OSS",
            {
                'local_file': local_file,
                'oss_key': oss_key,
                'upload_start_time': datetime.now().isoformat()
            }
        )
    
    def log_oss_upload_end(self, local_file: str, oss_key: str, success: bool, duration_seconds: float, error_msg: Optional[str] = None):
        """记录OSS上传结束"""
        level = logging.INFO if success else logging.ERROR
        message = f"OSS文件上传{'成功' if success else '失败'}"
        
        extra_data = {
            'local_file': local_file,
            'oss_key': oss_key,
            'success': success,
            'duration_seconds': duration_seconds,
            'upload_end_time': datetime.now().isoformat()
        }
        
        if error_msg:
            extra_data['error_message'] = error_msg
        
        self._log_with_extra(level, message, extra_data)
    
    def log_system_info(self, info: Dict[str, Any]):
        """记录系统信息"""
        self.info("系统信息", info)
    
    def log_config_info(self, config_type: str, config_data: Dict[str, Any]):
        """记录配置信息（敏感信息会被屏蔽）"""
        # 屏蔽敏感信息
        safe_config = self._mask_sensitive_data(config_data.copy())
        self.info(f"{config_type}配置信息", safe_config)
    
    def _mask_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """屏蔽敏感数据"""
        sensitive_keys = ['access_key_id', 'access_key_secret', 'password', 'token']
        
        for key, value in data.items():
            if isinstance(value, dict):
                data[key] = self._mask_sensitive_data(value)
            elif any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                if isinstance(value, str) and len(value) > 4:
                    data[key] = value[:2] + '*' * (len(value) - 4) + value[-2:]
                else:
                    data[key] = '***'
        
        return data
