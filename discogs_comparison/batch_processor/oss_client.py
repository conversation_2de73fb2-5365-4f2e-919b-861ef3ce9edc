#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云OSS客户端模块
提供文件上传、下载、管理等功能
"""

import os
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, Tuple

try:
    import oss2
    from oss2.exceptions import OssError
except ImportError:
    print("警告: oss2 模块未安装，请运行: pip install oss2")
    oss2 = None
    OssError = Exception


class OSSClient:
    """阿里云OSS客户端"""
    
    def __init__(self, config: Dict[str, Any], logger=None):
        """
        初始化OSS客户端
        
        Args:
            config: OSS配置字典
            logger: 日志记录器实例
        """
        self.config = config
        self.logger = logger
        self.bucket = None
        
        if oss2 is None:
            raise ImportError("oss2 模块未安装，请运行: pip install oss2")
        
        # 验证配置
        self._validate_config()
        
        # 初始化OSS客户端
        self._init_client()
    
    def _validate_config(self):
        """验证OSS配置"""
        required_fields = ['endpoint', 'access_key_id', 'access_key_secret', 'bucket_name']
        for field in required_fields:
            if not self.config.get(field):
                raise ValueError(f"OSS配置缺少必需字段: {field}")
    
    def _init_client(self):
        """初始化OSS客户端"""
        try:
            # 创建认证对象
            auth = oss2.Auth(
                self.config['access_key_id'],
                self.config['access_key_secret']
            )
            
            # 创建Bucket对象
            self.bucket = oss2.Bucket(
                auth,
                self.config['endpoint'],
                self.config['bucket_name'],
                connect_timeout=self.config.get('connection_timeout', 30),
                enable_crc=self.config.get('enable_crc', True)
            )
            
            # 测试连接
            self._test_connection()
            
            if self.logger:
                self.logger.info("OSS客户端初始化成功")
                
        except Exception as e:
            error_msg = f"OSS客户端初始化失败: {e}"
            if self.logger:
                self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    def _test_connection(self):
        """测试OSS连接"""
        try:
            # 尝试列出bucket信息来测试连接
            self.bucket.get_bucket_info()
        except OssError as e:
            raise RuntimeError(f"OSS连接测试失败: {e}")
    
    def upload_file(self, local_file_path: str, oss_key: Optional[str] = None, 
                   overwrite: bool = True) -> Tuple[bool, Optional[str]]:
        """
        上传文件到OSS
        
        Args:
            local_file_path: 本地文件路径
            oss_key: OSS对象键名，如果为None则自动生成
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        local_path = Path(local_file_path)
        
        # 检查本地文件是否存在
        if not local_path.exists():
            error_msg = f"本地文件不存在: {local_file_path}"
            if self.logger:
                self.logger.error(error_msg)
            return False, error_msg
        
        # 生成OSS键名
        if oss_key is None:
            oss_key = self._generate_oss_key(local_path)
        
        try:
            start_time = time.time()
            
            if self.logger:
                self.logger.log_oss_upload_start(str(local_path), oss_key)
            
            # 检查文件是否已存在
            if not overwrite and self.object_exists(oss_key):
                error_msg = f"OSS对象已存在且不允许覆盖: {oss_key}"
                if self.logger:
                    self.logger.warning(error_msg)
                return False, error_msg
            
            # 获取文件大小
            file_size = local_path.stat().st_size
            
            # 根据文件大小选择上传方式
            if file_size > self.config.get('multipart_threshold', 100 * 1024 * 1024):
                # 大文件使用分片上传
                result = self._multipart_upload(str(local_path), oss_key)
            else:
                # 小文件使用简单上传
                result = self.bucket.put_object_from_file(oss_key, str(local_path))
            
            duration = time.time() - start_time
            
            if self.logger:
                self.logger.log_oss_upload_end(
                    str(local_path), oss_key, True, duration
                )
                self.logger.info(f"文件上传成功: {oss_key} (耗时: {duration:.2f}秒)")
            
            return True, None
            
        except OssError as e:
            duration = time.time() - start_time
            error_msg = f"OSS上传失败: {e}"
            
            if self.logger:
                self.logger.log_oss_upload_end(
                    str(local_path), oss_key, False, duration, error_msg
                )
            
            return False, error_msg
        
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"上传过程中发生未知错误: {e}"
            
            if self.logger:
                self.logger.log_oss_upload_end(
                    str(local_path), oss_key, False, duration, error_msg
                )
            
            return False, error_msg
    
    def _multipart_upload(self, local_file_path: str, oss_key: str):
        """分片上传大文件"""
        part_size = self.config.get('part_size', 10 * 1024 * 1024)  # 默认10MB
        
        # 初始化分片上传
        upload_id = self.bucket.init_multipart_upload(oss_key).upload_id
        
        parts = []
        part_number = 1
        
        try:
            with open(local_file_path, 'rb') as f:
                while True:
                    data = f.read(part_size)
                    if not data:
                        break
                    
                    # 上传分片
                    result = self.bucket.upload_part(oss_key, upload_id, part_number, data)
                    parts.append(oss2.models.PartInfo(part_number, result.etag))
                    part_number += 1
            
            # 完成分片上传
            result = self.bucket.complete_multipart_upload(oss_key, upload_id, parts)
            return result
            
        except Exception as e:
            # 取消分片上传
            self.bucket.abort_multipart_upload(oss_key, upload_id)
            raise e
    
    def _generate_oss_key(self, local_path: Path) -> str:
        """生成OSS对象键名"""
        # 获取配置的路径前缀
        prefix = self.config.get('upload_path_prefix', 'discogs_comparison/')
        
        # 确保前缀以/结尾
        if prefix and not prefix.endswith('/'):
            prefix += '/'
        
        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{local_path.name}"
        
        return f"{prefix}{filename}"
    
    def object_exists(self, oss_key: str) -> bool:
        """检查OSS对象是否存在"""
        try:
            self.bucket.head_object(oss_key)
            return True
        except OssError as e:
            if e.status == 404:
                return False
            raise e
    
    def delete_object(self, oss_key: str) -> Tuple[bool, Optional[str]]:
        """删除OSS对象"""
        try:
            self.bucket.delete_object(oss_key)
            if self.logger:
                self.logger.info(f"OSS对象删除成功: {oss_key}")
            return True, None
        except OssError as e:
            error_msg = f"删除OSS对象失败: {e}"
            if self.logger:
                self.logger.error(error_msg)
            return False, error_msg
    
    def list_objects(self, prefix: str = '', max_keys: int = 100) -> list:
        """列出OSS对象"""
        try:
            result = self.bucket.list_objects_v2(prefix=prefix, max_keys=max_keys)
            return [obj.key for obj in result.object_list]
        except OssError as e:
            if self.logger:
                self.logger.error(f"列出OSS对象失败: {e}")
            return []
    
    def get_object_info(self, oss_key: str) -> Optional[Dict[str, Any]]:
        """获取OSS对象信息"""
        try:
            result = self.bucket.head_object(oss_key)
            return {
                'key': oss_key,
                'size': result.content_length,
                'last_modified': result.last_modified,
                'etag': result.etag,
                'content_type': result.content_type
            }
        except OssError as e:
            if self.logger:
                self.logger.error(f"获取OSS对象信息失败: {e}")
            return None
    
    def generate_presigned_url(self, oss_key: str, expires: int = 3600) -> Optional[str]:
        """生成预签名URL"""
        try:
            url = self.bucket.sign_url('GET', oss_key, expires)
            return url
        except OssError as e:
            if self.logger:
                self.logger.error(f"生成预签名URL失败: {e}")
            return None
