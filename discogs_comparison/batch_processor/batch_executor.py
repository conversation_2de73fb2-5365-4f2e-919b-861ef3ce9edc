#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批处理执行器模块
负责执行数据比较任务和文件上传
"""

import os
import sys
import time
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from get_diff import DiffAPI
from .config import BatchConfig
from .oss_client import OSSClient
from .logger import BatchLogger


class BatchExecutor:
    """批处理执行器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化批处理执行器
        
        Args:
            config_dir: 配置文件目录
        """
        # 初始化配置
        self.config = BatchConfig(config_dir)
        
        # 初始化日志记录器
        self.logger = BatchLogger(
            name="batch_executor",
            config=self.config.get_log_config()
        )
        
        # 初始化DiffAPI
        self.diff_api = DiffAPI()
        
        # 初始化OSS客户端
        self.oss_client = None
        if self.config.validate_oss_config():
            try:
                self.oss_client = OSSClient(
                    self.config.get_oss_config(),
                    self.logger
                )
            except Exception as e:
                self.logger.error(f"OSS客户端初始化失败: {e}")
        else:
            self.logger.warning("OSS配置不完整，将跳过文件上传")
        
        # 记录系统信息
        self._log_system_info()
    
    def _log_system_info(self):
        """记录系统信息"""
        import platform
        import psutil

        # 获取磁盘使用信息（跨平台兼容）
        try:
            if platform.system() == 'Windows':
                # Windows下获取当前驱动器的磁盘使用情况
                import os
                current_drive = os.path.splitdrive(os.getcwd())[0] + os.sep
                disk_free_gb = round(shutil.disk_usage(current_drive).free / (1024**3), 2)
            else:
                # Linux/Unix下获取当前目录的磁盘使用情况
                disk_free_gb = round(shutil.disk_usage('.').free / (1024**3), 2)
        except Exception:
            disk_free_gb = 0

        system_info = {
            'platform': platform.platform(),
            'system': platform.system(),
            'python_version': platform.python_version(),
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
            'disk_free_gb': disk_free_gb,
            'working_directory': str(Path.cwd()),
            'is_windows': platform.system() == 'Windows'
        }

        self.logger.log_system_info(system_info)
        self.logger.log_config_info("批处理", self.config.get_batch_config())
        self.logger.log_config_info("OSS", self.config.get_oss_config())
    
    def execute_single_task(self, data_type: str, force_regenerate: bool = False, 
                           upload_to_oss: bool = True) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        执行单个数据比较任务
        
        Args:
            data_type: 数据类型
            force_regenerate: 是否强制重新生成
            upload_to_oss: 是否上传到OSS
            
        Returns:
            Tuple[bool, Optional[str], Optional[str]]: (是否成功, 本地文件路径, OSS键名)
        """
        start_time = time.time()
        
        # 验证数据类型
        if data_type not in self.config.get_supported_data_types():
            error_msg = f"不支持的数据类型: {data_type}"
            self.logger.error(error_msg)
            return False, None, None
        
        self.logger.log_execution_start(data_type, force_regenerate)
        
        try:
            # 执行数据比较
            local_file_path = self._execute_diff_with_retry(data_type, force_regenerate)
            
            if not local_file_path:
                duration = time.time() - start_time
                self.logger.log_execution_end(data_type, False, duration)
                return False, None, None
            
            # 备份本地文件
            backup_path = self._backup_local_file(local_file_path)
            if backup_path:
                self.logger.info(f"本地文件已备份: {backup_path}")
            
            # 上传到OSS
            oss_key = None
            if upload_to_oss and self.oss_client:
                success, error_msg = self.oss_client.upload_file(local_file_path)
                if success:
                    # 生成OSS键名用于返回
                    oss_key = self.oss_client._generate_oss_key(Path(local_file_path))
                    self.logger.info(f"文件已成功上传到OSS: {oss_key}")
                else:
                    self.logger.error(f"OSS上传失败: {error_msg}")
            elif upload_to_oss:
                self.logger.warning("OSS客户端未初始化，跳过文件上传")
            
            duration = time.time() - start_time
            self.logger.log_execution_end(data_type, True, duration, local_file_path)
            
            return True, local_file_path, oss_key
            
        except Exception as e:
            duration = time.time() - start_time
            self.logger.error(f"执行任务时发生未知错误: {e}", exc_info=True)
            self.logger.log_execution_end(data_type, False, duration)
            return False, None, None
    
    def _execute_diff_with_retry(self, data_type: str, force_regenerate: bool) -> Optional[str]:
        """带重试机制的数据比较执行"""
        execution_config = self.config.get_execution_config()
        max_attempts = execution_config.get('retry_attempts', 3)
        retry_delay = execution_config.get('retry_delay_seconds', 60)
        
        for attempt in range(1, max_attempts + 1):
            try:
                self.logger.info(f"开始第 {attempt} 次尝试执行数据比较")
                
                # 执行数据比较
                result = self.diff_api.get_diff(data_type, force_regenerate)
                
                if result:
                    self.logger.info(f"数据比较成功完成，输出文件: {result}")
                    return result
                else:
                    self.logger.error(f"第 {attempt} 次尝试失败：数据比较返回空结果")
                    
            except Exception as e:
                self.logger.error(f"第 {attempt} 次尝试失败: {e}", exc_info=True)
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < max_attempts:
                self.logger.info(f"等待 {retry_delay} 秒后进行第 {attempt + 1} 次尝试")
                time.sleep(retry_delay)
        
        self.logger.error(f"所有 {max_attempts} 次尝试均失败")
        return None
    
    def _backup_local_file(self, file_path: str) -> Optional[str]:
        """备份本地文件"""
        if not self.config.get_batch_config('output.local_backup'):
            return None
        
        try:
            source_path = Path(file_path)
            if not source_path.exists():
                return None
            
            # 创建备份目录
            backup_dir = source_path.parent / "backup"
            backup_dir.mkdir(exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{source_path.stem}_{timestamp}{source_path.suffix}"
            backup_path = backup_dir / backup_filename
            
            # 复制文件
            shutil.copy2(source_path, backup_path)
            
            # 清理旧备份
            self._cleanup_old_backups(backup_dir)
            
            return str(backup_path)
            
        except Exception as e:
            self.logger.error(f"备份文件失败: {e}")
            return None
    
    def _cleanup_old_backups(self, backup_dir: Path):
        """清理旧备份文件"""
        try:
            backup_days = self.config.get_batch_config('output.backup_days') or 7
            cutoff_date = datetime.now() - timedelta(days=backup_days)
            
            for backup_file in backup_dir.glob("*"):
                if backup_file.is_file():
                    file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        backup_file.unlink()
                        self.logger.debug(f"删除旧备份文件: {backup_file}")
                        
        except Exception as e:
            self.logger.error(f"清理旧备份文件失败: {e}")
    
    def execute_all_tasks(self, force_regenerate: bool = False, 
                         upload_to_oss: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        执行所有数据类型的比较任务
        
        Args:
            force_regenerate: 是否强制重新生成
            upload_to_oss: 是否上传到OSS
            
        Returns:
            Dict[str, Dict[str, Any]]: 每个数据类型的执行结果
        """
        results = {}
        data_types = self.config.get_supported_data_types()
        
        self.logger.info(f"开始执行所有数据比较任务，共 {len(data_types)} 个类型")
        
        for data_type in data_types:
            self.logger.info(f"正在处理数据类型: {data_type}")
            
            success, local_file, oss_key = self.execute_single_task(
                data_type, force_regenerate, upload_to_oss
            )
            
            results[data_type] = {
                'success': success,
                'local_file': local_file,
                'oss_key': oss_key
            }
            
            if success:
                self.logger.info(f"数据类型 {data_type} 处理成功")
            else:
                self.logger.error(f"数据类型 {data_type} 处理失败")
        
        # 统计结果
        success_count = sum(1 for result in results.values() if result['success'])
        total_count = len(results)
        
        self.logger.info(f"所有任务执行完成: {success_count}/{total_count} 成功")
        
        return results
    
    def get_status_report(self) -> Dict[str, Any]:
        """获取状态报告"""
        try:
            # 获取diff文件状态
            diff_status = []
            for data_type in self.config.get_supported_data_types():
                config = self.diff_api.config[data_type]
                script_dir = self.diff_api.base_dir / config['script_dir']
                output_file = script_dir / config['output_file']
                
                if output_file.exists():
                    stat = output_file.stat()
                    size_mb = stat.st_size / 1024 / 1024
                    mtime = datetime.fromtimestamp(stat.st_mtime)
                    
                    diff_status.append({
                        'type': data_type,
                        'exists': True,
                        'file_path': str(output_file),
                        'size_mb': round(size_mb, 2),
                        'modified_time': mtime.isoformat(),
                        'age_hours': round((datetime.now() - mtime).total_seconds() / 3600, 1)
                    })
                else:
                    diff_status.append({
                        'type': data_type,
                        'exists': False,
                        'file_path': str(output_file)
                    })
            
            return {
                'timestamp': datetime.now().isoformat(),
                'diff_files': diff_status,
                'oss_configured': self.oss_client is not None,
                'supported_types': self.config.get_supported_data_types()
            }
            
        except Exception as e:
            self.logger.error(f"获取状态报告失败: {e}")
            return {'error': str(e)}
