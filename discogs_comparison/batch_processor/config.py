#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批处理配置管理模块
负责加载和管理批处理相关的配置信息
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional


class BatchConfig:
    """批处理配置管理类"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为项目根目录下的config文件夹
        """
        if config_dir is None:
            # 默认配置目录为项目根目录下的config文件夹
            self.config_dir = Path(__file__).parent.parent / "config"
        else:
            self.config_dir = Path(config_dir)
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.batch_config_file = self.config_dir / "batch_config.yaml"
        self.oss_config_file = self.config_dir / "oss_config.yaml"
        
        # 加载配置
        self.batch_config = self._load_batch_config()
        self.oss_config = self._load_oss_config()
    
    def _load_batch_config(self) -> Dict[str, Any]:
        """加载批处理配置"""
        default_config = {
            'execution': {
                'timeout_seconds': 7200,  # 2小时超时
                'retry_attempts': 3,
                'retry_delay_seconds': 60
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file_max_size_mb': 100,
                'backup_count': 5
            },
            'data_types': ['release', 'master', 'artists', 'label'],
            'output': {
                'local_backup': True,
                'backup_days': 7
            }
        }
        
        if self.batch_config_file.exists():
            try:
                with open(self.batch_config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    # 合并默认配置和用户配置
                    return self._merge_config(default_config, config)
            except Exception as e:
                print(f"警告: 加载批处理配置文件失败: {e}")
                print("使用默认配置")
        
        # 创建默认配置文件
        self._save_config(self.batch_config_file, default_config)
        return default_config
    
    def _load_oss_config(self) -> Dict[str, Any]:
        """加载OSS配置"""
        default_config = {
            'endpoint': '',  # 需要用户填写
            'access_key_id': '',  # 需要用户填写
            'access_key_secret': '',  # 需要用户填写
            'bucket_name': '',  # 需要用户填写
            'upload_path_prefix': 'discogs_comparison/',
            'connection_timeout': 30,
            'enable_crc': True,
            'multipart_threshold': 100 * 1024 * 1024,  # 100MB
            'part_size': 10 * 1024 * 1024  # 10MB
        }
        
        if self.oss_config_file.exists():
            try:
                with open(self.oss_config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    return self._merge_config(default_config, config)
            except Exception as e:
                print(f"警告: 加载OSS配置文件失败: {e}")
                print("使用默认配置")
        
        # 创建默认配置文件
        self._save_config(self.oss_config_file, default_config)
        return default_config
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """递归合并配置字典"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def _save_config(self, file_path: Path, config: Dict[str, Any]):
        """保存配置到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            print(f"配置文件已创建: {file_path}")
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get_batch_config(self, key: str = None) -> Any:
        """获取批处理配置"""
        if key is None:
            return self.batch_config
        
        keys = key.split('.')
        value = self.batch_config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return None
        return value
    
    def get_oss_config(self, key: str = None) -> Any:
        """获取OSS配置"""
        if key is None:
            return self.oss_config
        
        keys = key.split('.')
        value = self.oss_config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return None
        return value
    
    def validate_oss_config(self) -> bool:
        """验证OSS配置是否完整"""
        required_fields = ['endpoint', 'access_key_id', 'access_key_secret', 'bucket_name']
        for field in required_fields:
            if not self.get_oss_config(field):
                return False
        return True
    
    def get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get_batch_config('logging') or {}
    
    def get_execution_config(self) -> Dict[str, Any]:
        """获取执行配置"""
        return self.get_batch_config('execution') or {}
    
    def get_supported_data_types(self) -> list:
        """获取支持的数据类型列表"""
        return self.get_batch_config('data_types') or []
