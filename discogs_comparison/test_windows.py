#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Windows兼容性测试脚本
验证批处理系统在Windows环境下的兼容性
"""

import sys
import os
import platform
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def test_platform_detection():
    """测试平台检测"""
    print("🔍 测试平台检测...")
    
    system = platform.system()
    print(f"   操作系统: {system}")
    print(f"   平台信息: {platform.platform()}")
    print(f"   Python版本: {platform.python_version()}")
    print(f"   Python路径: {sys.executable}")
    
    if system == 'Windows':
        print("✅ Windows平台检测成功")
        return True
    else:
        print(f"⚠️  当前平台: {system} (非Windows)")
        return True  # 仍然返回True，因为脚本应该跨平台兼容


def test_path_handling():
    """测试路径处理"""
    print("\n🔍 测试路径处理...")
    
    try:
        # 测试pathlib.Path的跨平台兼容性
        current_dir = Path.cwd()
        config_dir = current_dir / "config"
        logs_dir = current_dir / "logs"
        
        print(f"   当前目录: {current_dir}")
        print(f"   配置目录: {config_dir}")
        print(f"   日志目录: {logs_dir}")
        
        # 测试路径存在性检查
        print(f"   配置目录存在: {config_dir.exists()}")
        print(f"   日志目录存在: {logs_dir.exists()}")
        
        # 测试路径字符串转换
        if platform.system() == 'Windows':
            # Windows路径应该包含反斜杠
            config_str = str(config_dir)
            if '\\' in config_str or ':' in config_str:
                print("✅ Windows路径格式正确")
            else:
                print("⚠️  Windows路径格式异常")
        
        print("✅ 路径处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 路径处理测试失败: {e}")
        return False


def test_virtual_environment():
    """测试虚拟环境检测"""
    print("\n🔍 测试虚拟环境...")
    
    try:
        # 检查是否在虚拟环境中
        in_venv = hasattr(sys, 'real_prefix') or (
            hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
        )
        
        print(f"   在虚拟环境中: {in_venv}")
        print(f"   Python前缀: {sys.prefix}")
        
        if hasattr(sys, 'base_prefix'):
            print(f"   基础前缀: {sys.base_prefix}")
        
        # 检查虚拟环境目录结构
        venv_dir = Path("venv")
        if venv_dir.exists():
            print(f"   虚拟环境目录存在: {venv_dir}")
            
            if platform.system() == 'Windows':
                scripts_dir = venv_dir / "Scripts"
                activate_script = scripts_dir / "activate.bat"
                python_exe = scripts_dir / "python.exe"
            else:
                scripts_dir = venv_dir / "bin"
                activate_script = scripts_dir / "activate"
                python_exe = scripts_dir / "python"
            
            print(f"   脚本目录存在: {scripts_dir.exists()}")
            print(f"   激活脚本存在: {activate_script.exists()}")
            print(f"   Python可执行文件存在: {python_exe.exists()}")
        
        print("✅ 虚拟环境检测完成")
        return True
        
    except Exception as e:
        print(f"❌ 虚拟环境测试失败: {e}")
        return False


def test_python_command_detection():
    """测试Python命令检测"""
    print("\n🔍 测试Python命令检测...")
    
    try:
        import subprocess
        
        commands = ['python', 'python3', 'py']
        available_commands = []
        
        for cmd in commands:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    version = result.stdout.strip() or result.stderr.strip()
                    available_commands.append((cmd, version))
                    print(f"   ✅ {cmd}: {version}")
                else:
                    print(f"   ❌ {cmd}: 不可用")
            except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
                print(f"   ❌ {cmd}: 未找到")
        
        if available_commands:
            print(f"✅ 找到 {len(available_commands)} 个可用的Python命令")
            return True
        else:
            print("❌ 未找到可用的Python命令")
            return False
            
    except Exception as e:
        print(f"❌ Python命令检测失败: {e}")
        return False


def test_file_operations():
    """测试文件操作"""
    print("\n🔍 测试文件操作...")
    
    try:
        # 测试目录创建
        test_dir = Path("test_temp")
        test_dir.mkdir(exist_ok=True)
        print(f"   目录创建: {test_dir.exists()}")
        
        # 测试文件写入
        test_file = test_dir / "test.txt"
        test_content = "Windows兼容性测试\n测试中文内容"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"   文件写入: {test_file.exists()}")
        
        # 测试文件读取
        with open(test_file, 'r', encoding='utf-8') as f:
            read_content = f.read()
        
        print(f"   文件读取: {read_content == test_content}")
        
        # 清理测试文件
        test_file.unlink()
        test_dir.rmdir()
        
        print("✅ 文件操作测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False


def test_imports():
    """测试模块导入"""
    print("\n🔍 测试模块导入...")
    
    required_modules = [
        ('pathlib', 'pathlib (内置)'),
        ('platform', 'platform (内置)'),
        ('subprocess', 'subprocess (内置)'),
        ('json', 'json (内置)'),
        ('datetime', 'datetime (内置)'),
        ('argparse', 'argparse (内置)')
    ]
    
    optional_modules = [
        ('yaml', 'PyYAML'),
        ('oss2', 'oss2'),
        ('psutil', 'psutil')
    ]
    
    # 测试必需模块
    missing_required = []
    for module, display_name in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {display_name}")
        except ImportError:
            print(f"   ❌ {display_name}")
            missing_required.append(display_name)
    
    # 测试可选模块
    missing_optional = []
    for module, display_name in optional_modules:
        try:
            __import__(module)
            print(f"   ✅ {display_name}")
        except ImportError:
            print(f"   ⚠️  {display_name} (可选)")
            missing_optional.append(display_name)
    
    if missing_required:
        print(f"❌ 缺失必需模块: {missing_required}")
        return False
    
    if missing_optional:
        print(f"⚠️  缺失可选模块: {missing_optional}")
        print("   请运行: pip install -r requirements.txt")
    
    print("✅ 模块导入测试通过")
    return True


def test_batch_processor_import():
    """测试批处理模块导入"""
    print("\n🔍 测试批处理模块导入...")
    
    try:
        # 测试批处理模块导入
        from batch_processor import BatchConfig, BatchLogger, BatchExecutor
        print("   ✅ 批处理核心模块导入成功")
        
        # 测试配置管理
        config = BatchConfig()
        print("   ✅ 配置管理器初始化成功")
        
        # 测试日志系统
        logger = BatchLogger("test_windows")
        print("   ✅ 日志系统初始化成功")
        
        print("✅ 批处理模块导入测试通过")
        return True
        
    except ImportError as e:
        print(f"❌ 批处理模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 批处理模块测试失败: {e}")
        return False


def test_windows_specific_features():
    """测试Windows特定功能"""
    print("\n🔍 测试Windows特定功能...")
    
    if platform.system() != 'Windows':
        print("⚠️  非Windows系统，跳过Windows特定功能测试")
        return True
    
    try:
        # 测试Windows路径
        import os
        current_drive = os.path.splitdrive(os.getcwd())[0]
        print(f"   当前驱动器: {current_drive}")
        
        # 测试环境变量
        username = os.environ.get('USERNAME', 'Unknown')
        computername = os.environ.get('COMPUTERNAME', 'Unknown')
        print(f"   用户名: {username}")
        print(f"   计算机名: {computername}")
        
        # 测试Windows命令
        try:
            import subprocess
            result = subprocess.run(['where', 'python'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                python_paths = result.stdout.strip().split('\n')
                print(f"   Python路径: {python_paths[0] if python_paths else 'Unknown'}")
        except:
            print("   ⚠️  无法检测Python路径")
        
        print("✅ Windows特定功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Windows特定功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 Windows兼容性测试")
    print("=" * 50)
    
    tests = [
        ("平台检测", test_platform_detection),
        ("路径处理", test_path_handling),
        ("虚拟环境", test_virtual_environment),
        ("Python命令检测", test_python_command_detection),
        ("文件操作", test_file_operations),
        ("模块导入", test_imports),
        ("批处理模块", test_batch_processor_import),
        ("Windows特定功能", test_windows_specific_features)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_name} 时发生异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Windows兼容性测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统在Windows环境下兼容性良好。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查上述错误信息。")
        return 1


if __name__ == '__main__':
    sys.exit(main())
