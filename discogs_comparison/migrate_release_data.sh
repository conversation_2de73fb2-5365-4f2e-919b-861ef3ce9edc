#!/bin/bash
# MongoDB数据迁移脚本启动器

echo "========================================"
echo "MongoDB数据迁移脚本"
echo "========================================"
echo

echo "⚠️  警告: 此脚本将迁移MongoDB中的数据！"
echo "📋 源集合: release_copy"
echo "📋 目标集合: release_new"
echo "📄 筛选条件: status != 429"
echo

echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请确保Python3已安装"
    exit 1
fi

echo "✅ Python环境检查通过"
echo "Python版本: $(python3 --version)"

echo
echo "📦 检查依赖包..."
if ! python3 -c "import pymongo" &> /dev/null; then
    echo "⚠️  pymongo未安装，正在安装依赖..."
    pip3 install pymongo
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败，请手动运行: pip3 install pymongo"
        exit 1
    fi
fi

echo "✅ 依赖检查通过"

echo
echo "⚠️  最后警告: 此操作将永久迁移数据！"
echo "📊 脚本将会："
echo "   1. 从release_copy集合查询status != 429的记录"
echo "   2. 将这些记录插入到release_new集合"
echo "   3. 插入成功后从release_copy集合删除对应记录"
echo "   4. 使用事务确保数据一致性"
echo

read -p "确定要继续吗？(输入 Y 继续，任意其他键取消): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 0
fi

echo
echo "🚀 开始执行数据迁移脚本..."
echo

python3 migrate_release_data.py

if [ $? -eq 0 ]; then
    echo
    echo "✅ 迁移操作完成！"
    echo "📋 详细日志: migrate_release_data.log"
else
    echo
    echo "❌ 迁移操作失败，请查看日志文件 migrate_release_data.log"
    exit 1
fi
