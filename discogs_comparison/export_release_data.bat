@echo off
chcp 65001 >nul
echo ========================================
echo MongoDB数据导出脚本
echo ========================================
echo.

echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 检查依赖包...
python -c "import pymongo" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  pymongo未安装，正在安装依赖...
    pip install pymongo
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请手动运行: pip install pymongo
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查通过

echo.
echo 🚀 开始执行数据导出...
echo 📊 导出目标: release_new集合
echo 📅 日期范围: 2025年7月29日-30日
echo 📄 导出字段: id
echo.

python export_release_data.py

echo.
if errorlevel 1 (
    echo ❌ 导出失败，请查看日志文件 export_release_data.log
) else (
    echo ✅ 导出完成！
    echo 📁 输出文件: release_data_july_29_30.csv
    echo 📋 日志文件: export_release_data.log
)

echo.
echo 按任意键退出...
pause >nul
