# MongoDB数据迁移脚本使用说明

## 概述

此脚本用于将 `release_copy` 集合中 `status != 429` 的数据迁移到 `release_new` 集合中。迁移过程使用事务确保数据一致性，支持批量处理以提高性能。

## 功能特性

- ✅ **安全的事务处理**: 使用MongoDB事务确保数据一致性
- ✅ **批量处理优化**: 支持大量数据的高效迁移
- ✅ **详细的日志记录**: 完整的操作日志和错误追踪
- ✅ **进度监控**: 实时显示迁移进度和处理速度
- ✅ **用户确认机制**: 防止误操作的安全确认
- ✅ **错误处理**: 完善的异常处理和回滚机制

## 文件说明

| 文件名 | 描述 |
|--------|------|
| `migrate_release_data.py` | 主要的Python迁移脚本 |
| `migrate_release_data.bat` | Windows批处理启动器 |
| `migrate_release_data.sh` | Linux/macOS Shell启动器 |
| `migrate_release_data.log` | 执行日志文件（运行后生成） |

## 系统要求

- Python 3.6+
- pymongo 库
- MongoDB 4.0+（支持事务）
- 网络连接到MongoDB服务器

## 使用方法

### Windows系统

1. 双击运行 `migrate_release_data.bat`
2. 按照提示确认操作
3. 等待迁移完成

### Linux/macOS系统

1. 在终端中执行：
   ```bash
   ./migrate_release_data.sh
   ```
2. 按照提示确认操作
3. 等待迁移完成

### 直接运行Python脚本

```bash
python3 migrate_release_data.py
```

## 配置说明

脚本中的主要配置参数：

```python
# MongoDB连接配置
MONGODB_CONFIG = {
    'connection_string': '**********************************************************',
    'database_name': 'music_test',
    'source_collection': 'release_copy',      # 源集合
    'target_collection': 'release_new',       # 目标集合
    'connection_timeout': 30000,
    'server_selection_timeout': 30000
}

# 迁移操作配置
MIGRATION_CONFIG = {
    'batch_size': 1000,                       # 批量处理大小
    'status_filter': {'$ne': 429},           # 筛选条件：status != 429
    'require_confirmation': True,             # 是否需要用户确认
    'max_migration_count': 100000,           # 最大迁移数量限制
    'enable_transaction': True,               # 是否启用事务处理
    'progress_report_interval': 5000          # 进度报告间隔
}
```

## 操作流程

1. **连接验证**: 检查MongoDB连接和集合存在性
2. **数据统计**: 统计符合条件的记录数量
3. **用户确认**: 显示迁移信息并等待用户确认
4. **批量迁移**: 
   - 从源集合查询符合条件的记录
   - 批量插入到目标集合
   - 插入成功后从源集合删除对应记录
   - 使用事务确保操作的原子性
5. **结果报告**: 显示迁移统计信息

## 安全特性

- **事务保护**: 插入和删除操作在同一事务中执行
- **数量验证**: 验证插入和删除的记录数量一致
- **用户确认**: 防止误操作的确认机制
- **数量限制**: 设置最大迁移数量防止意外大量操作
- **详细日志**: 记录所有操作细节便于问题排查

## 日志文件

执行过程中会生成 `migrate_release_data.log` 文件，包含：

- 连接信息
- 迁移进度
- 错误信息
- 性能统计
- 操作结果

## 故障排除

### 常见问题

1. **连接失败**
   - 检查MongoDB服务器是否运行
   - 验证连接字符串和认证信息
   - 确认网络连接

2. **集合不存在**
   - 确认源集合 `release_copy` 存在
   - 确认目标集合 `release_new` 存在

3. **权限问题**
   - 确认数据库用户有读写权限
   - 检查事务权限

4. **依赖问题**
   - 安装pymongo: `pip install pymongo`
   - 检查Python版本

### 性能优化

- 调整 `batch_size` 参数优化批量处理大小
- 根据网络情况调整超时参数
- 监控内存使用情况

## 注意事项

⚠️ **重要警告**:
- 此操作会永久删除源集合中的数据
- 建议在执行前备份重要数据
- 在生产环境中请先在测试环境验证
- 确保有足够的磁盘空间和网络带宽

## 技术支持

如遇问题，请检查：
1. 日志文件 `migrate_release_data.log`
2. MongoDB服务器日志
3. 网络连接状态
4. 系统资源使用情况
