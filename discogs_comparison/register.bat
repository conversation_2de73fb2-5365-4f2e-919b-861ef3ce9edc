@echo off
chcp 65001 >nul
title Discogs 自动注册工具

echo.
echo ========================================
echo    Discogs 自动注册工具
echo ========================================
echo.

REM 检查Python环境
echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python，请确保Python已安装并添加到PATH
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
python --version
echo.

REM 检查是否存在requirements.txt
if exist requirements.txt (
    echo 🔄 检测到requirements.txt，正在检查依赖...
    
    REM 检查关键依赖
    python -c "import selenium" >nul 2>&1
    if %errorlevel% neq 0 (
        echo 📦 正在安装依赖包...
        pip install -r requirements.txt
        if %errorlevel% neq 0 (
            echo ❌ 错误: 依赖安装失败
            echo 请手动运行: pip install -r requirements.txt
            pause
            exit /b 1
        )
        echo ✅ 依赖安装完成
    ) else (
        echo ✅ 依赖检查通过
    )
    echo.
) else (
    echo 🔄 未找到requirements.txt，尝试安装必要依赖...
    pip install selenium webdriver-manager PyYAML
    if %errorlevel% neq 0 (
        echo ❌ 错误: 依赖安装失败
        echo 请手动运行: pip install selenium webdriver-manager PyYAML
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
    echo.
)

REM 检查配置文件
if not exist config\register_config.yaml (
    echo ❌ 错误: 配置文件不存在
    echo 请确保 config\register_config.yaml 文件存在并正确配置
    pause
    exit /b 1
)

echo ✅ 配置文件检查通过
echo.

REM 检查注册脚本
if not exist discogs_auto_register.py (
    echo ❌ 错误: 注册脚本不存在
    echo 请确保 discogs_auto_register.py 文件存在
    pause
    exit /b 1
)

echo ✅ 注册脚本检查通过
echo.

REM 显示菜单
:menu
echo ========================================
echo 请选择操作:
echo.
echo 1. 批量注册所有账号
echo 2. 注册指定账号
echo 3. 试运行模式（验证配置）
echo 4. 查看配置信息
echo 5. 退出
echo.
set /p choice=请输入选项 (1-5): 

if "%choice%"=="1" goto batch_register
if "%choice%"=="2" goto single_register
if "%choice%"=="3" goto dry_run
if "%choice%"=="4" goto show_config
if "%choice%"=="5" goto exit
echo 无效选项，请重新选择
goto menu

:batch_register
echo.
echo 🚀 开始批量注册...
echo.
python discogs_auto_register.py
goto end

:single_register
echo.
set /p username=请输入要注册的用户名: 
if "%username%"=="" (
    echo 用户名不能为空
    goto menu
)
echo.
echo 🚀 开始注册账号: %username%
echo.
python discogs_auto_register.py --account %username%
goto end

:dry_run
echo.
echo 🔍 试运行模式 - 验证配置...
echo.
python discogs_auto_register.py --dry-run
goto end

:show_config
echo.
echo 📋 配置信息:
echo.
if exist config\register_config.yaml (
    type config\register_config.yaml
) else (
    echo 配置文件不存在
)
echo.
pause
goto menu

:end
echo.
echo ========================================
echo 操作完成
echo ========================================
echo.

REM 询问是否继续
set /p continue=是否继续使用? (y/N): 
if /i "%continue%"=="y" goto menu
if /i "%continue%"=="yes" goto menu

:exit
echo.
echo 👋 感谢使用 Discogs 自动注册工具！
echo.
pause
