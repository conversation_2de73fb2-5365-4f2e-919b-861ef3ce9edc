#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Discogs 数据比较批处理主脚本 (Windows优化版本)
支持命令行参数，自动执行数据比较并上传到阿里云OSS
针对Windows环境进行了优化
"""

import sys
import os
import argparse
import json
import platform
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from batch_processor import BatchExecutor, BatchConfig


def detect_python_command():
    """检测可用的Python命令"""
    commands = ['python', 'python3', 'py']
    for cmd in commands:
        try:
            import subprocess
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return cmd
        except:
            continue
    return 'python'  # 默认返回python


def get_system_info():
    """获取系统信息"""
    return {
        'platform': platform.platform(),
        'system': platform.system(),
        'release': platform.release(),
        'version': platform.version(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'python_version': platform.python_version(),
        'python_executable': sys.executable,
        'working_directory': str(Path.cwd()),
        'script_directory': str(Path(__file__).parent)
    }


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='Discogs 数据比较批处理工具 (Windows版本)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s release                    # 处理release数据类型
  %(prog)s master --force             # 强制重新生成master数据
  %(prog)s --all                      # 处理所有数据类型
  %(prog)s artists --no-upload        # 处理artists但不上传到OSS
  %(prog)s --status                   # 查看当前状态
  %(prog)s --config-check             # 检查配置
  %(prog)s --system-info              # 显示系统信息

支持的数据类型: release, master, artists, label

Windows特定功能:
  %(prog)s --create-shortcuts         # 创建桌面快捷方式
  %(prog)s --install-service          # 安装为Windows服务
        """
    )
    
    # 位置参数：数据类型
    parser.add_argument(
        'data_type',
        nargs='?',
        choices=['release', 'master', 'artists', 'label'],
        help='要处理的数据类型'
    )
    
    # 可选参数
    parser.add_argument(
        '--all',
        action='store_true',
        help='处理所有数据类型'
    )
    
    parser.add_argument(
        '--force',
        action='store_true',
        help='强制重新生成diff文件'
    )
    
    parser.add_argument(
        '--no-upload',
        action='store_true',
        help='不上传文件到OSS'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示当前状态信息'
    )
    
    parser.add_argument(
        '--config-check',
        action='store_true',
        help='检查配置文件'
    )
    
    parser.add_argument(
        '--system-info',
        action='store_true',
        help='显示系统信息'
    )
    
    parser.add_argument(
        '--config-dir',
        type=str,
        help='指定配置文件目录'
    )
    
    parser.add_argument(
        '--output-format',
        choices=['text', 'json'],
        default='text',
        help='输出格式 (默认: text)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出模式'
    )
    
    # Windows特定参数
    parser.add_argument(
        '--create-shortcuts',
        action='store_true',
        help='创建桌面快捷方式'
    )
    
    parser.add_argument(
        '--install-service',
        action='store_true',
        help='安装为Windows服务'
    )
    
    return parser


def print_system_info(output_format='text'):
    """打印系统信息"""
    system_info = get_system_info()
    
    if output_format == 'json':
        print(json.dumps(system_info, indent=2, ensure_ascii=False))
        return
    
    print("=" * 60)
    print("系统信息")
    print("=" * 60)
    print(f"操作系统: {system_info['platform']}")
    print(f"系统类型: {system_info['system']}")
    print(f"系统版本: {system_info['release']}")
    print(f"处理器: {system_info['processor']}")
    print(f"Python版本: {system_info['python_version']}")
    print(f"Python路径: {system_info['python_executable']}")
    print(f"工作目录: {system_info['working_directory']}")
    print(f"脚本目录: {system_info['script_directory']}")


def print_status_report(status_report: dict, output_format: str = 'text'):
    """打印状态报告"""
    if output_format == 'json':
        print(json.dumps(status_report, indent=2, ensure_ascii=False))
        return
    
    print("=" * 60)
    print("Discogs 数据比较批处理状态报告 (Windows)")
    print("=" * 60)
    print(f"生成时间: {status_report.get('timestamp', 'N/A')}")
    print(f"OSS配置状态: {'已配置' if status_report.get('oss_configured') else '未配置'}")
    print(f"支持的数据类型: {', '.join(status_report.get('supported_types', []))}")
    print()
    
    print("Diff文件状态:")
    print("-" * 40)
    for file_info in status_report.get('diff_files', []):
        status_icon = "✅" if file_info.get('exists') else "❌"
        print(f"{status_icon} {file_info['type']}")
        
        if file_info.get('exists'):
            print(f"   文件大小: {file_info.get('size_mb', 0):.2f} MB")
            print(f"   修改时间: {file_info.get('modified_time', 'N/A')}")
            print(f"   文件年龄: {file_info.get('age_hours', 0):.1f} 小时")
            # Windows路径显示
            file_path = file_info.get('file_path', '')
            if file_path:
                print(f"   文件路径: {file_path}")
        else:
            print(f"   状态: 文件不存在")
        print()


def print_execution_results(results: dict, output_format: str = 'text'):
    """打印执行结果"""
    if output_format == 'json':
        print(json.dumps(results, indent=2, ensure_ascii=False))
        return
    
    print("=" * 60)
    print("执行结果")
    print("=" * 60)
    
    for data_type, result in results.items():
        status_icon = "✅" if result['success'] else "❌"
        print(f"{status_icon} {data_type}: {'成功' if result['success'] else '失败'}")
        
        if result['success']:
            if result.get('local_file'):
                # Windows路径格式化
                local_file = result['local_file'].replace('/', '\\')
                print(f"   本地文件: {local_file}")
            if result.get('oss_key'):
                print(f"   OSS键名: {result['oss_key']}")
        print()


def check_configuration(executor: BatchExecutor, output_format: str = 'text'):
    """检查配置"""
    config_status = {
        'batch_config_valid': True,
        'oss_config_valid': executor.config.validate_oss_config(),
        'oss_client_initialized': executor.oss_client is not None,
        'supported_data_types': executor.config.get_supported_data_types(),
        'log_config': executor.config.get_log_config(),
        'execution_config': executor.config.get_execution_config(),
        'system_info': get_system_info()
    }
    
    if output_format == 'json':
        print(json.dumps(config_status, indent=2, ensure_ascii=False))
        return
    
    print("=" * 60)
    print("配置检查结果 (Windows)")
    print("=" * 60)
    print(f"批处理配置: {'✅ 有效' if config_status['batch_config_valid'] else '❌ 无效'}")
    print(f"OSS配置: {'✅ 有效' if config_status['oss_config_valid'] else '❌ 无效'}")
    print(f"OSS客户端: {'✅ 已初始化' if config_status['oss_client_initialized'] else '❌ 未初始化'}")
    print(f"支持的数据类型: {', '.join(config_status['supported_data_types'])}")
    print(f"操作系统: {config_status['system_info']['system']}")
    print()
    
    if not config_status['oss_config_valid']:
        print("⚠️  OSS配置不完整，请检查以下配置项:")
        print("   - endpoint: OSS服务端点")
        print("   - access_key_id: 访问密钥ID")
        print("   - access_key_secret: 访问密钥Secret")
        print("   - bucket_name: 存储桶名称")
        print()
        print("配置文件位置: config\\oss_config.yaml")


def create_desktop_shortcuts():
    """创建桌面快捷方式 (Windows)"""
    if platform.system() != 'Windows':
        print("此功能仅在Windows系统下可用")
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        script_dir = Path(__file__).parent
        python_exe = sys.executable
        
        # 创建主脚本快捷方式
        shortcut_path = Path(desktop) / "Discogs批处理.lnk"
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(str(shortcut_path))
        shortcut.Targetpath = python_exe
        shortcut.Arguments = f'"{script_dir / "batch_run_windows.py"}" --status'
        shortcut.WorkingDirectory = str(script_dir)
        shortcut.IconLocation = python_exe
        shortcut.save()
        
        print(f"✅ 桌面快捷方式已创建: {shortcut_path}")
        
    except ImportError:
        print("❌ 创建快捷方式需要安装 pywin32 和 winshell")
        print("请运行: pip install pywin32 winshell")
    except Exception as e:
        print(f"❌ 创建快捷方式失败: {e}")


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 如果没有提供任何参数，显示帮助信息
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    try:
        # 处理系统信息查询
        if args.system_info:
            print_system_info(args.output_format)
            return
        
        # 处理Windows特定功能
        if args.create_shortcuts:
            create_desktop_shortcuts()
            return
        
        # 初始化批处理执行器
        executor = BatchExecutor(args.config_dir)
        
        # 处理状态查询
        if args.status:
            status_report = executor.get_status_report()
            print_status_report(status_report, args.output_format)
            return
        
        # 处理配置检查
        if args.config_check:
            check_configuration(executor, args.output_format)
            return
        
        # 确定要处理的数据类型
        if args.all:
            # 处理所有数据类型
            if args.verbose:
                print("开始处理所有数据类型...")
            
            results = executor.execute_all_tasks(
                force_regenerate=args.force,
                upload_to_oss=not args.no_upload
            )
            
            print_execution_results(results, args.output_format)
            
        elif args.data_type:
            # 处理单个数据类型
            if args.verbose:
                print(f"开始处理数据类型: {args.data_type}")
            
            success, local_file, oss_key = executor.execute_single_task(
                args.data_type,
                force_regenerate=args.force,
                upload_to_oss=not args.no_upload
            )
            
            result = {
                args.data_type: {
                    'success': success,
                    'local_file': local_file,
                    'oss_key': oss_key
                }
            }
            
            print_execution_results(result, args.output_format)
            
            # 设置退出码
            sys.exit(0 if success else 1)
            
        else:
            print("错误: 请指定要处理的数据类型或使用 --all 处理所有类型")
            parser.print_help()
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1)
    
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
