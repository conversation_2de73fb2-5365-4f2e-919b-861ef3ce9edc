#!/bin/bash
# Discogs 数据比较批处理部署脚本
# 适用于 Ubuntu 24.04.2 LTS

set -e  # 遇到错误立即退出

echo "🚀 开始部署 Discogs 数据比较批处理系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到root用户，建议创建专用用户运行批处理任务"
        read -p "是否继续使用root用户？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "请创建专用用户后重新运行此脚本"
            exit 1
        fi
    fi
}

# 检查系统版本
check_system() {
    log_info "检查系统版本..."
    
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测系统版本"
        exit 1
    fi
    
    source /etc/os-release
    
    if [[ "$ID" != "ubuntu" ]]; then
        log_warning "此脚本专为Ubuntu设计，当前系统: $ID"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    log_success "系统检查完成: $PRETTY_NAME"
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    
    sudo apt update
    sudo apt upgrade -y
    
    log_success "系统包更新完成"
}

# 安装必需的系统包
install_system_packages() {
    log_info "安装必需的系统包..."
    
    sudo apt install -y \
        python3 \
        python3-pip \
        python3-venv \
        python3-dev \
        git \
        curl \
        wget \
        htop \
        vim \
        cron \
        logrotate \
        build-essential \
        libssl-dev \
        libffi-dev
    
    log_success "系统包安装完成"
}

# 检查Python版本
check_python() {
    log_info "检查Python版本..."
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_info "Python版本: $python_version"
    
    # 检查是否满足最低版本要求（3.8+）
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        log_success "Python版本满足要求"
    else
        log_error "Python版本过低，需要3.8或更高版本"
        exit 1
    fi
}

# 创建虚拟环境
create_virtual_env() {
    log_info "创建Python虚拟环境..."
    
    if [[ -d "venv" ]]; then
        log_warning "虚拟环境已存在，是否重新创建？"
        read -p "(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf venv
        else
            log_info "跳过虚拟环境创建"
            return
        fi
    fi
    
    python3 -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    log_success "虚拟环境创建完成"
}

# 安装Python依赖
install_python_dependencies() {
    log_info "安装Python依赖..."
    
    if [[ ! -f "requirements.txt" ]]; then
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
    
    source venv/bin/activate
    pip install -r requirements.txt
    
    log_success "Python依赖安装完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p config
    mkdir -p backup
    
    # 设置目录权限
    chmod 755 logs config backup
    
    log_success "目录创建完成"
}

# 设置文件权限
set_permissions() {
    log_info "设置文件权限..."
    
    # 设置脚本可执行权限
    chmod +x batch_run.py
    chmod +x deploy_batch.sh
    
    # 设置配置文件权限（只有所有者可读写）
    chmod 600 config/*.yaml
    
    log_success "文件权限设置完成"
}

# 测试批处理脚本
test_batch_script() {
    log_info "测试批处理脚本..."
    
    source venv/bin/activate
    
    # 测试配置检查
    if python3 batch_run.py --config-check; then
        log_success "批处理脚本测试通过"
    else
        log_warning "批处理脚本测试失败，请检查配置"
    fi
}

# 创建示例crontab配置
create_crontab_example() {
    log_info "创建crontab配置示例..."
    
    cat > crontab_example.txt << 'EOF'
# Discogs 数据比较批处理定时任务示例
# 请根据实际需求修改时间和参数

# 每天凌晨2点执行所有数据类型的比较
0 2 * * * cd /path/to/discogs_comparison && ./venv/bin/python batch_run.py --all >> logs/cron.log 2>&1

# 每周一凌晨3点强制重新生成所有数据
0 3 * * 1 cd /path/to/discogs_comparison && ./venv/bin/python batch_run.py --all --force >> logs/cron.log 2>&1

# 每小时检查一次release数据
0 * * * * cd /path/to/discogs_comparison && ./venv/bin/python batch_run.py release >> logs/cron.log 2>&1

# 使用方法：
# 1. 编辑上述路径为实际项目路径
# 2. 根据需求调整执行时间和参数
# 3. 运行: crontab crontab_example.txt
EOF
    
    log_success "crontab配置示例已创建: crontab_example.txt"
}

# 创建日志轮转配置
create_logrotate_config() {
    log_info "创建日志轮转配置..."
    
    cat > logrotate_discogs << EOF
$(pwd)/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
    postrotate
        # 可以在这里添加日志轮转后的操作
    endscript
}
EOF
    
    # 将配置复制到系统目录（需要sudo权限）
    if sudo cp logrotate_discogs /etc/logrotate.d/; then
        log_success "日志轮转配置已安装"
        rm logrotate_discogs
    else
        log_warning "日志轮转配置安装失败，请手动安装"
    fi
}

# 显示部署完成信息
show_completion_info() {
    log_success "🎉 部署完成！"
    echo
    echo "📋 接下来的步骤："
    echo "1. 编辑OSS配置文件: config/oss_config.yaml"
    echo "2. 根据需要调整批处理配置: config/batch_config.yaml"
    echo "3. 测试批处理脚本:"
    echo "   source venv/bin/activate"
    echo "   python3 batch_run.py --config-check"
    echo "   python3 batch_run.py --status"
    echo "4. 设置定时任务:"
    echo "   编辑 crontab_example.txt 中的路径"
    echo "   crontab crontab_example.txt"
    echo
    echo "📖 更多信息请查看: BATCH_DEPLOYMENT_GUIDE.md"
}

# 主函数
main() {
    echo "Discogs 数据比较批处理部署脚本"
    echo "适用于 Ubuntu 24.04.2 LTS"
    echo "================================"
    echo
    
    check_root
    check_system
    update_system
    install_system_packages
    check_python
    create_virtual_env
    install_python_dependencies
    create_directories
    set_permissions
    test_batch_script
    create_crontab_example
    create_logrotate_config
    show_completion_info
}

# 执行主函数
main "$@"
