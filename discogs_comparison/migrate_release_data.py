#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB数据迁移脚本
用于将release_copy集合中status != 429的数据迁移到release_new集合
"""

import sys
import time
from datetime import datetime
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure, BulkWriteError
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migrate_release_data.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MongoDB连接配置
MONGODB_CONFIG = {
    'connection_string': '**********************************************************',
    'database_name': 'music_test',
    'source_collection': 'release_copy',
    'target_collection': 'release_new',
    'connection_timeout': 30000,  # 30秒超时
    'server_selection_timeout': 30000  # 30秒服务器选择超时
}

# 迁移操作配置
MIGRATION_CONFIG = {
    'batch_size': 1000,  # 批量处理大小
    'status_filter': {'$ne': 429},  # 过滤条件：status != 429
    'require_confirmation': True,  # 是否需要用户确认
    'max_migration_count': 100000,  # 最大迁移数量限制（安全措施）
    'enable_transaction': True,  # 是否启用事务处理
    'progress_report_interval': 5000  # 进度报告间隔
}


def create_mongodb_connection():
    """
    创建MongoDB连接
    
    Returns:
        tuple: (client, database, source_collection, target_collection) 或 (None, None, None, None) 如果连接失败
    """
    try:
        logger.info("正在连接到MongoDB...")
        logger.info(f"连接地址: {MONGODB_CONFIG['connection_string'].split('@')[1]}")
        
        # 创建MongoDB客户端
        client = MongoClient(
            MONGODB_CONFIG['connection_string'],
            connectTimeoutMS=MONGODB_CONFIG['connection_timeout'],
            serverSelectionTimeoutMS=MONGODB_CONFIG['server_selection_timeout']
        )
        
        # 测试连接
        client.admin.command('ping')
        logger.info("MongoDB连接成功")
        
        # 获取数据库和集合
        database = client[MONGODB_CONFIG['database_name']]
        source_collection = database[MONGODB_CONFIG['source_collection']]
        target_collection = database[MONGODB_CONFIG['target_collection']]
        
        logger.info(f"成功连接到数据库: {MONGODB_CONFIG['database_name']}")
        logger.info(f"源集合: {MONGODB_CONFIG['source_collection']}")
        logger.info(f"目标集合: {MONGODB_CONFIG['target_collection']}")
        
        return client, database, source_collection, target_collection
        
    except ConnectionFailure as e:
        logger.error(f"MongoDB连接失败: {e}")
        return None, None, None, None
    except Exception as e:
        logger.error(f"连接MongoDB时发生未知错误: {e}")
        return None, None, None, None


def validate_collections(database):
    """
    验证源和目标集合是否存在
    
    Args:
        database: MongoDB数据库对象
        
    Returns:
        tuple: (success, error_message)
    """
    try:
        collection_names = database.list_collection_names()
        
        # 检查源集合
        if MONGODB_CONFIG['source_collection'] not in collection_names:
            error_msg = f"源集合 '{MONGODB_CONFIG['source_collection']}' 不存在"
            logger.error(error_msg)
            return False, error_msg
        
        # 检查目标集合
        if MONGODB_CONFIG['target_collection'] not in collection_names:
            error_msg = f"目标集合 '{MONGODB_CONFIG['target_collection']}' 不存在"
            logger.error(error_msg)
            return False, error_msg
        
        logger.info("集合验证通过")
        return True, None
        
    except Exception as e:
        error_msg = f"验证集合时发生错误: {e}"
        logger.error(error_msg)
        return False, error_msg


def count_migration_records(source_collection):
    """
    统计待迁移记录数量
    
    Args:
        source_collection: 源集合对象
        
    Returns:
        tuple: (success, count, error_message)
    """
    try:
        # 构建查询条件
        query = {'status': MIGRATION_CONFIG['status_filter']}
        
        logger.info("正在统计待迁移记录数量...")
        logger.info(f"查询条件: {query}")
        
        # 统计记录数量
        count = source_collection.count_documents(query)
        
        logger.info(f"找到符合条件的记录数: {count}")
        return True, count, None
        
    except Exception as e:
        error_msg = f"统计记录数量时发生错误: {e}"
        logger.error(error_msg)
        return False, 0, error_msg


def get_user_confirmation(record_count):
    """
    获取用户确认
    
    Args:
        record_count (int): 待迁移记录数量
        
    Returns:
        bool: 用户是否确认继续
    """
    if not MIGRATION_CONFIG['require_confirmation']:
        return True
    
    print("\n" + "="*60)
    print("数据迁移确认")
    print("="*60)
    print(f"源集合: {MONGODB_CONFIG['source_collection']}")
    print(f"目标集合: {MONGODB_CONFIG['target_collection']}")
    print(f"筛选条件: status != 429")
    print(f"待迁移记录数: {record_count}")
    print(f"批量处理大小: {MIGRATION_CONFIG['batch_size']}")
    print("="*60)
    
    if record_count > MIGRATION_CONFIG['max_migration_count']:
        print(f"⚠️  警告: 待迁移记录数 ({record_count}) 超过安全限制 ({MIGRATION_CONFIG['max_migration_count']})")
        print("请检查数据或调整配置中的max_migration_count参数")
        return False
    
    print("\n⚠️  注意: 此操作将:")
    print("1. 从源集合查询符合条件的记录")
    print("2. 将这些记录插入到目标集合")
    print("3. 插入成功后从源集合删除对应记录")
    print("4. 使用事务确保数据一致性")
    print("\n此操作不可撤销，请确认数据已备份！")
    
    while True:
        confirm = input("\n确定要继续吗？(输入 Y 继续，N 取消): ").strip().upper()
        if confirm == 'Y':
            return True
        elif confirm == 'N':
            return False
        else:
            print("请输入 Y 或 N")


def migrate_data_batch(client, source_collection, target_collection):
    """
    批量迁移数据

    Args:
        client: MongoDB客户端对象
        source_collection: 源集合对象
        target_collection: 目标集合对象

    Returns:
        tuple: (success, migrated_count, error_message)
    """
    try:
        # 构建查询条件
        query = {'status': MIGRATION_CONFIG['status_filter']}

        logger.info("开始批量数据迁移...")
        logger.info(f"查询条件: {query}")
        logger.info(f"批量大小: {MIGRATION_CONFIG['batch_size']}")

        migrated_count = 0
        batch_count = 0
        start_time = time.time()

        # 使用游标批量处理数据
        cursor = source_collection.find(query).batch_size(MIGRATION_CONFIG['batch_size'])

        batch_documents = []
        batch_ids = []

        for document in cursor:
            # 收集文档用于批量插入
            # 移除_id字段，让目标集合自动生成新的_id
            doc_copy = document.copy()
            doc_id = doc_copy.pop('_id', None)
            batch_documents.append(doc_copy)
            batch_ids.append(document['_id'])

            # 当达到批量大小时，执行批量操作
            if len(batch_documents) >= MIGRATION_CONFIG['batch_size']:
                success, batch_migrated = _process_batch(
                    client, source_collection, target_collection,
                    batch_documents, batch_ids
                )

                if not success:
                    error_msg = f"批量处理失败，已处理 {migrated_count} 条记录"
                    logger.error(error_msg)
                    return False, migrated_count, error_msg

                migrated_count += batch_migrated
                batch_count += 1

                # 显示进度
                if migrated_count % MIGRATION_CONFIG['progress_report_interval'] == 0:
                    elapsed_time = time.time() - start_time
                    rate = migrated_count / elapsed_time if elapsed_time > 0 else 0
                    logger.info(f"迁移进度: {migrated_count} 条记录，处理速度: {rate:.1f} 条/秒")

                # 清空批量数据
                batch_documents = []
                batch_ids = []

        # 处理剩余的数据
        if batch_documents:
            success, batch_migrated = _process_batch(
                client, source_collection, target_collection,
                batch_documents, batch_ids
            )

            if success:
                migrated_count += batch_migrated
            else:
                error_msg = f"处理最后一批数据失败，已处理 {migrated_count} 条记录"
                logger.error(error_msg)
                return False, migrated_count, error_msg

        total_time = time.time() - start_time
        avg_rate = migrated_count / total_time if total_time > 0 else 0

        logger.info(f"数据迁移完成！")
        logger.info(f"总计迁移: {migrated_count} 条记录")
        logger.info(f"处理批次: {batch_count} 批")
        logger.info(f"总耗时: {total_time:.2f} 秒")
        logger.info(f"平均速度: {avg_rate:.1f} 条/秒")

        return True, migrated_count, None

    except Exception as e:
        error_msg = f"数据迁移过程中发生错误: {e}"
        logger.error(error_msg, exc_info=True)
        return False, migrated_count, error_msg


def _process_batch(client, source_collection, target_collection, documents, document_ids):
    """
    处理单个批次的数据迁移

    Args:
        client: MongoDB客户端对象
        source_collection: 源集合对象
        target_collection: 目标集合对象
        documents: 要插入的文档列表
        document_ids: 要删除的文档ID列表

    Returns:
        tuple: (success, migrated_count)
    """
    if not documents:
        return True, 0

    try:
        if MIGRATION_CONFIG['enable_transaction']:
            # 使用事务确保数据一致性
            with client.start_session() as session:
                with session.start_transaction():
                    # 1. 插入到目标集合
                    insert_result = target_collection.insert_many(documents, session=session)
                    inserted_count = len(insert_result.inserted_ids)

                    # 2. 从源集合删除
                    delete_result = source_collection.delete_many(
                        {'_id': {'$in': document_ids}},
                        session=session
                    )
                    deleted_count = delete_result.deleted_count

                    # 验证操作结果
                    if inserted_count != len(documents):
                        raise Exception(f"插入数量不匹配: 期望 {len(documents)}, 实际 {inserted_count}")

                    if deleted_count != len(document_ids):
                        raise Exception(f"删除数量不匹配: 期望 {len(document_ids)}, 实际 {deleted_count}")

                    logger.debug(f"批次处理成功: 插入 {inserted_count} 条，删除 {deleted_count} 条")
                    return True, inserted_count
        else:
            # 不使用事务的简单模式（不推荐用于生产环境）
            logger.warning("未启用事务模式，数据一致性无法保证")

            # 1. 插入到目标集合
            insert_result = target_collection.insert_many(documents)
            inserted_count = len(insert_result.inserted_ids)

            # 2. 从源集合删除
            delete_result = source_collection.delete_many({'_id': {'$in': document_ids}})
            deleted_count = delete_result.deleted_count

            logger.debug(f"批次处理完成: 插入 {inserted_count} 条，删除 {deleted_count} 条")
            return True, inserted_count

    except BulkWriteError as e:
        logger.error(f"批量写入错误: {e.details}")
        return False, 0
    except Exception as e:
        logger.error(f"批次处理失败: {e}")
        return False, 0


def main():
    """主函数"""
    logger.info("="*60)
    logger.info("MongoDB数据迁移脚本启动")
    logger.info("源集合: %s", MONGODB_CONFIG['source_collection'])
    logger.info("目标集合: %s", MONGODB_CONFIG['target_collection'])
    logger.info("筛选条件: status != 429")
    logger.info("="*60)

    # 1. 创建数据库连接
    client, database, source_collection, target_collection = create_mongodb_connection()

    if client is None or database is None or source_collection is None or target_collection is None:
        logger.error("无法连接到数据库，程序退出")
        sys.exit(1)

    try:
        # 2. 验证集合存在性
        success, error_msg = validate_collections(database)
        if not success:
            logger.error("集合验证失败: %s", error_msg)
            sys.exit(1)

        # 3. 统计待迁移记录数量
        success, record_count, error_msg = count_migration_records(source_collection)
        if not success:
            logger.error("统计记录数量失败: %s", error_msg)
            sys.exit(1)

        if record_count == 0:
            logger.info("没有找到符合条件的记录，程序退出")
            sys.exit(0)

        # 4. 获取用户确认
        if not get_user_confirmation(record_count):
            logger.info("用户取消操作，程序退出")
            sys.exit(0)

        # 5. 执行数据迁移
        logger.info("开始执行数据迁移...")
        success, migrated_count, error_msg = migrate_data_batch(
            client, source_collection, target_collection
        )

        if success:
            logger.info("="*60)
            logger.info("数据迁移成功完成！")
            logger.info("总计迁移记录数: %d", migrated_count)
            logger.info("="*60)
            sys.exit(0)
        else:
            logger.error("数据迁移失败: %s", error_msg)
            logger.error("已迁移记录数: %d", migrated_count)
            sys.exit(1)

    except KeyboardInterrupt:
        logger.warning("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error("程序执行过程中发生未知错误: %s", e, exc_info=True)
        sys.exit(1)
    finally:
        # 关闭数据库连接
        if 'client' in locals() and client is not None:
            client.close()
            logger.info("数据库连接已关闭")


if __name__ == "__main__":
    main()
