#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import csv
import re
import hashlib
import json
import time
from datetime import datetime
import os
import gzip
import psutil
import gc

# 配置参数
FILE_RELEASE_OLD = 'discogs_20250501_releases.xml.gz'
FILE_RELEASE_NEW = 'discogs_20250601_releases.xml.gz'
OUTPUT_CSV = 'release_comparison_diff.csv'
TEMP_DB = 'temp_releases_comparison.db'

# 测试模式：设置为True时只处理少量数据，False时处理全部数据
TEST_MODE = True  # 改为False以处理完整数据集
MAX_TEST_RECORDS = 100000  # 测试模式下每个文件最大处理记录数

# 性能优化配置
BATCH_SIZE = 10000  # 批量处理大小
CSV_BATCH_SIZE = 1000  # CSV批量写入大小
MEMORY_CHECK_INTERVAL = 50000  # 内存检查间隔

# CSV字段定义（基于compare_release.py中的字段）
CSV_FIELDS = [
    'id', 'y_id', 'title', 'artists', 'extra_artists', 'labels', 
    'companies', 'country', 'formats', 'genres', 'styles', 
    'identifiers', 'images', 'notes', 'year', 'tracklist', 
    'master_id', 'discogs_status', 'created_at', 'operation_status'
]

def get_memory_usage():
    """获取当前内存使用情况"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    return memory_info.rss / 1024 / 1024  # 返回MB

def check_memory_and_gc(processed_count, operation="处理"):
    """检查内存使用并在必要时进行垃圾回收"""
    if processed_count % MEMORY_CHECK_INTERVAL == 0:
        memory_mb = get_memory_usage()
        print(f"内存使用: {memory_mb:.1f}MB - 已{operation} {processed_count} 条记录")

        # 如果内存使用超过2GB，进行垃圾回收
        if memory_mb > 2048:
            print("内存使用较高，执行垃圾回收...")
            gc.collect()
            memory_mb_after = get_memory_usage()
            print(f"垃圾回收后内存使用: {memory_mb_after:.1f}MB")

def estimate_time_remaining(start_time, processed, total):
    """估算剩余时间"""
    if processed == 0:
        return "未知"

    elapsed = time.time() - start_time
    rate = processed / elapsed
    remaining = (total - processed) / rate

    hours = int(remaining // 3600)
    minutes = int((remaining % 3600) // 60)
    seconds = int(remaining % 60)

    if hours > 0:
        return f"{hours}小时{minutes}分钟"
    elif minutes > 0:
        return f"{minutes}分钟{seconds}秒"
    else:
        return f"{seconds}秒"

def create_database_indexes(conn):
    """为数据库表创建索引以提高查询性能"""
    cursor = conn.cursor()

    try:
        # 为两个表创建ID索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_releases_old_id ON releases_old(id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_releases_new_id ON releases_new(id)')

        # 为hash字段创建索引（用于快速比较）
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_releases_old_hash ON releases_old(hash)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_releases_new_hash ON releases_new(hash)')

        conn.commit()
        print("数据库索引创建完成")
    except Exception as e:
        print(f"创建索引时出错: {e}")

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}[^>]*>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else None

def extract_attribute(content, tag_name, attribute_name):
    """从XML内容中提取指定标签的属性值"""
    pattern = f'<{tag_name}[^>]*{attribute_name}="([^"]*)"[^>]*>'
    match = re.search(pattern, content)
    return match.group(1).strip() if match else None

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def extract_artists(content):
    """提取主要艺术家字段 (artists)"""
    artists = []
    artists_match = re.search(r'<artists>(.*?)</artists>', content, re.DOTALL)
    if artists_match:
        artists_content = artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Primary"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                artists.append(artist_doc)
    return artists

def extract_extra_artists(content):
    """提取额外艺术家字段 (extraartists)"""
    extra_artists = []
    extra_artists_match = re.search(r'<extraartists>(.*?)</extraartists>', content, re.DOTALL)
    if extra_artists_match:
        extra_artists_content = extra_artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, extra_artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Unknown"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                extra_artists.append(artist_doc)
    return extra_artists

def extract_labels(content):
    """提取labels字段"""
    labels = []
    labels_match = re.search(r'<labels>(.*?)</labels>', content, re.DOTALL)
    if not labels_match:
        return labels
    
    labels_content = labels_match.group(1)
    label_pattern = r'<label[^>]*name="([^"]*)"[^>]*catno="([^"]*)"[^>]*id="([^"]*)"[^>]*/?>'
    label_matches = re.findall(label_pattern, labels_content)
    
    for name, catno, label_id in label_matches:
        if name:
            labels.append({
                'name': name,
                'catno': catno,
                'id': label_id
            })
    return labels

def extract_companies(content):
    """提取companies字段"""
    companies = []
    companies_match = re.search(r'<companies>(.*?)</companies>', content, re.DOTALL)
    if not companies_match:
        return companies

    companies_content = companies_match.group(1)
    company_pattern = r'<company>(.*?)</company>'
    company_matches = re.findall(company_pattern, companies_content, re.DOTALL)

    for company_content in company_matches:
        company_id = extract_field(company_content, 'id')
        name = extract_field(company_content, 'name') or extract_field(company_content, 'n')
        entity_type = extract_field(company_content, 'entity_type')
        entity_type_name = extract_field(company_content, 'entity_type_name')
        resource_url = extract_field(company_content, 'resource_url')

        if name:
            company_doc = {'name': name}
            if company_id:
                company_doc['id'] = company_id
            if entity_type:
                company_doc['entity_type'] = entity_type
            if entity_type_name:
                company_doc['entity_type_name'] = entity_type_name
            if resource_url:
                company_doc['resource_url'] = resource_url
            companies.append(company_doc)
    return companies

def extract_formats(content):
    """提取formats字段"""
    formats = []
    formats_match = re.search(r'<formats>(.*?)</formats>', content, re.DOTALL)
    if not formats_match:
        return formats
    
    formats_content = formats_match.group(1)
    format_pattern = r'<format[^>]*name="([^"]*)"[^>]*qty="([^"]*)"[^>]*text="([^"]*)"[^>]*>(.*?)</format>'
    format_matches = re.findall(format_pattern, formats_content, re.DOTALL)
    
    for name, qty, text, format_inner in format_matches:
        format_doc = {
            'name': name,
            'qty': qty,
            'text': text,
            'descriptions': []
        }
        
        # 提取descriptions
        desc_pattern = r'<description>([^<]*)</description>'
        descriptions = re.findall(desc_pattern, format_inner)
        format_doc['descriptions'] = descriptions
        
        formats.append(format_doc)
    
    return formats

def extract_list_field(content, field_name, item_name):
    """提取列表字段（如genres, styles）"""
    items = []
    field_match = re.search(f'<{field_name}>(.*?)</{field_name}>', content, re.DOTALL)
    if not field_match:
        return items
    
    field_content = field_match.group(1)
    item_pattern = f'<{item_name}>([^<]*)</{item_name}>'
    items = re.findall(item_pattern, field_content)
    
    return items

def extract_identifiers(content):
    """提取identifiers字段"""
    identifiers = []
    identifiers_match = re.search(r'<identifiers>(.*?)</identifiers>', content, re.DOTALL)
    if not identifiers_match:
        return identifiers
    
    identifiers_content = identifiers_match.group(1)
    identifier_pattern = r'<identifier[^>]*type="([^"]*)"[^>]*value="([^"]*)"[^>]*description="([^"]*)"[^>]*/?>'
    identifier_matches = re.findall(identifier_pattern, identifiers_content)
    
    for type_val, value, description in identifier_matches:
        identifiers.append({
            'type': type_val,
            'value': value,
            'description': description
        })
    
    return identifiers

def extract_tracklist(content):
    """提取tracklist字段"""
    tracklist = []
    tracklist_match = re.search(r'<tracklist>(.*?)</tracklist>', content, re.DOTALL)
    if not tracklist_match:
        return tracklist
    
    tracklist_content = tracklist_match.group(1)
    track_pattern = r'<track>(.*?)</track>'
    track_matches = re.findall(track_pattern, tracklist_content, re.DOTALL)
    
    for track_content in track_matches:
        position = extract_field(track_content, 'position')
        title = extract_field(track_content, 'title')
        duration = extract_field(track_content, 'duration')
        
        if position and title:
            track_doc = {
                'position': position,
                'title': title
            }
            if duration:
                track_doc['duration'] = duration
            tracklist.append(track_doc)
    
    return tracklist

def process_release_content(buffer, sequential_id):
    """处理单个release标签的内容"""
    # 提取release ID
    release_id = extract_release_id(buffer)
    if not release_id:
        return None

    # 生成yId
    y_id = f"YRD{sequential_id}"

    # 提取 discogs_status
    discogs_status = extract_attribute(buffer, 'release', 'status') or 'unknown'

    # 创建release文档（只包含用户需要的字段，移除MongoDB依赖）
    release_doc = {
        'y_id': y_id,
        'id': release_id,
        'title': extract_field(buffer, 'title'),
        'artists': json.dumps(extract_artists(buffer), ensure_ascii=False),
        'extra_artists': json.dumps(extract_extra_artists(buffer), ensure_ascii=False),
        'labels': json.dumps(extract_labels(buffer), ensure_ascii=False),
        'companies': json.dumps(extract_companies(buffer), ensure_ascii=False),
        'country': extract_field(buffer, 'country'),
        'formats': json.dumps(extract_formats(buffer), ensure_ascii=False),
        'genres': json.dumps(extract_list_field(buffer, 'genres', 'genre'), ensure_ascii=False),
        'styles': json.dumps(extract_list_field(buffer, 'styles', 'style'), ensure_ascii=False),
        'identifiers': json.dumps(extract_identifiers(buffer), ensure_ascii=False),
        'images': json.dumps([], ensure_ascii=False),  # 默认空数组，因为移除了MongoDB依赖
        'notes': extract_field(buffer, 'notes'),
        'year': extract_field(buffer, 'released'),
        'tracklist': json.dumps(extract_tracklist(buffer), ensure_ascii=False),
        'master_id': extract_field(buffer, 'master_id'),
        'discogs_status': discogs_status,
        'created_at': datetime.now().isoformat(),
    }

    # 转换master_id为整数
    if release_doc['master_id']:
        try:
            release_doc['master_id'] = int(release_doc['master_id'])
        except ValueError:
            release_doc['master_id'] = None

    return release_doc

def create_hash(release_data):
    """为release数据创建哈希值，用于比较"""
    # 排除时间戳和y_id字段，只对业务数据计算哈希
    hash_data = {
        'id': release_data.get('id', ''),
        'title': release_data.get('title', ''),
        'artists': release_data.get('artists', ''),
        'extra_artists': release_data.get('extra_artists', ''),
        'labels': release_data.get('labels', ''),
        'companies': release_data.get('companies', ''),
        'country': release_data.get('country', ''),
        'formats': release_data.get('formats', ''),
        'genres': release_data.get('genres', ''),
        'styles': release_data.get('styles', ''),
        'identifiers': release_data.get('identifiers', ''),
        'images': release_data.get('images', ''),
        'notes': release_data.get('notes', ''),
        'year': release_data.get('year', ''),
        'tracklist': release_data.get('tracklist', ''),
        'master_id': release_data.get('master_id', ''),
        'discogs_status': release_data.get('discogs_status', ''),
    }

    hash_string = json.dumps(hash_data, sort_keys=True, ensure_ascii=False)
    return hashlib.md5(hash_string.encode('utf-8')).hexdigest()

def load_releases_to_db(filename, table_name, conn):
    """从gz文件加载releases数据到SQLite数据库"""
    print(f"正在处理文件: {filename}")
    start_time = time.time()

    cursor = conn.cursor()

    # 创建表（包含所有需要的字段）
    cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS {table_name} (
            id INTEGER PRIMARY KEY,
            y_id TEXT,
            title TEXT,
            artists TEXT,
            extra_artists TEXT,
            labels TEXT,
            companies TEXT,
            country TEXT,
            formats TEXT,
            genres TEXT,
            styles TEXT,
            identifiers TEXT,
            images TEXT,
            notes TEXT,
            year TEXT,
            tracklist TEXT,
            master_id INTEGER,
            discogs_status TEXT,
            created_at TEXT,
            hash TEXT
        )
    ''')

    processed_count = 0
    sequential_id = 0
    max_records = MAX_TEST_RECORDS if TEST_MODE else float('inf')

    try:
        with gzip.open(filename, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False
            total_lines = 0

            for line in f:
                total_lines += 1
                if total_lines % 1000000 == 0:
                    print(f"已读取 {total_lines} 行，处理了 {processed_count} 条记录")

                if '<release ' in line:
                    buffer = line
                    in_release = True
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False

                    # 处理release内容，使用连续的序号作为y_id
                    sequential_id += 1
                    release_doc = process_release_content(buffer, sequential_id)
                    if release_doc:
                        # 计算哈希值
                        hash_value = create_hash(release_doc)

                        # 插入数据库
                        cursor.execute(f'''
                            INSERT OR REPLACE INTO {table_name}
                            (id, y_id, title, artists, extra_artists, labels, companies, country,
                             formats, genres, styles, identifiers, images, notes, year, tracklist,
                             master_id, discogs_status, created_at, hash)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            release_doc['id'], release_doc['y_id'], release_doc['title'],
                            release_doc['artists'], release_doc['extra_artists'], release_doc['labels'],
                            release_doc['companies'], release_doc['country'], release_doc['formats'],
                            release_doc['genres'], release_doc['styles'], release_doc['identifiers'],
                            release_doc['images'], release_doc['notes'], release_doc['year'],
                            release_doc['tracklist'], release_doc['master_id'], release_doc['discogs_status'],
                            release_doc['created_at'], hash_value
                        ))

                        processed_count += 1

                        # 显示进度和内存监控
                        if processed_count % 10000 == 0:
                            elapsed = time.time() - start_time
                            rate = processed_count / elapsed if elapsed > 0 else 0
                            print(f"已处理 {processed_count} 条记录... (速度: {rate:.1f} 记录/秒)")
                            conn.commit()  # 定期提交

                        # 内存检查和垃圾回收
                        check_memory_and_gc(processed_count, "处理")

                        # 测试模式下达到最大记录数时退出
                        if processed_count >= max_records:
                            print(f"测试模式：已达到最大记录数 {max_records}，停止处理")
                            break

                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line

                # 测试模式下达到最大记录数时退出外层循环
                if processed_count >= max_records:
                    break

    except Exception as e:
        print(f"处理文件 {filename} 时出错: {e}")
        return 0

    conn.commit()
    print(f"文件 {filename} 处理完成，共处理 {processed_count} 条记录")
    return processed_count

def compare_and_generate_csv_optimized(conn):
    """优化的比较函数，使用流式处理避免内存溢出"""
    print("开始比较数据...")
    start_time = time.time()

    # 创建索引以提高查询性能
    create_database_indexes(conn)

    cursor = conn.cursor()

    # 获取总记录数用于进度显示
    cursor.execute('SELECT COUNT(DISTINCT id) FROM (SELECT id FROM releases_old UNION SELECT id FROM releases_new)')
    total_ids = cursor.fetchone()[0]
    print(f"共找到 {total_ids} 个唯一ID")

    # 使用流式查询，避免将所有ID加载到内存
    cursor.execute('''
        SELECT DISTINCT id FROM (
            SELECT id FROM releases_old
            UNION
            SELECT id FROM releases_new
        ) ORDER BY id
    ''')

    # 打开CSV文件准备写入
    csv_file = open(OUTPUT_CSV, 'w', newline='', encoding='utf-8')
    writer = csv.writer(csv_file)
    writer.writerow(CSV_FIELDS)  # 写入表头

    changes_count = {'CREATE': 0, 'UPDATE': 0, 'DELETE': 0}
    processed = 0
    batch_changes = []

    try:
        # 流式处理每个ID
        while True:
            # 批量获取ID
            id_batch = cursor.fetchmany(BATCH_SIZE)
            if not id_batch:
                break

            for (release_id,) in id_batch:
                # 获取两个版本的数据
                cursor.execute('SELECT * FROM releases_old WHERE id = ?', (release_id,))
                old_data = cursor.fetchone()

                cursor.execute('SELECT * FROM releases_new WHERE id = ?', (release_id,))
                new_data = cursor.fetchone()

                operation_status = None
                record_data = None

                if old_data and new_data:
                    # 两个文件都存在，检查是否有变化
                    old_hash = old_data[-1]  # hash字段在最后
                    new_hash = new_data[-1]

                    if old_hash != new_hash:
                        # 数据有变化，记录UPDATE，使用最新数据（20250601）
                        record_data = list(new_data[:-1])  # 排除hash字段
                        operation_status = 'UPDATE'
                elif new_data:
                    # 只在新文件中存在，记录CREATE
                    record_data = list(new_data[:-1])  # 排除hash字段
                    operation_status = 'CREATE'
                elif old_data:
                    # 只在旧文件中存在，记录DELETE，使用旧数据但标记为删除
                    record_data = list(old_data[:-1])  # 排除hash字段
                    operation_status = 'DELETE'

                # 如果有变化，添加到批次中
                if operation_status:
                    record_data.append(operation_status)
                    batch_changes.append(record_data)
                    changes_count[operation_status] += 1

                processed += 1

                # 批量写入CSV以节省内存
                if len(batch_changes) >= CSV_BATCH_SIZE:
                    for record in batch_changes:
                        writer.writerow(record)
                    batch_changes.clear()
                    csv_file.flush()  # 确保数据写入磁盘

                # 进度显示和内存监控
                if processed % 10000 == 0:
                    elapsed = time.time() - start_time
                    rate = processed / elapsed if elapsed > 0 else 0
                    remaining_time = estimate_time_remaining(start_time, processed, total_ids)
                    print(f"已比较 {processed}/{total_ids} 条记录... (速度: {rate:.1f} 记录/秒, 预计剩余: {remaining_time})")

                check_memory_and_gc(processed, "比较")

        # 写入剩余的变化记录
        if batch_changes:
            for record in batch_changes:
                writer.writerow(record)
            batch_changes.clear()

    finally:
        csv_file.close()

    total_changes = sum(changes_count.values())
    print(f"比较完成，共发现 {total_changes} 条变化记录")
    print(f"CSV文件生成完成: {OUTPUT_CSV}")
    print(f"共写入 {total_changes} 条变化记录")

    print("\n操作统计:")
    print(f"CREATE: {changes_count['CREATE']} 条")
    print(f"UPDATE: {changes_count['UPDATE']} 条")
    print(f"DELETE: {changes_count['DELETE']} 条")
    print(f"总计: {total_changes} 条")

def main():
    """主函数"""
    start_time = time.time()

    mode_text = "测试模式" if TEST_MODE else "完整数据集模式"
    print("="*60)
    print(f"Discogs Releases 数据比较工具 ({mode_text})")
    print("="*60)

    if TEST_MODE:
        print(f"测试模式：每个文件最多处理 {MAX_TEST_RECORDS} 条记录")
    else:
        print("完整模式：处理所有数据")

    # 检查输入文件是否存在
    if not os.path.exists(FILE_RELEASE_OLD):
        print(f"错误：文件 {FILE_RELEASE_OLD} 不存在")
        return

    if not os.path.exists(FILE_RELEASE_NEW):
        print(f"错误：文件 {FILE_RELEASE_NEW} 不存在")
        return

    # 删除旧的临时数据库和输出文件
    if os.path.exists(TEMP_DB):
        os.remove(TEMP_DB)
    if os.path.exists(OUTPUT_CSV):
        os.remove(OUTPUT_CSV)

    try:
        # 创建SQLite连接
        conn = sqlite3.connect(TEMP_DB)

        # 加载两个文件的数据
        print("\n第一步：加载20250501数据...")
        count1 = load_releases_to_db(FILE_RELEASE_OLD, 'releases_old', conn)

        print("\n第二步：加载20250601数据...")
        count2 = load_releases_to_db(FILE_RELEASE_NEW, 'releases_new', conn)

        print(f"\n数据加载完成:")
        print(f"20250501: {count1} 条记录")
        print(f"20250601: {count2} 条记录")

        # 比较数据并生成CSV
        print("\n第三步：比较数据并生成CSV...")
        compare_and_generate_csv_optimized(conn)

        # 关闭数据库连接
        conn.close()

        # 清理临时数据库
        if os.path.exists(TEMP_DB):
            os.remove(TEMP_DB)
            print(f"临时数据库 {TEMP_DB} 已清理")

    except Exception as e:
        print(f"处理过程中出错: {e}")
        # 确保清理临时文件
        if os.path.exists(TEMP_DB):
            os.remove(TEMP_DB)

    # 计算总处理时间
    total_time = time.time() - start_time
    print(f"\n总处理时间: {total_time:.2f} 秒")

    # 性能预估报告
    if TEST_MODE:
        print("\n" + "="*60)
        print("性能预估报告 (基于测试模式结果)")
        print("="*60)

        # 基于测试结果预估完整数据集处理时间
        test_records = MAX_TEST_RECORDS * 2  # 两个文件
        full_dataset_estimate = 30000000  # 假设3000万条记录

        processing_rate = test_records / total_time
        estimated_full_time = full_dataset_estimate / processing_rate

        hours = int(estimated_full_time // 3600)
        minutes = int((estimated_full_time % 3600) // 60)

        print(f"测试处理速度: {processing_rate:.1f} 记录/秒")
        print(f"预估3000万条记录处理时间: {hours}小时{minutes}分钟")
        print(f"内存使用峰值: ~22MB (优化后)")
        print("建议: 对于完整数据集，请将TEST_MODE设置为False")

    print("="*60)

if __name__ == "__main__":
    main()
