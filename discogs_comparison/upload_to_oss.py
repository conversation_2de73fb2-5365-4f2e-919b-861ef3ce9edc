#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSS文件上传工具
简单易用的阿里云OSS文件上传脚本

使用方法:
1. 上传单个文件: python3 upload_to_oss.py <文件路径>
2. 上传多个文件: python3 upload_to_oss.py <文件1> <文件2> <文件3>
3. 查看帮助: python3 upload_to_oss.py --help

作者: AI Assistant
创建时间: 2025-01-14
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path

# 导入oss2模块
import oss2

# OSS配置参数 - 请根据您的实际配置修改
OSS_CONFIG = {
    "access_key_id": "LTAI5tA59JgAKzFJv67anscu",
    "access_key_secret": "******************************",
    "endpoint": "https://oss-cn-shanghai.aliyuncs.com",
    "bucket_name": "discogs",
    "region": "cn-shanghai",
    "upload_path_prefix": "diff/"
}

def init_oss_client():
    """初始化OSS客户端"""
    try:
        # 创建认证对象
        auth = oss2.Auth(
            OSS_CONFIG['access_key_id'],
            OSS_CONFIG['access_key_secret']
        )
        
        # 创建Bucket对象
        bucket = oss2.Bucket(
            auth,
            OSS_CONFIG['endpoint'],
            OSS_CONFIG['bucket_name']
        )
        
        return bucket
    except Exception as e:
        print(f"❌ OSS客户端初始化失败: {e}")
        return None

def upload_file(bucket, local_file_path, custom_name=None):
    """
    上传单个文件到OSS
    
    Args:
        bucket: OSS bucket对象
        local_file_path: 本地文件路径
        custom_name: 自定义文件名（可选）
        
    Returns:
        bool: 是否上传成功
    """
    local_path = Path(local_file_path)
    
    # 检查文件是否存在
    if not local_path.exists():
        print(f"❌ 文件不存在: {local_file_path}")
        return False
    
    # 生成OSS对象名称
    if custom_name:
        object_name = f"{OSS_CONFIG['upload_path_prefix']}{custom_name}"
    else:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        object_name = f"{OSS_CONFIG['upload_path_prefix']}{timestamp}_{local_path.name}"
    
    try:
        print(f"📤 正在上传: {local_path.name}")
        print(f"   本地路径: {local_file_path}")
        print(f"   OSS路径: {object_name}")
        
        start_time = time.time()
        
        # 执行上传
        result = bucket.put_object_from_file(object_name, str(local_path))
        
        duration = time.time() - start_time
        file_size = local_path.stat().st_size
        
        print(f"✅ 上传成功!")
        print(f"   文件大小: {format_file_size(file_size)}")
        print(f"   耗时: {duration:.2f}秒")
        print(f"   ETag: {result.etag}")
        print(f"   OSS URL: https://{OSS_CONFIG['bucket_name']}.{OSS_CONFIG['endpoint'].replace('https://', '')}/{object_name}")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        print()
        return False

def format_file_size(size_bytes):
    """格式化文件大小显示"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

def print_help():
    """打印帮助信息"""
    print("=" * 60)
    print("🚀 OSS文件上传工具")
    print("=" * 60)
    print()
    print("使用方法:")
    print("  python3 upload_to_oss.py <文件路径1> [文件路径2] [文件路径3] ...")
    print()
    print("示例:")
    print("  python3 upload_to_oss.py test.txt")
    print("  python3 upload_to_oss.py file1.txt file2.jpg file3.pdf")
    print()
    print("配置信息:")
    print(f"  Bucket: {OSS_CONFIG['bucket_name']}")
    print(f"  Endpoint: {OSS_CONFIG['endpoint']}")
    print(f"  上传路径前缀: {OSS_CONFIG['upload_path_prefix']}")
    print()

def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) < 2 or '--help' in sys.argv or '-h' in sys.argv:
        print_help()
        return
    
    file_paths = sys.argv[1:]
    
    print("=" * 60)
    print("🚀 OSS文件上传工具")
    print("=" * 60)
    print()
    
    # 初始化OSS客户端
    print("🔧 正在初始化OSS客户端...")
    bucket = init_oss_client()
    if not bucket:
        print("❌ 初始化失败，退出程序")
        sys.exit(1)
    
    print("✅ OSS客户端初始化成功")
    print()
    
    # 上传文件
    success_count = 0
    total_count = len(file_paths)
    
    for i, file_path in enumerate(file_paths, 1):
        print(f"📁 处理文件 {i}/{total_count}: {file_path}")
        
        if upload_file(bucket, file_path):
            success_count += 1
    
    # 显示结果
    print("=" * 60)
    if success_count == total_count:
        print(f"🎉 所有文件上传成功! ({success_count}/{total_count})")
    else:
        print(f"⚠️  部分文件上传成功: {success_count}/{total_count}")
        if success_count == 0:
            print("❌ 没有文件上传成功")
    print("=" * 60)

if __name__ == "__main__":
    main()
