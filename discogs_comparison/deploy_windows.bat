@echo off
REM Discogs 数据比较批处理 Windows 部署脚本 (批处理版本)
REM 适用于 Windows Server 2019/2022 和 Windows 10/11

setlocal enabledelayedexpansion

echo ========================================
echo Discogs 数据比较批处理 Windows 部署脚本
echo ========================================
echo.

REM 检查Python安装
echo [INFO] 检查Python安装...

set PYTHON_CMD=
for %%i in (python python3 py) do (
    %%i --version >nul 2>&1
    if !errorlevel! equ 0 (
        set PYTHON_CMD=%%i
        goto :python_found
    )
)

echo [ERROR] 未找到Python安装
echo 请从以下地址下载并安装Python 3.8或更高版本:
echo https://www.python.org/downloads/
echo.
pause
exit /b 1

:python_found
echo [SUCCESS] 找到Python: %PYTHON_CMD%

REM 检查Python版本
echo [INFO] 检查Python版本...
%PYTHON_CMD% -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python版本过低，需要3.8或更高版本
    %PYTHON_CMD% --version
    pause
    exit /b 1
)

%PYTHON_CMD% --version
echo [SUCCESS] Python版本满足要求

REM 创建虚拟环境
echo [INFO] 创建Python虚拟环境...

if exist venv (
    echo [WARNING] 虚拟环境已存在
    set /p recreate="是否重新创建虚拟环境？(y/N): "
    if /i "!recreate!"=="y" (
        echo [INFO] 删除现有虚拟环境...
        rmdir /s /q venv
    ) else (
        echo [INFO] 跳过虚拟环境创建
        goto :install_deps
    )
)

%PYTHON_CMD% -m venv venv
if %errorlevel% neq 0 (
    echo [ERROR] 虚拟环境创建失败
    pause
    exit /b 1
)

echo [SUCCESS] 虚拟环境创建完成

:install_deps
REM 安装Python依赖
echo [INFO] 安装Python依赖...

if not exist requirements.txt (
    echo [ERROR] requirements.txt 文件不存在
    pause
    exit /b 1
)

REM 激活虚拟环境并安装依赖
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo [ERROR] 虚拟环境激活失败
    pause
    exit /b 1
)

REM 升级pip
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo [WARNING] pip升级失败，继续安装依赖...
)

REM 安装依赖
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [ERROR] 依赖安装失败
    pause
    exit /b 1
)

echo [SUCCESS] Python依赖安装完成

REM 创建必要的目录
echo [INFO] 创建必要的目录...

if not exist logs mkdir logs
if not exist config mkdir config
if not exist backup mkdir backup

echo [SUCCESS] 目录创建完成

REM 测试批处理脚本
echo [INFO] 测试批处理脚本...

python batch_run.py --config-check >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] 批处理脚本测试通过
) else (
    echo [WARNING] 批处理脚本测试失败，请检查配置
)

REM 创建启动脚本
echo [INFO] 创建启动脚本...

echo @echo off > run_batch.bat
echo REM Discogs 批处理启动脚本 >> run_batch.bat
echo cd /d "%%~dp0" >> run_batch.bat
echo call venv\Scripts\activate.bat >> run_batch.bat
echo python batch_run.py %%* >> run_batch.bat

echo [SUCCESS] 启动脚本已创建: run_batch.bat

REM 创建Windows定时任务PowerShell脚本
echo [INFO] 创建定时任务设置脚本...

(
echo # Windows定时任务设置脚本
echo $currentPath = Get-Location
echo $taskName = "DiscogsBatchProcessor"
echo $pythonPath = Join-Path $currentPath "venv\Scripts\python.exe"
echo $scriptPath = Join-Path $currentPath "batch_run.py"
echo.
echo # 创建定时任务
echo $action = New-ScheduledTaskAction -Execute $pythonPath -Argument "$scriptPath --all" -WorkingDirectory $currentPath
echo $trigger = New-ScheduledTaskTrigger -Daily -At 2:00AM
echo $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
echo.
echo try {
echo     Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Description "Discogs 数据比较批处理任务"
echo     Write-Host "定时任务创建成功: $taskName"
echo } catch {
echo     Write-Host "定时任务创建失败: $_"
echo }
) > setup_task.ps1

echo [SUCCESS] 定时任务设置脚本已创建: setup_task.ps1

REM 创建监控脚本
echo [INFO] 创建监控脚本...

(
echo @echo off
echo REM 批处理状态监控脚本
echo cd /d "%%~dp0"
echo call venv\Scripts\activate.bat
echo python batch_run.py --status
echo pause
) > monitor.bat

echo [SUCCESS] 监控脚本已创建: monitor.bat

REM 显示完成信息
echo.
echo ========================================
echo [SUCCESS] Windows部署完成！
echo ========================================
echo.
echo 📋 接下来的步骤:
echo 1. 编辑OSS配置文件: config\oss_config.yaml
echo 2. 根据需要调整批处理配置: config\batch_config.yaml
echo 3. 测试批处理脚本:
echo    run_batch.bat --config-check
echo    run_batch.bat --status
echo 4. 设置定时任务:
echo    以管理员身份运行PowerShell
echo    执行: .\setup_task.ps1
echo.
echo 📖 使用说明:
echo - 运行批处理: run_batch.bat [参数]
echo - 查看状态: monitor.bat
echo - 处理单个类型: run_batch.bat release
echo - 处理所有类型: run_batch.bat --all
echo.
echo 📁 重要文件:
echo - run_batch.bat: 主启动脚本
echo - monitor.bat: 状态监控脚本
echo - setup_task.ps1: 定时任务设置脚本
echo.
echo 更多信息请查看: WINDOWS_DEPLOYMENT_GUIDE.md
echo.
pause
