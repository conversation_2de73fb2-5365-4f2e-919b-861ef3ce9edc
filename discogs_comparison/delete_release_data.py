#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB数据删除脚本
用于根据CSV文件中的id列表删除release_new集合中的记录
"""

import csv
import sys
import os
from datetime import datetime
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure, BulkWriteError
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('delete_release_data.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MongoDB连接配置
MONGODB_CONFIG = {
    'connection_string': '**********************************************************',
    'database_name': 'music_test',
    'collection_name': 'release_new',
    'connection_timeout': 30000,  # 30秒超时
    'server_selection_timeout': 30000  # 30秒服务器选择超时
}

# 删除操作配置
DELETE_CONFIG = {
    'csv_file': 'release_data_july_29_30.csv',
    'batch_size': 1000,  # 批量删除大小
    'require_confirmation': True,  # 是否需要用户确认
    'backup_before_delete': False,  # 是否在删除前备份（可选功能）
    'max_delete_count': 10000  # 最大删除数量限制（安全措施）
}


def read_ids_from_csv(csv_file_path):
    """
    从CSV文件中读取id列表并转换为整数
    
    Args:
        csv_file_path (str): CSV文件路径
        
    Returns:
        tuple: (success, id_list, error_message)
    """
    try:
        if not os.path.exists(csv_file_path):
            return False, [], f"CSV文件不存在: {csv_file_path}"
        
        logger.info(f"正在读取CSV文件: {csv_file_path}")
        
        id_list = []
        invalid_ids = []
        
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            # 检查是否有id列
            if 'id' not in reader.fieldnames:
                return False, [], "CSV文件中没有找到'id'列"
            
            for row_num, row in enumerate(reader, start=2):  # 从第2行开始（跳过表头）
                id_value = row['id'].strip()
                
                if not id_value:
                    logger.warning(f"第{row_num}行的id值为空，跳过")
                    continue
                
                try:
                    # 转换为整数
                    id_int = int(id_value)
                    id_list.append(id_int)
                except ValueError:
                    invalid_ids.append((row_num, id_value))
                    logger.warning(f"第{row_num}行的id值无法转换为整数: '{id_value}'")
        
        if invalid_ids:
            logger.warning(f"发现 {len(invalid_ids)} 个无效的id值")
            for row_num, invalid_id in invalid_ids[:5]:  # 只显示前5个
                logger.warning(f"  行{row_num}: '{invalid_id}'")
            if len(invalid_ids) > 5:
                logger.warning(f"  ... 还有 {len(invalid_ids) - 5} 个无效id")
        
        # 去重
        original_count = len(id_list)
        id_list = list(set(id_list))
        duplicate_count = original_count - len(id_list)
        
        if duplicate_count > 0:
            logger.info(f"去除了 {duplicate_count} 个重复的id")
        
        logger.info(f"成功读取 {len(id_list)} 个有效的id")
        return True, id_list, None
        
    except Exception as e:
        error_msg = f"读取CSV文件时发生错误: {e}"
        logger.error(error_msg)
        return False, [], error_msg


def create_mongodb_connection():
    """
    创建MongoDB连接
    
    Returns:
        tuple: (client, database, collection) 或 (None, None, None) 如果连接失败
    """
    try:
        logger.info("正在连接到MongoDB...")
        logger.info(f"连接地址: {MONGODB_CONFIG['connection_string'].split('@')[1]}")
        
        # 创建MongoDB客户端
        client = MongoClient(
            MONGODB_CONFIG['connection_string'],
            connectTimeoutMS=MONGODB_CONFIG['connection_timeout'],
            serverSelectionTimeoutMS=MONGODB_CONFIG['server_selection_timeout']
        )
        
        # 测试连接
        client.admin.command('ping')
        logger.info("MongoDB连接成功")
        
        # 获取数据库和集合
        database = client[MONGODB_CONFIG['database_name']]
        collection = database[MONGODB_CONFIG['collection_name']]
        
        # 检查集合是否存在
        if MONGODB_CONFIG['collection_name'] not in database.list_collection_names():
            logger.error(f"集合 '{MONGODB_CONFIG['collection_name']}' 不存在")
            return None, None, None
        
        logger.info(f"成功连接到数据库: {MONGODB_CONFIG['database_name']}")
        logger.info(f"目标集合: {MONGODB_CONFIG['collection_name']}")
        
        return client, database, collection
        
    except ConnectionFailure as e:
        logger.error(f"MongoDB连接失败: {e}")
        return None, None, None
    except Exception as e:
        logger.error(f"连接MongoDB时发生未知错误: {e}")
        return None, None, None


def count_matching_records(collection, id_list):
    """
    统计匹配的记录数量
    
    Args:
        collection: MongoDB集合对象
        id_list (list): id列表
        
    Returns:
        tuple: (success, count, error_message)
    """
    try:
        logger.info("正在统计匹配的记录数量...")
        
        # 构建查询条件
        query = {'id': {'$in': id_list}}
        
        # 统计匹配的记录数
        count = collection.count_documents(query)
        
        logger.info(f"找到 {count} 条匹配的记录")
        return True, count, None
        
    except Exception as e:
        error_msg = f"统计记录数量时发生错误: {e}"
        logger.error(error_msg)
        return False, 0, error_msg


def get_user_confirmation(id_count, record_count):
    """
    获取用户确认
    
    Args:
        id_count (int): CSV中的id数量
        record_count (int): 数据库中匹配的记录数量
        
    Returns:
        bool: 用户是否确认删除
    """
    print("\n" + "="*60)
    print("⚠️  删除操作确认")
    print("="*60)
    print(f"📄 CSV文件中的id数量: {id_count}")
    print(f"🗄️  数据库中匹配的记录数量: {record_count}")
    print(f"🗑️  将要删除的记录数量: {record_count}")
    print(f"📊 数据库: {MONGODB_CONFIG['database_name']}")
    print(f"📋 集合: {MONGODB_CONFIG['collection_name']}")
    print("="*60)
    
    if record_count == 0:
        print("ℹ️  没有找到匹配的记录，无需删除")
        return False
    
    if record_count > DELETE_CONFIG['max_delete_count']:
        print(f"❌ 错误: 删除数量 ({record_count}) 超过安全限制 ({DELETE_CONFIG['max_delete_count']})")
        print("   如需删除更多记录，请修改脚本中的 max_delete_count 配置")
        return False
    
    print("⚠️  警告: 此操作将永久删除数据，无法撤销！")
    print("\n请仔细确认以上信息是否正确。")
    
    while True:
        response = input("\n是否继续执行删除操作？(输入 'YES' 确认，'NO' 取消): ").strip()
        
        if response.upper() == 'YES':
            print("✅ 用户确认删除操作")
            return True
        elif response.upper() == 'NO':
            print("❌ 用户取消删除操作")
            return False
        else:
            print("❌ 无效输入，请输入 'YES' 或 'NO'")


def delete_records_batch(collection, id_list):
    """
    批量删除记录
    
    Args:
        collection: MongoDB集合对象
        id_list (list): 要删除的id列表
        
    Returns:
        tuple: (success, deleted_count, error_message)
    """
    try:
        logger.info("开始执行批量删除操作...")
        
        total_ids = len(id_list)
        batch_size = DELETE_CONFIG['batch_size']
        total_deleted = 0
        
        # 分批删除
        for i in range(0, total_ids, batch_size):
            batch_ids = id_list[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (total_ids + batch_size - 1) // batch_size
            
            logger.info(f"正在处理第 {batch_num}/{total_batches} 批，包含 {len(batch_ids)} 个id")
            
            # 构建删除查询
            delete_query = {'id': {'$in': batch_ids}}
            
            # 执行删除
            result = collection.delete_many(delete_query)
            batch_deleted = result.deleted_count
            total_deleted += batch_deleted
            
            logger.info(f"第 {batch_num} 批删除完成，删除了 {batch_deleted} 条记录")
            
            # 显示进度
            progress = (i + len(batch_ids)) / total_ids * 100
            logger.info(f"删除进度: {min(i + len(batch_ids), total_ids)}/{total_ids} ({progress:.1f}%)")
        
        logger.info(f"批量删除操作完成，总共删除了 {total_deleted} 条记录")
        return True, total_deleted, None
        
    except BulkWriteError as e:
        error_msg = f"批量删除操作部分失败: {e.details}"
        logger.error(error_msg)
        return False, 0, error_msg
    except Exception as e:
        error_msg = f"删除记录时发生错误: {e}"
        logger.error(error_msg)
        return False, 0, error_msg


def main():
    """主函数"""
    logger.info("="*60)
    logger.info("MongoDB数据删除脚本启动")
    logger.info(f"目标数据库: {MONGODB_CONFIG['database_name']}")
    logger.info(f"目标集合: {MONGODB_CONFIG['collection_name']}")
    logger.info(f"CSV文件: {DELETE_CONFIG['csv_file']}")
    logger.info("="*60)
    
    try:
        # 1. 读取CSV文件中的id列表
        success, id_list, error_msg = read_ids_from_csv(DELETE_CONFIG['csv_file'])
        if not success:
            logger.error(f"读取CSV文件失败: {error_msg}")
            sys.exit(1)
        
        if not id_list:
            logger.warning("CSV文件中没有有效的id，程序退出")
            sys.exit(0)
        
        # 2. 连接到MongoDB
        client, database, collection = create_mongodb_connection()
        if client is None or database is None or collection is None:
            logger.error("无法连接到数据库，程序退出")
            sys.exit(1)
        
        # 3. 统计匹配的记录数量
        success, record_count, error_msg = count_matching_records(collection, id_list)
        if not success:
            logger.error(f"统计记录数量失败: {error_msg}")
            sys.exit(1)
        
        # 4. 获取用户确认
        if DELETE_CONFIG['require_confirmation']:
            if not get_user_confirmation(len(id_list), record_count):
                logger.info("删除操作已取消")
                sys.exit(0)
        
        # 5. 执行删除操作
        if record_count > 0:
            success, deleted_count, error_msg = delete_records_batch(collection, id_list)
            if not success:
                logger.error(f"删除操作失败: {error_msg}")
                sys.exit(1)
            
            logger.info("="*60)
            logger.info("删除操作完成！")
            logger.info(f"预期删除: {record_count} 条记录")
            logger.info(f"实际删除: {deleted_count} 条记录")
            logger.info("="*60)
        else:
            logger.info("没有找到匹配的记录，无需删除")
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行过程中发生未知错误: {e}")
        sys.exit(1)
    finally:
        # 关闭数据库连接
        if 'client' in locals() and client:
            client.close()
            logger.info("数据库连接已关闭")


if __name__ == '__main__':
    main()
