#!/bin/bash
# Discogs 数据比较 API 服务启动脚本

echo "🚀 启动 Discogs 数据比较 API 服务..."

# 检查Python版本
python_version=$(python3 --version 2>&1)
echo "📋 Python版本: $python_version"

# 检查依赖
echo "📦 检查依赖..."
if ! python3 -c "import flask" 2>/dev/null; then
    echo "❌ Flask未安装，正在安装依赖..."
    pip3 install -r requirements.txt
else
    echo "✅ 依赖检查通过"
fi

# 设置环境变量
export FLASK_APP=api_server.py
export FLASK_ENV=production

# 启动服务
echo "🌐 启动Web服务..."
echo "📖 API文档: http://localhost:5000/"
echo "🔍 健康检查: http://localhost:5000/api/health"
echo "📊 状态查询: http://localhost:5000/api/status"
echo "🎯 示例请求: http://localhost:5000/api/diff/release"
echo ""
echo "按 Ctrl+C 停止服务"
echo "================================"

python3 api_server.py
