#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API 客户端安装脚本

自动安装 python3-discogs-client 依赖并验证安装。

作者：AI Assistant
创建时间：2025-08-03
"""

import subprocess
import sys
import os

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_package(package_name):
    """安装Python包"""
    print(f"📦 正在安装 {package_name}...")
    
    try:
        # 使用pip安装
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ {package_name} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败:")
        print(f"错误信息: {e.stderr}")
        return False

def verify_installation():
    """验证安装"""
    print("🔍 验证安装...")
    
    try:
        import discogs_client
        print(f"✅ python3-discogs-client 导入成功")
        print(f"版本: {discogs_client.__version__ if hasattr(discogs_client, '__version__') else '未知'}")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        import discogs_client
        
        # 创建一个测试客户端（不需要token）
        client = discogs_client.Client('TestApp/1.0')
        print("✅ 客户端创建成功")
        
        # 注意：不进行实际API调用以避免需要token
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def check_existing_installation():
    """检查是否已安装"""
    print("🔍 检查现有安装...")
    
    try:
        import discogs_client
        print("✅ python3-discogs-client 已安装")
        return True
    except ImportError:
        print("ℹ️ python3-discogs-client 未安装")
        return False

def install_from_requirements():
    """从requirements.txt安装所有依赖"""
    print("📋 从requirements.txt安装所有依赖...")
    
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        print(f"❌ 找不到 {requirements_file} 文件")
        return False
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ], capture_output=True, text=True, check=True)
        
        print("✅ 所有依赖安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 依赖安装失败:")
        print(f"错误信息: {e.stderr}")
        return False

def main():
    """主安装函数"""
    print("🚀 Discogs API 客户端安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查现有安装
    already_installed = check_existing_installation()
    
    if already_installed:
        user_input = input("\n已检测到现有安装，是否重新安装？(y/N): ").strip().lower()
        if user_input not in ['y', 'yes']:
            print("⏭️ 跳过安装")
            if verify_installation() and test_basic_functionality():
                print("\n🎉 安装验证完成！")
                return
            else:
                print("\n❌ 现有安装有问题，建议重新安装")
                return
    
    print("\n📦 开始安装过程...")
    
    # 选择安装方式
    print("\n请选择安装方式:")
    print("1. 仅安装 python3-discogs-client")
    print("2. 安装所有依赖（从requirements.txt）")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    success = False
    
    if choice == "1":
        success = install_package("python3-discogs-client")
    elif choice == "2":
        success = install_from_requirements()
    else:
        print("❌ 无效选择")
        sys.exit(1)
    
    if not success:
        print("\n❌ 安装失败")
        sys.exit(1)
    
    # 验证安装
    print("\n🔍 验证安装...")
    if verify_installation() and test_basic_functionality():
        print("\n🎉 安装完成！")
        print("\n📋 下一步:")
        print("1. 运行测试: python test_api_client.py")
        print("2. 使用客户端: python discogs_api_client.py --help")
        print("3. 查看文档: README_discogs_api_client.md")
    else:
        print("\n❌ 安装验证失败")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 用户中断安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装程序出错: {e}")
        sys.exit(1)
