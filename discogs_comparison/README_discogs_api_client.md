# Discogs API 客户端使用说明

## 概述

`discogs_api_client.py` 是一个使用官方 `python3-discogs-client` 库的 Discogs API 客户端，专门用于获取 Discogs release 数据。该脚本移除了所有浏览器自动化相关的代码，仅使用 API 方式获取数据。

## 主要特性

- ✅ **官方库支持**: 使用 `python3-discogs-client` 官方库
- ✅ **多账号轮换**: 支持两个 Discogs 账号自动轮换，避免 API 限制
- ✅ **智能重试机制**: 区分不同错误类型，采用不同的重试策略
- ✅ **批量处理**: 支持批量获取多个 release ID
- ✅ **数据兼容性**: 输出格式与现有数据处理流程完全兼容
- ✅ **错误处理**: 完整的错误分类和处理机制
- ✅ **进度跟踪**: 实时进度报告和统计信息
- ✅ **优雅停止**: 支持 Ctrl+C 优雅停止

## 系统要求

### Python 版本
- Python 3.6+

### 依赖库
```bash
pip install python3-discogs-client
```

### 现有项目依赖
脚本会自动尝试导入现有的枚举类：
```python
from release.enums import Permissions, Status, Source
```

如果导入失败，会使用内置的默认值。

## 配置说明

### Discogs 账号配置

脚本内置了两个 Discogs 账号配置：

```python
DISCOGS_ACCOUNTS = [
    {
        'email': '<EMAIL>',
        'token': 'hhKxjHkkcVYSbsWEscUlBekYWLafpUbvzbUGLtPz',
        'user_agent': 'DiscogsAPIClient/1.0 +https://example.com/contact'
    },
    {
        'email': '<EMAIL>', 
        'token': 'zXwURCsYotIjXROUfIngknavKuMWCjcmODCXuEJs',
        'user_agent': 'DiscogsAPIClient/1.0 +https://example.com/contact'
    }
]
```

### API 配置

```python
DEFAULT_CONFIG = {
    'api': {
        'rate_limit': 1.0,      # 1秒1次请求
        'timeout': 30,          # 请求超时时间
        'max_retries': 3,       # 最大重试次数
        'retry_delay': 2.0,     # 重试延迟
    },
    'batch': {
        'size': 100,            # 批量处理大小
        'progress_interval': 10, # 进度报告间隔
    }
}
```

## 使用方法

### 命令行使用

#### 1. 获取单个 Release
```bash
python discogs_api_client.py --id 123456
```

#### 2. 获取多个 Release
```bash
python discogs_api_client.py --ids 123456,789012,345678
```

#### 3. 从文件读取 ID 列表
```bash
python discogs_api_client.py --file release_ids.txt
```

文件格式（每行一个 ID）：
```
123456
789012
345678
```

#### 4. 指定输出文件
```bash
python discogs_api_client.py --id 123456 --output result.json
```

#### 5. 详细输出模式
```bash
python discogs_api_client.py --id 123456 --verbose
```

#### 6. 自定义配置
```bash
python discogs_api_client.py --id 123456 --rate-limit 2.0 --max-retries 5
```

### 编程接口使用

#### 1. 基本使用
```python
from discogs_api_client import ReleaseDataFetcher

# 创建获取器
fetcher = ReleaseDataFetcher()

# 获取单个 release
result = fetcher.fetch_single_release(123456)
if result.success:
    print(f"获取成功: {result.data['title']}")
else:
    print(f"获取失败: {result.error_message}")
```

#### 2. 批量获取
```python
# 批量获取多个 release
release_ids = [123456, 789012, 345678]
results = fetcher.fetch_batch_releases(release_ids)

# 处理结果
for result in results:
    if result.success:
        print(f"Release {result.release_id}: {result.data['title']}")
    else:
        print(f"Release {result.release_id} 失败: {result.error_message}")
```

#### 3. 带进度回调的批量获取
```python
def progress_callback(current, total, result):
    print(f"进度: {current}/{total}, 当前: {result.release_id}")

results = fetcher.fetch_batch_releases(release_ids, progress_callback)
```

#### 4. 自定义账号配置
```python
custom_accounts = [
    {
        'email': '<EMAIL>',
        'token': 'your_discogs_token',
        'user_agent': 'YourApp/1.0'
    }
]

fetcher = ReleaseDataFetcher(custom_accounts)
```

## 数据格式

### 输出数据结构

脚本输出的数据格式与现有的 `api_release_补全器.py` 完全兼容：

```python
{
    'id': 123456,
    'title': 'Album Title',
    'country': 'US',
    'master_id': 789012,
    'year': 2023,
    'notes': 'Release notes',
    'discogs_status': 'Accepted',
    
    # 艺术家信息
    'artists': [
        {
            'artist_id': 12345,
            'name': 'Artist Name',
            'role': 'Primary',
            'anv': 'Artist Name Variation'  # 可选字段
        }
    ],
    'extra_artists': [...],
    
    # 标签和格式
    'labels': [...],
    'formats': [...],
    'companies': [...],
    
    # 分类和标识
    'genres': [...],
    'styles': [...],
    'identifiers': [...],
    'tracklist': [...],
    
    # 系统字段
    'images': [],
    'images_permissions': 1,
    'delete_status': 1,
    'permissions': 1,
    'source': 1,
    'created_at': '2025-08-03T...',
    'updated_at': '2025-08-03T...'
}
```

### 错误处理

脚本会自动分类和处理不同类型的错误：

- **404 错误**: Release 不存在，不会重试
- **429 错误**: API 频率限制，会等待更长时间后重试
- **网络错误**: 连接问题，会重试
- **认证错误**: Token 问题，不会重试
- **其他错误**: 未知错误，会重试

## 日志和统计

### 日志文件
- 默认日志文件: `discogs_api_client.log`
- 包含详细的执行日志和错误信息

### 统计报告
脚本会提供详细的统计报告：
```
📊 批量获取完成 - 最终统计
============================================================
总处理数量: 100
成功获取: 85
404未找到: 10
429限制: 3
其他错误: 2
成功率: 85.00%
处理速度: 0.95 条/秒
总耗时: 105.26 秒

📋 错误类型统计:
  404: 10
  429: 3
  network: 2
============================================================
```

## 与现有流程集成

### 1. 替换现有 API 调用
可以直接替换 `api_release_补全器.py` 中的 API 调用部分：

```python
# 原有代码
api_data = api_client.get_release(release_id)

# 新代码
fetcher = ReleaseDataFetcher()
result = fetcher.fetch_single_release(release_id)
if result.success:
    api_data = result.data
```

### 2. 数据库集成
输出的数据格式可以直接插入到现有的数据库结构中。

### 3. 批量处理集成
可以集成到现有的批量处理流程中，提供更稳定的 API 调用。

## 故障排除

### 常见问题

1. **ImportError: No module named 'discogs_client'**
   ```bash
   pip install python3-discogs-client
   ```

2. **认证失败**
   - 检查 Discogs token 是否有效
   - 确认账号没有被限制

3. **频率限制**
   - 脚本会自动处理 429 错误
   - 可以调整 `rate_limit` 参数

4. **网络连接问题**
   - 检查网络连接
   - 考虑使用代理（需要修改代码）

### 调试模式
使用 `--verbose` 参数启用详细日志：
```bash
python discogs_api_client.py --id 123456 --verbose
```

## 性能优化建议

1. **合理设置频率限制**: 根据账号等级调整 `rate_limit`
2. **使用多账号**: 脚本支持自动轮换多个账号
3. **批量处理**: 对于大量数据，使用批量获取模式
4. **错误处理**: 合理设置重试次数和延迟时间

## 更新日志

### v1.0.0 (2025-08-03)
- 初始版本发布
- 支持单个和批量 Release 获取
- 多账号轮换机制
- 智能错误处理和重试
- 完整的命令行接口
- 与现有数据流程兼容
