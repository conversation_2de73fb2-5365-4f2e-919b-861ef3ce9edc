<html lang="en"><head>
        <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
      <meta name="ulp-version" content="1.68.7">
    
    
    
    <meta name="robots" content="noindex, nofollow">
    
    
    <link rel="stylesheet" href="https://cdn.auth0.com/ulp/react-components/1.145.7/css/main.cdn.min.css">
    <style id="custom-styles-container">
      
        




        
          :root, .af-custom-form-container .af-form {
    --primary-color: #31312F;
  }
        
      

        
          :root, .af-custom-form-container .af-form {
    --button-font-color: #F5F4EC;
  }
        
      

        
          :root {
    --secondary-button-border-color: #CECDC6;
    --social-button-border-color: #CECDC6;
    --radio-button-border-color: #CECDC6;
  }
        
      

        
          :root {
    --secondary-button-text-color: #31312F;
  }
        
      

        
          :root {
    --link-color: #175CD3;
  }
        
      

        
          :root {
    --title-font-color: #31312F;
  }
        
      

        
          :root {
    --font-default-color: #31312F;
  }
        
      

        
          :root {
    --widget-background-color: #ffffff;
  }
        
      

        
          :root {
    --box-border-color: #CECDC6;
  }
        
      

        
          :root {
    --font-light-color: #6C6B68;
  }
        
      

        
          :root {
    --input-text-color: #31312F;
  }
        
      

        
          :root {
    --input-border-color: #CECDC6;
    --border-default-color: #CECDC6;
  }
        
      

        
          :root {
    --input-background-color: #ffffff;
  }
        
      

        
          :root {
    --icon-default-color: #6C6B68;
  }
        
      

        
          :root {
    --error-color: #CC0000;
    --error-text-color: #ffffff;
  }
        
      

        
          :root {
    --success-color: #00800B;
  }
        
      

        
          :root {
    --base-focus-color: #175CD3;
    --transparency-focus-color: rgba(23,92,211, 0.15);
  }
        
      

        
          :root {
    --base-hover-color: #20201F;
    --transparency-hover-color: rgba(32,32,31, var(--hover-transparency-value));
  }
        
      

        
      




        
          
        
      

        
          html, :root, .af-custom-form-container .af-form {
    font-size: 16px;
    --default-font-size: 16px;
  }
        
      

        
          body {
    --title-font-size: 1.5rem;
    --title-font-weight: var(--font-default-weight);
  }
        
      

        
          .c88132841 {
    font-size: 0.875rem;
    font-weight: var(--font-default-weight);
  }
        
      

        
          .cff379b99 {
    font-size: 0.875rem;
    font-weight: var(--font-default-weight);
  }
  .ulp-passkey-benefit-heading {
    font-size: 1.025rem;
  }
        
      

        
          .ca7f4b502, .c493f8f7c {
    font-size: 1rem;
    font-weight: var(--font-default-weight);
  }
        
      

        
          body {
    --ulp-label-font-size: 1rem;
    --ulp-label-font-weight: var(--font-default-weight);
  }
        
      

        
          .c3a13cc80, .caafa0739, [id^='ulp-container-'] a {
    font-size: 0.875rem;
    font-weight: var(--font-bold-weight) !important;
  }
        
      

        
          .caafa0739, .c3a13cc80 {
      text-decoration: underline;
    }
        
      




        
          :root {
    --button-border-width: 1px;
    --social-button-border-width: 1px;
    --radio-border-width: 1px;
  }
        
      

        
          body {
    --button-border-radius: 4px;
    --radio-border-radius: 4px;
  }
        
      

        
          :root {
    --input-border-width: 1px;
  }
        
      

        
          body {
    --input-border-radius: 4px;
  }

  .af-custom-form-container .af-form {
    --border-radius: 4px;
  }
        
      

        
          :root {
    --border-radius-outer: 8px;
  }
        
      

        
          :root {
    --box-border-width: 0px;
  }
        
      

        
          
        
      




        
          
    body {
      --logo-alignment: 0 auto;
    }
  
        
      

        
          
    .c6c980cc9 {
      content: url('https://login-assets.discogs.com/Discogs-primary-logo.svg');
    }
  
        
      

        
          body {
    --logo-height: 48px;
  }
  .c6c980cc9 {
    height: var(--logo-height);
  }
  
        
      

        
          
    body {
      --header-alignment: center;
    }
  
        
      

        
          
        
      




        
          .caf26be19 {
    --page-background-alignment: center;
  }
        
      

        
          body {
    --page-background-color: #20201F;
  }
        
      

        
          
        
      




      
    </style>
    <style>
    /* By default, hide features for javascript-disabled browsing */
    /* We use !important to override any css with higher specificity */
    /* It is also overriden by the styles in <noscript> in the header file */
    .no-js {
      clip: rect(0 0 0 0);
      clip-path: inset(50%);
      height: 1px;
      overflow: hidden;
      position: absolute;
      white-space: nowrap;
      width: 1px;
    }
  </style>
  <noscript>
    <style>
      /* We use !important to override the default for js enabled */
      /* If the display should be other than block, it should be defined specifically here */
      .js-required { display: none !important; }
      .no-js {
        clip: auto;
        clip-path: none;
        height: auto;
        overflow: auto;
        position: static;
        white-space: normal;
        width: 100%;
      }
      .no-js-container {
        width: var(--prompt-width);
      }
    </style>
  </noscript>
    
    <title>Log in | Discogs Web App</title>
  </head>
  
  
  <body>
    <div class="cc2daca3d caf26be19">
  
<main class="ced903bbf login">
  <section class="c182a72e3 _prompt-box-outer ce3569b35">
    <div class="c37cbdd45 c0d7170bc">
      
    
      <div class="c9bd7c1cd">
        <header class="c29217b8e c90f4c9a0">
          <div title="Discogs (Prod)" id="custom-prompt-logo" style="width: auto !important; height: 60px !important; position: static !important; margin: auto !important; padding: 0 !important; background-color: transparent !important; background-position: center !important; background-size: contain !important; background-repeat: no-repeat !important"></div>
        
          <img class="c6c980cc9 c56dab89d" id="prompt-logo-center" src="https://login-assets.discogs.com/Discogs-primary-logo.svg" alt="Discogs (Prod)">
        
          
            <h1 class="c0fc36dbc cfdf1b839">Welcome</h1>
          
        
          <div class="c88132841 c0e5d9f6f">
            
              <p class="c8bb2f7f6 c93920c54">Log in to Discogs to continue</p>
            
          </div>
        </header>
      
        <div class="cff379b99 c5c47deda">
          
        
          
            
              <form method="POST" class="c02489e96 ce17f0b60" data-form-primary="true">
                <input type="hidden" name="state" value="hKFo2SBkUm9ESWhGWGdiNV9jTEd6cnZtWko4VlVHNFVHSHJsU6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDFFeGJmOXY5MUc0VDU1SEwtamZONWdTekYtTV80NTh1o2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg">
              
                
              
                
              
                <div class="c9ec6708d c3c91a03f">
                  <div class="cc6129e87">
                    
                  
                    
                      
                        <div class="input-wrapper _input-wrapper">
                          <div class="c76639a41 c3ea8634a text cd29731fa c467e32c0" data-action-text="" data-alternate-action-text="">
                            <label class="cbe32b1a4 no-js ccff2bd28 cf8cdf7eb" for="username">
                              Username or email address
                            </label>
                          
                            <input class="input c5f1fe555 c925e4d05" inputmode="text" name="username" id="username" type="text" value="" required="" autocomplete="username" autocapitalize="none" spellcheck="false" autofocus="">
                          
                            <div class="cbe32b1a4 js-required ccff2bd28 cf8cdf7eb" data-dynamic-label-for="username" aria-hidden="true">
                              Username or email address*
                            </div>
                          </div>
                        
                          
                        </div>
                      
                    
                  
                    
                      <div class="input-wrapper _input-wrapper">
                        <div class="c76639a41 c3ea8634a password c36d5b2c3 c467e32c0" data-action-text="" data-alternate-action-text="">
                          <label class="cbe32b1a4 no-js ccff2bd28 c01b03e28" for="password">
                            Password
                          </label>
                        
                          <input class="input c5f1fe555 c339ec039" name="password" id="password" type="password" required="" autocomplete="current-password" autocapitalize="none" spellcheck="false">
                        
                          <div class="cbe32b1a4 js-required ccff2bd28 c01b03e28" data-dynamic-label-for="password" aria-hidden="true">
                            Password*
                          </div>
                        
                          
                            <button type="button" class="ca7f4b502 ulp-button-icon c4df18aeb _button-icon" data-action="toggle">
                              <span aria-hidden="true" class="password-icon-tooltip show-password-tooltip">Show password</span>
                            
                              <span aria-hidden="true" class="password-icon-tooltip hide-password-tooltip hide">Hide password</span>
                            
                              <span class="screen-reader-only password-toggle-label" data-label="show-password">Show password</span>
                            
                              <span class="screen-reader-only password-toggle-label hide" data-label="hide-password">Hide password</span>
                            
                              <span class="cda6ec3cf password js-required" aria-hidden="true"></span>
                            </button>
                          
                        </div>
                      
                        
                      </div>
                    
                  
                    
                  </div>
                </div>
              
                
                  
                    <p class="c5dab77ba c6f98dee3">
                      
                        <a class="caafa0739 cbaad237f c596deeac" href="/u/login/password-reset-start/Username-Password-Authentication?state=hKFo2SBkUm9ESWhGWGdiNV9jTEd6cnZtWko4VlVHNFVHSHJsU6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDFFeGJmOXY5MUc0VDU1SEwtamZONWdTekYtTV80NTh1o2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg">Forgot password?</a>
                      
                    </p>
                  
                
              
                
              
                <div class="ce8833b8a">
                  
                    <button type="submit" name="action" value="default" class="ca7f4b502 caeb75e5c c4df18aeb cf1f014b6 cb9e86ba7" data-action-button-primary="true">Continue</button>
                  
                </div>
              </form>
            
          
        
          
        
          
            <div class="ulp-alternate-action  _alternate-action __s16nu9">
              <p class="c8bb2f7f6 c93920c54 ceb0f0278">Don't have an account?
                <a class="caafa0739 c596deeac" href="/u/signup?state=hKFo2SBkUm9ESWhGWGdiNV9jTEd6cnZtWko4VlVHNFVHSHJsU6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDFFeGJmOXY5MUc0VDU1SEwtamZONWdTekYtTV80NTh1o2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg">Sign up</a>
              </p>
            </div>
          
        
          
        
          
            <div class="c168af9a4 cbee03823">
              <span>Or</span>
            </div>
          
        
          
            <div class="cdd589b4b c2bc65b66">
              
            
              
                <form method="post" data-provider="google" class="c493f8f7c c4826061d cc48bbcac" data-form-secondary="true">
                  <input type="hidden" name="state" value="hKFo2SBkUm9ESWhGWGdiNV9jTEd6cnZtWko4VlVHNFVHSHJsU6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDFFeGJmOXY5MUc0VDU1SEwtamZONWdTekYtTV80NTh1o2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg">
                
                  <input type="hidden" name="connection" value="google-oauth2">
                
                  <button type="submit" class="c3c69580c c964658dc cd8bdb79b" data-provider="google" data-action-button-secondary="true">
                    
                      <span class="ce97ec77d ca01af166" data-provider="google"></span>
                    
                  
                    <span class="cc2ee83ee">Continue with Google</span>
                  </button>
                </form>
              
                <form method="post" data-provider="apple" class="c493f8f7c c4826061d cc4df7555" data-form-secondary="true">
                  <input type="hidden" name="state" value="hKFo2SBkUm9ESWhGWGdiNV9jTEd6cnZtWko4VlVHNFVHSHJsU6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDFFeGJmOXY5MUc0VDU1SEwtamZONWdTekYtTV80NTh1o2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg">
                
                  <input type="hidden" name="connection" value="apple">
                
                  <button type="submit" class="c3c69580c c964658dc cbbca31b8" data-provider="apple" data-action-button-secondary="true">
                    
                      <span class="ce97ec77d ca01af166" data-provider="apple"></span>
                    
                  
                    <span class="cc2ee83ee">Continue with Apple</span>
                  </button>
                </form>
              
                <form method="post" data-provider="facebook" class="c493f8f7c c4826061d c97ddce64" data-form-secondary="true">
                  <input type="hidden" name="state" value="hKFo2SBkUm9ESWhGWGdiNV9jTEd6cnZtWko4VlVHNFVHSHJsU6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDFFeGJmOXY5MUc0VDU1SEwtamZONWdTekYtTV80NTh1o2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg">
                
                  <input type="hidden" name="connection" value="facebook">
                
                  <button type="submit" class="c3c69580c c964658dc c9cd33abf" data-provider="facebook" data-action-button-secondary="true">
                    
                      <span class="ce97ec77d ca01af166" data-provider="facebook"></span>
                    
                  
                    <span class="cc2ee83ee">Continue with Facebook</span>
                  </button>
                </form>
              
            
              
            </div>
          
        </div>
      </div>
    </div>
  
    
  </section>
</main>
<script id="client-scripts">
window.ulpFlags = {"enable_ulp_wcag_compliance":false,"enable_ulp_rtl_support":false};!function(){var e,t,r,n,S,A,F,T,k,L,a,q,P,N,j,R,O,i,o,c={exports:function(e,t){return"object"==typeof e.ulpFlags&&null!==e.ulpFlags?e.ulpFlags:{}}}.exports(window,document),u=((e={}).exports=function(r,l){var n={},a={};function c(e,t){if(e.classList)return e.classList.add(t);var r=e.className.split(" ");-1===r.indexOf(t)&&(r.push(t),e.className=r.join(" "))}function i(e,t,r,n){return e.addEventListener(t,r,n)}function o(e){return"string"==typeof e}function u(e,t){return o(e)?l.querySelector(e):e.querySelector(t)}function s(e,t){if(e.classList)return e.classList.remove(t);var r=e.className.split(" "),n=r.indexOf(t);-1!==n&&(r.splice(n,1),e.className=r.join(" "))}function f(e,t){return e.getAttribute(t)}function d(e,t,r){return e.setAttribute(t,r)}function p(e){return e.remove()}var e=["text","number","email","password","tel","url"],t="select,textarea,"+e.map(function(e){return'input[type="'+e+'"]'}).join(",");return{addClass:c,toggleClass:function(e,t,r){if(!0===r||!1===r)return n=e,a=t,!0!==r?s(n,a):c(n,a);var n,a;if(e.classList)return e.classList.toggle(t);var i=e.className.split(" "),o=i.indexOf(t);-1!==o?i.splice(o,1):i.push(t),e.className=i.join(" ")},hasClass:function(e,t){return e.classList?e.classList.contains(t):-1!==e.className.split(" ").indexOf(t)},addClickListener:function(e,t){return i(e,"click",t)},addEventListener:i,getAttribute:f,hasAttribute:function(e,t){return e.hasAttribute(t)},getElementById:function(e){return l.getElementById(e)},getParent:function(e){return e.parentNode},isString:o,loadScript:function(e,t){var r=l.createElement("script");for(var n in t)n.startsWith("data-")?r.dataset[n.replace("data-","")]=t[n]:r[n]=t[n];r.src=e,l.body.appendChild(r)},removeScript:function(e){l.querySelectorAll('script[src="'+e+'"]').forEach(function(e){e.remove()})},poll:function(e){var i=e.interval||2e3,t=e.url||r.location.href,o=e.condition||function(){return!0},c=e.onSuccess||function(){},u=e.onError||function(){};return setTimeout(function n(){if(l.hidden)return setTimeout(n,i);var a=new XMLHttpRequest;return a.open("GET",t),a.setRequestHeader("Accept","application/json"),a.onload=function(){if(200===a.status){var e="application/json"===a.getResponseHeader("Content-Type").split(";")[0]?JSON.parse(a.responseText):a.responseText;return o(e)?c():setTimeout(n,i)}if(429!==a.status)return u({status:a.status,responseText:a.responseText});var t=1e3*Number.parseInt(a.getResponseHeader("X-RateLimit-Reset")),r=t-(new Date).getTime();return setTimeout(n,i<r?r:i)},a.send()},i)},querySelector:u,querySelectorAll:function(e,t){var r=o(e)?l.querySelectorAll(e):e.querySelectorAll(t);return Array.prototype.slice.call(r)},removeClass:s,removeElement:p,setAttribute:d,removeAttribute:function(e,t){return e.removeAttribute(t)},swapAttributes:function(e,t,r){var n=f(e,t),a=f(e,r);d(e,r,n),d(e,t,a)},setGlobalFlag:function(e,t){n[e]=!!t},getGlobalFlag:function(e){return!!n[e]},setSubmittedForm:function(e,t){a[e]=t},getSubmittedForm:function(e){return a[e]},preventFormSubmit:function(e){e.stopPropagation(),e.preventDefault()},matchMedia:function(e){return"function"!=typeof r.matchMedia&&r.matchMedia(e).matches},dispatchEvent:function(e,t,r){var n;"function"!=typeof Event?(n=l.createEvent("Event")).initCustomEvent(t,r,!1):n=new Event(t,{bubbles:r}),e.dispatchEvent(n)},timeoutPromise:function(e,a){return new Promise(function(t,r){var n=setTimeout(function(){r(new Error("timeoutPromise: promise timed out"))},e);a.then(function(e){clearTimeout(n),t(e)},function(e){clearTimeout(n),r(e)})})},createMutationObserver:function(e){return"undefined"==typeof MutationObserver?null:new MutationObserver(e)},consoleWarn:function(){(console.warn||console.log).apply(console,arguments)},getConfigJson:function(e){try{var t=u(e);if(!t)return null;var r=t.value;return r?JSON.parse(r):null}catch(e){return null}},getCSSVariable:function(e){return getComputedStyle(l.documentElement).getPropertyValue(e)},removeAndTrimString:function(e,t){var r=new RegExp(t,"g"),n=e.replace(r,"");return n=n.replace(/\s+/g,"  ").trim()},htmlEncode:function(e){var t=l.createTextNode(e),r=l.createElement("span");return r.appendChild(t),r.innerHTML||""},cleanServerErrorMessage:function(e,t){0<e.length&&0<t.length&&t.forEach(function(e){p(e)})},setTimeout:setTimeout,globalWindow:r,SUPPORTED_INPUT_TYPES:e,ELEMENT_TYPE_SELECTOR:t,RUN_INIT:!0}},e.exports)(window,document),l=function(){var e={};function h(e){if(!("string"==typeof e||e instanceof String)){var t=typeof e;throw null===e?t="null":"object"===t&&(t=e.constructor.name),new TypeError("Expected a string but received a "+t)}}function v(e,t){var r,n;h(e),n="object"==typeof t?(r=t.min||0,t.max):(r=t,arguments[2]);var a=encodeURI(e).split(/%..|./).length-1;return r<=a&&(void 0===n||a<=n)}function m(e,t){for(var r in void 0===e&&(e={}),t)void 0===e[r]&&(e[r]=t[r]);return e}var g={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1},t="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",r="("+t+"[.]){3}"+t,n=new RegExp("^"+r+"$"),a="(?:[0-9a-fA-F]{1,4})",i=new RegExp("^((?:"+a+":){7}(?:"+a+"|:)|(?:"+a+":){6}(?:"+r+"|:"+a+"|:)|(?:"+a+":){5}(?::"+r+"|(:"+a+"){1,2}|:)|(?:"+a+":){4}(?:(:"+a+"){0,1}:"+r+"|(:"+a+"){1,3}|:)|(?:"+a+":){3}(?:(:"+a+"){0,2}:"+r+"|(:"+a+"){1,4}|:)|(?:"+a+":){2}(?:(:"+a+"){0,3}:"+r+"|(:"+a+"){1,5}|:)|(?:"+a+":){1}(?:(:"+a+"){0,4}:"+r+"|(:"+a+"){1,6}|:)|(?::((?::"+a+"){0,5}:"+r+"|(?::"+a+"){1,7}|:)))(%[0-9a-zA-Z-.:]{1,})?$");function b(e,t){return void 0===t&&(t=""),h(e),(t=String(t))?"4"===t?n.test(e):"6"===t&&i.test(e):b(e,4)||b(e,6)}var w={allow_display_name:!1,allow_underscores:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0,blacklisted_chars:"",ignore_max_length:!1,host_blacklist:[],host_whitelist:[]},_=/^([^\x00-\x1F\x7F-\x9F\cX]+)</i,x=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,E=/^[a-z\d]+$/,y=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,C=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A1-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,S=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i,A=254;function o(e,t){if(h(e),(t=m(t,w)).require_display_name||t.allow_display_name){var r=e.match(_);if(r){var n=r[1];if(e=e.replace(n,"").replace(/(^<|>$)/g,""),n.endsWith(" ")&&(n=n.slice(0,-1)),!function(e){var t=e.replace(/^"(.+)"$/,"$1");if(!t.trim())return!1;if(/[\.";<>]/.test(t)){if(t===e)return!1;if(t.split('"').length!==t.split('\\"').length)return!1}return!0}(n))return!1}else if(t.require_display_name)return!1}if(!t.ignore_max_length&&e.length>A)return!1;var a=e.split("@"),i=a.pop(),o=i.toLowerCase();if(t.host_blacklist.includes(o))return!1;if(0<t.host_whitelist.length&&!t.host_whitelist.includes(o))return!1;var c=a.join("@");if(t.domain_specific_validation&&("gmail.com"===o||"googlemail.com"===o)){var u=(c=c.toLowerCase()).split("+")[0];if(!v(u.replace(/\./g,""),{min:6,max:30}))return!1;for(var l=u.split("."),s=0;s<l.length;s++)if(!E.test(l[s]))return!1}if(!(!1!==t.ignore_max_length||v(c,{max:64})&&v(i,{max:254})))return!1;if(!function(e,t){h(e),(t=m(t,g)).allow_trailing_dot&&"."===e[e.length-1]&&(e=e.substring(0,e.length-1)),!0===t.allow_wildcard&&0===e.indexOf("*.")&&(e=e.substring(2));var r=e.split("."),n=r[r.length-1];if(t.require_tld){if(r.length<2)return!1;if(!t.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(n))return!1;if(/\s/.test(n))return!1}return!(!t.allow_numeric_tld&&/^\d+$/.test(n))&&r.every(function(e){return!(63<e.length&&!t.ignore_max_length||!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(e)||/[\uff01-\uff5e]/.test(e)||/^-|-$/.test(e)||!t.allow_underscores&&/_/.test(e))})}(i,{require_tld:t.require_tld,ignore_max_length:t.ignore_max_length,allow_underscores:t.allow_underscores})){if(!t.allow_ip_domain)return!1;if(!b(i)){if(!i.startsWith("[")||!i.endsWith("]"))return!1;var f=i.slice(1,-1);if(0===f.length||!b(f))return!1}}if('"'===c[0])return c=c.slice(1,c.length-1),t.allow_utf8_local_part?S.test(c):y.test(c);for(var d=t.allow_utf8_local_part?C:x,p=(l=c.split("."),0);p<l.length;p++)if(!d.test(l[p]))return!1;return!t.blacklisted_chars||-1===c.search(new RegExp("["+t.blacklisted_chars+"]+","g"))}return e.exports=function(e,t){return{ulpRequiredFunction:function(e,t){return!t||!!e.value},ulpEmailValidationFunction:function(e,t){return!t||!e.value||!!o(e.value)},ulpPatternCheckFunction:function(e,t){return!t||!e.value||function(e){if("password"===e.name)return!0;var t=e.getAttribute("pattern");return!t||null!==e.value.match(t)}(e)}}},e.exports}()(window,document);((t={}).exports=function(n,e,o,c,u,l,s,f,t){function d(e){"Escape"==e.code&&document.activeElement.blur()}t.enable_ulp_wcag_compliance&&e("div.c76639a41.password").forEach(function(e){var a,i,t=n(e,"input"),r=n(e,'[data-action="toggle"]');o(e,(a=t,i=r,function(e){if(e.target.classList.contains("ulp-button-icon")){if(a.type="password"===a.type?"text":"password",i){i.ariaChecked="false"===i.ariaChecked?"true":"false";var t=i.querySelector(".show-password-tooltip"),r=i.querySelector(".hide-password-tooltip");t&&s(t,"hide"),r&&s(r,"hide")}var n=f(a);"text"===a.type?u(n,"show"):l(n,"show")}})),c(r,"keyup",d)})},t.exports)(u.querySelector,u.querySelectorAll,u.addClickListener,u.addEventListener,u.addClass,u.removeClass,u.toggleClass,u.getParent,c),((r={}).exports=function(n,e,o,c,u,l,s,t){t.enable_ulp_wcag_compliance||e("div.c76639a41.password").forEach(function(e){var a,i,t=n(e,"input"),r=n(e,'[data-action="toggle"]');o(e,(a=t,i=r,function(e){if(e.target.classList.contains("ulp-button-icon")){if(a.type="password"===a.type?"text":"password",i){var t=i.querySelector(".show-password-tooltip"),r=i.querySelector(".hide-password-tooltip");t&&l(t,"hide"),r&&l(r,"hide")}var n=s(a);"text"===a.type?c(n,"show"):u(n,"show")}}))})},r.exports)(u.querySelector,u.querySelectorAll,u.addClickListener,u.addClass,u.removeClass,u.toggleClass,u.getParent,c),{exports:function(e,n,a,t){var r=e(".ced903bbf"),i=e("#alert-trigger"),o=e(".c4a8389e8"),c=e(".cbae56aa8"),u=!1;i&&c&&r&&t(r,function(e){var t=e.target===i,r=c.contains(e.target);return t&&!u?(n(o,"show"),void(u=!0)):t&&u||u&&!r?(a(o,"show"),void(u=!1)):void 0})}}.exports(u.querySelector,u.addClass,u.removeClass,u.addClickListener),(S="recaptcha_v2",A="recaptcha_enterprise",F="hcaptcha",T="friendly_captcha",k="arkose",L="auth0_v2",(n={}).exports=function(i,o,a,c,u,l,s,f,e){if(!e.enable_ulp_rtl_support){var d=500,p=3,h=0,v=a("div[data-captcha-sitekey]"),m=a(".ulp-captcha-client-error"),t=a("div[data-captcha-sitekey] input");v&&function(){var e="captchaCallback_"+Math.floor(1000001*Math.random()),t=_(),r={async:!0,defer:!0},n=function(e,t,r,n){switch(_()){case S:return"https://www.recaptcha.net/recaptcha/api.js?render=explicit&hl="+e+"&onload="+t;case A:return"https://www.recaptcha.net/recaptcha/enterprise.js?render=explicit&hl="+e+"&onload="+t;case F:return"https://js.hcaptcha.com/1/api.js?render=explicit&hl="+e+"&onload="+t;case T:return"https://cdn.jsdelivr.net/npm/friendly-challenge@0.9.14/widget.min.js";case k:return"https://"+r+".arkoselabs.com/v2/"+n+"/api.js";case L:return"https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit&onload="+t}}(w(),e,v.getAttribute("data-captcha-client-subdomain"),x());if(t===k||t===L){r["data-callback"]=e,r.onerror=function(){if(h<p)return o(n),i(n,r),void h++;o(n),y("BYPASS_CAPTCHA")};var a=function(e){var t,r;t=e,r=function(e){setTimeout(function(){t.run()},d),e.preventDefault()},E().addEventListener("submit",r),t.setConfig({onCompleted:function(e){y(e.token),E().submit()},onError:function(e){return fetch("https://status.arkoselabs.com/api/v2/status.json").then(function(e){return e.json()}).then(function(e){var t=e.status.indicator;return"none"===t}).catch(function(e){return!1}).then(function(e){if(e&&h<p)return t.reset(),new Promise(function(e){setTimeout(function(){e(t.run())},d),h++});y("BYPASS_CAPTCHA"),E().removeEventListener("submit",r)})}})};t===L&&(a=function(){C()}),window[e]=a}else window[e]=function(){delete window[e],C()},t===T&&(c(b(),"frc-captcha"),s(b(),"data-sitekey",x()),r.onload=window[e]);i(n,r)}()}function g(){switch(_()){case S:return window.grecaptcha;case A:return window.grecaptcha.enterprise;case F:return window.hcaptcha;case T:return window.friendlyChallenge;case k:return window.arkose;case L:return window.turnstile}}function b(){return a(function(){switch(_()){case S:case A:return"#ulp-recaptcha";case F:return"#ulp-hcaptcha";case T:return"#ulp-friendly-captcha";case k:return"#ulp-arkose";case L:return"#ulp-auth0-v2-captcha"}}())}function w(){return v.getAttribute("data-captcha-lang")}function _(){return v.getAttribute("data-captcha-provider")}function x(){return v.getAttribute("data-captcha-sitekey")}function E(){return a('form[data-form-primary="true"]')}function y(e){return t.value=e}function C(){var e=g(),t=l("--ulp-captcha-widget-theme")||"light";if(_()===T)"dark"===t&&c(a(".frc-captcha"),"dark"),(n=e.autoWidget).opts.language=w(),n.opts.doneCallback=function(e){y(e)};else{var r={sitekey:x(),theme:t,"expired-callback":function(){y(""),c(v,"ce0c44fca"),e.reset(n)},callback:function(e){y(e),u(v,"ce0c44fca")}};_()===L&&(r.language=w(),r.retry="never",r.size="flexible",r["response-field"]=!1,r["error-callback"]=function(e){return console.error("ERROR: Auth Challenge Error Code",e),y(""),h<p?(h++,g().reset(n)):(m.innerHTML=m.innerHTML.replace("#{errorCode}",f(e)),c(v,"ce0c44fca"),u(m,"hide")),!0});var n=e.render(b(),r)}}},n.exports)(u.loadScript,u.removeScript,u.querySelector,u.addClass,u.removeClass,u.getCSSVariable,u.setAttribute,u.htmlEncode,c),(q="recaptcha_v2",P="recaptcha_enterprise",N="hcaptcha",j="friendly_captcha",R="arkose",O="auth0_v2",(a={}).exports=function(i,o,a,c,u,l,s,f,e){if(e.enable_ulp_rtl_support){var d=500,p=3,h=0,v=a("div[data-captcha-sitekey]"),m=a(".ulp-captcha-client-error"),t=a("div[data-captcha-sitekey] input");v&&function(){var e="captchaCallback_"+Math.floor(1000001*Math.random()),t=_(),r={async:!0,defer:!0},n=function(e,t,r,n){switch(_()){case q:return"https://www.recaptcha.net/recaptcha/api.js?render=explicit&hl="+e+"&onload="+t;case P:return"https://www.recaptcha.net/recaptcha/enterprise.js?render=explicit&hl="+e+"&onload="+t;case N:return"https://js.hcaptcha.com/1/api.js?render=explicit&hl="+e+"&onload="+t;case j:return"https://cdn.jsdelivr.net/npm/friendly-challenge@0.9.14/widget.min.js";case R:return"https://"+r+".arkoselabs.com/v2/"+n+"/api.js";case O:return"https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit&onload="+t}}(w(),e,v.getAttribute("data-captcha-client-subdomain"),x());if(t===R||t===O){r["data-callback"]=e,r.onerror=function(){if(h<p)return o(n),i(n,r),void h++;o(n),y("BYPASS_CAPTCHA")};var a=function(e){var t,r;t=e,r=function(e){setTimeout(function(){t.run()},d),e.preventDefault()},E().addEventListener("submit",r),t.setConfig({language:w(),onCompleted:function(e){y(e.token),E().submit()},onError:function(e){return fetch("https://status.arkoselabs.com/api/v2/status.json").then(function(e){return e.json()}).then(function(e){var t=e.status.indicator;return"none"===t}).catch(function(e){return!1}).then(function(e){if(e&&h<p)return t.reset(),new Promise(function(e){setTimeout(function(){e(t.run())},d),h++});y("BYPASS_CAPTCHA"),E().removeEventListener("submit",r)})}})};t===O&&(a=function(){C()}),window[e]=a}else window[e]=function(){delete window[e],C()},t===j&&(c(b(),"frc-captcha"),s(b(),"data-sitekey",x()),s(b(),"data-lang",w()),r.onload=window[e]);i(n,r)}()}function g(){switch(_()){case q:return window.grecaptcha;case P:return window.grecaptcha.enterprise;case N:return window.hcaptcha;case j:return window.friendlyChallenge;case R:return window.arkose;case O:return window.turnstile}}function b(){return a(function(){switch(_()){case q:case P:return"#ulp-recaptcha";case N:return"#ulp-hcaptcha";case j:return"#ulp-friendly-captcha";case R:return"#ulp-arkose";case O:return"#ulp-auth0-v2-captcha"}}())}function w(){return v.getAttribute("data-captcha-lang")}function _(){return v.getAttribute("data-captcha-provider")}function x(){return v.getAttribute("data-captcha-sitekey")}function E(){return a('form[data-form-primary="true"]')}function y(e){return t.value=e}function C(){var e=g(),t=l("--ulp-captcha-widget-theme")||"light";if(_()===j)"dark"===t&&c(a(".frc-captcha"),"dark"),(n=e.autoWidget).opts.language=w(),n.opts.doneCallback=function(e){y(e)};else{var r={sitekey:x(),theme:t,"expired-callback":function(){y(""),c(v,"ce0c44fca"),e.reset(n)},callback:function(e){y(e),u(v,"ce0c44fca")}};_()===O&&(r.language=w(),r.retry="never",r.size="flexible",r["response-field"]=!1,r["error-callback"]=function(e){return console.error("ERROR: Auth Challenge Error Code",e),y(""),h<p?(h++,g().reset(n)):(m.innerHTML=m.innerHTML.replace("#{errorCode}",f(e)),c(v,"ce0c44fca"),u(m,"hide")),!0});var n=e.render(b(),r)}}},a.exports)(u.loadScript,u.removeScript,u.querySelector,u.addClass,u.removeClass,u.getCSSVariable,u.setAttribute,u.htmlEncode,c),((i={}).exports=function(n,e,a,i,o,c,u,l,r,s,t){if(!t.enable_ulp_wcag_compliance){if(n("body._simple-labels"))return e(".cbe32b1a4.no-js").forEach(function(e){o(e,"no-js")}),void e(".cbe32b1a4.js-required").forEach(function(e){i(e,"hide")});e(".c76639a41:not(.c95dcec96):not(disabled)").forEach(function(e){i(e,"c467e32c0");var t,r=n(e,".input");r.value&&i(e,"ca4a89b91"),a(e,"change",f),a(r,"blur",f),a(r,"animationstart",d),t=r,u(function(){t.value&&l(t,"change",!0)},100)})}function f(e){var t=e.target,r=c(t);t.value||s(t,"data-autofilled")?i(r,"ca4a89b91"):o(r,"ca4a89b91")}function d(e){var t=e.target;"onAutoFillStart"===e.animationName&&(r(t,"data-autofilled",!0),l(e.target,"change",!0),a(t,"keyup",p,{once:!0}))}function p(e){var t=e.target;r(t,"data-autofilled","")}},i.exports)(u.querySelector,u.querySelectorAll,u.addEventListener,u.addClass,u.removeClass,u.getParent,u.setTimeout,u.dispatchEvent,u.setAttribute,u.getAttribute,c),{exports:function(e,t,n,a,i,o,c,u){function r(e){var t=n("submitted"),r=i("submittedForm");a("submitted",!0),o("submittedForm",e.currentTarget),t&&r&&r===e.currentTarget?c(e):"apple"===u(e.target,"data-provider")&&setTimeout(function(){a("submitted",!1)},2e3)}var l=e("form");l&&l.forEach(function(e){t(e,"submit",r)})}}.exports(u.querySelectorAll,u.addEventListener,u.getGlobalFlag,u.setGlobalFlag,u.getSubmittedForm,u.setSubmittedForm,u.preventFormSubmit,u.getAttribute),{exports:function(r,e,n,a,i,o,c,u,l,t,s,f){if(f.enable_ulp_wcag_compliance){var d=e("[id^='ulp-container-']");if(d&&d.length){var p=t(x);if(p)for(var h=0;h<d.length;h++)p.observe(d[h],{childList:!0,subtree:!0})}x()}function v(e){var t=e.target,r=o(t);t.value||u(t,"data-autofilled")?a(r,"ca4a89b91"):i(r,"ca4a89b91")}function m(e){var t=e.target,r=o(t);a(r,"focus"),_(t,r)}function g(e){var t=e.target,r=o(t);i(r,"focus"),v(e),_(t,r)}function b(e){var t=e.target;l(t,"data-autofilled","")}function w(e){var t=e.target;"onAutoFillStart"===e.animationName&&(l(t,"data-autofilled",!0),dispatchEvent(e.target,"change",!0),n(t,"keyup",b,{once:!0}))}function _(e,t){e.value?a(t,"ca4a89b91"):i(t,"ca4a89b91")}function x(){e(".ulp-field").forEach(function(e){if(!c(e,"c467e32c0")){var t=r(e,s);t&&(a(e,"c467e32c0"),_(t,e),setTimeout(function(){_(t,e)},50),t===document.activeElement&&a(e,"focus"),n(t,"change",v),n(t,"focus",m),n(t,"blur",g),n(t,"animationstart",w))}})}}}.exports(u.querySelector,u.querySelectorAll,u.addEventListener,u.addClass,u.removeClass,u.getParent,u.hasClass,u.getAttribute,u.setAttribute,u.createMutationObserver,u.ELEMENT_TYPE_SELECTOR,c),{exports:function(r,e,n,a,i,o,c,t,u,l){if(!l.enable_ulp_wcag_compliance){var s=e("[id^='ulp-container-']");if(s&&s.length){var f=t(g);if(f)for(var d=0;d<s.length;d++)f.observe(s[d],{childList:!0,subtree:!0});g()}}function p(e){var t=e.target,r=o(t);t.value?a(r,"ca4a89b91"):i(r,"ca4a89b91")}function h(e){var t=e.target,r=o(t);a(r,"focus"),m(t,r)}function v(e){var t=e.target,r=o(t);i(r,"focus"),m(t,r)}function m(e,t){e.value?a(t,"ca4a89b91"):i(t,"ca4a89b91")}function g(){e("[id^='ulp-container-'] .ulp-field").forEach(function(e){if(!c(e,"c467e32c0")){var t=r(e,u);t&&(a(e,"c467e32c0"),m(t,e),setTimeout(function(){m(t,e)},50),t===document.activeElement&&a(e,"focus"),n(t,"change",p),n(t,"focus",h),n(t,"blur",v))}})}}}.exports(u.querySelector,u.querySelectorAll,u.addEventListener,u.addClass,u.removeClass,u.getParent,u.hasClass,u.createMutationObserver,u.ELEMENT_TYPE_SELECTOR,c),{exports:function(n,o,a,i,c,u,l,s,f,d,p,h,t,v,m,e,r,g){if(g.enable_ulp_wcag_compliance){var b=!1,w=e+',input[type="checkbox"]';return F(),[w,_,x,E,y,C,S,A,F]}function _(e){var t=c(e,"data-ulp-validation-function"),r=i(e);return{functionName:t,element:n(r,w),parent:r}}function x(e){var a=[],i=[];return o(e,"[data-ulp-validation-function]").forEach(function(e){var t=_(e),r=[];if(t.element){if("input"===t.element.tagName.toLowerCase()){var n=c(t.element,"type");"checkbox"!==n&&-1===m.indexOf(n)&&r.push("Unsupported input type: "+n)}}else r.push("Could not find element");h[t.functionName]||r.push("Could not find function with name: "+t.functionName),r.length?i=i.concat(r):a.push(e)}),i.length&&t(i.join("\r\n")),a}function E(e,t,r){var n=_(e),a=(0,h[n.functionName])(n.element,t,r);a?s(e,"ulp-validator-error")&&(d(e,"ulp-validator-error"),l(e,"data-is-error")):s(e,"ulp-validator-error")||(f(e,"ulp-validator-error"),u(e,"data-is-error",!0));var i=o(n.parent,".ulp-validator-error");return p(n.parent,"ulp-error",!!i.length),a}function y(t){var r=_(t),e=(c(t,"data-ulp-validation-event-listeners")||"").replace(/\s/g,"").split(",").filter(function(e){return!!e});e.length&&e.forEach(function(e){a(r.element,e,function(){E(t,b,e)})})}function C(e,t,r){b=!0;var n=r.filter(function(e){return!E(e,b,"submit")});if(!n.length)return t.submitter&&"default"==c(t.submitter,"value")&&u(t.submitter,"disabled",!0),void e.submit();v(t);var a=_(n[0]);a.element.focus({preventScroll:!0}),a.parent.scrollIntoView({behavior:"smooth"})}function S(){var t=n('form[data-form-primary="true"]'),r=x(t);0!==r.length&&(r.forEach(function(e){y(e)}),a(t,"submit",function(e){C(t,e,r)}))}function A(){if(r)for(var e in r)r.hasOwnProperty(e)&&(h[e]=r[e])}function F(){var e=n("form[data-disable-html-validations]");e&&(A(),u(e,"novalidate",""),S())}}}.exports(u.querySelector,u.querySelectorAll,u.addEventListener,u.getParent,u.getAttribute,u.setAttribute,u.removeAttribute,u.hasClass,u.addClass,u.removeClass,u.toggleClass,u.globalWindow,u.consoleWarn,u.preventFormSubmit,u.SUPPORTED_INPUT_TYPES,u.ELEMENT_TYPE_SELECTOR,l,c),{exports:function(n,o,a,i,c,u,l,t,s,f,e,r){if(!r.enable_ulp_wcag_compliance){var d=!1,p=e+',input[type="checkbox"]';return _(),[p,h,v,m,g,b,w,_]}function h(e){var t=c(e,"data-ulp-validation-function"),r=i(e);return{functionName:t,element:n(r,p),parent:r}}function v(e){var a=[],i=[];return o(e,"[data-ulp-validation-function]").forEach(function(e){var t=h(e),r=[];if(t.element){if("input"===t.element.tagName.toLowerCase()){var n=c(t.element,"type");"checkbox"!==n&&-1===f.indexOf(n)&&r.push("Unsupported input type: "+n)}}else r.push("Could not find element");l[t.functionName]||r.push("Could not find function with name: "+t.functionName),r.length?i=i.concat(r):a.push(e)}),i.length&&t(i.join("\r\n")),a}function m(e,t,r){var n=h(e),a=(0,l[n.functionName])(n.element,t,r);u(e,"ulp-validator-error",!a);var i=o(n.parent,".ulp-validator-error");return u(n.parent,"ulp-error",!!i.length),a}function g(t){var r=h(t),e=(c(t,"data-ulp-validation-event-listeners")||"").replace(/\s/g,"").split(",").filter(function(e){return!!e});e.length&&e.forEach(function(e){a(r.element,e,function(){m(t,d,e)})})}function b(e,t,r){d=!0;var n=r.filter(function(e){return!m(e,d,"submit")});if(n.length){s(t);var a=h(n[0]);a.element.focus({preventScroll:!0}),a.parent.scrollIntoView({behavior:"smooth"})}else e.submit()}function w(){var t=n('form[data-form-primary="true"]'),r=v(t);0!==r.length&&(r.forEach(function(e){g(e)}),a(t,"submit",function(e){b(t,e,r)}))}function _(){var e=o("[id^='ulp-container-']");e&&e.length&&w()}}}.exports(u.querySelector,u.querySelectorAll,u.addEventListener,u.getParent,u.getAttribute,u.toggleClass,u.globalWindow,u.consoleWarn,u.preventFormSubmit,u.SUPPORTED_INPUT_TYPES,u.ELEMENT_TYPE_SELECTOR,c),{exports:function(e,t,r){function n(r){t(r,"click",function(e){e.preventDefault();var t=document.createElement("input");t.name="action",t.type="hidden",t.value=r.value,r.form.appendChild(t),r.form.submit(),r.form.removeChild(t)})}function a(){e('form button[type="submit"][formnovalidate]').forEach(function(e){n(e)})}return r&&a(),[a,n]}}.exports(u.querySelectorAll,u.addEventListener,u.RUN_INIT),((o={}).exports=function(o,e,c,u,l,s,t,f,r){if(r.enable_ulp_wcag_compliance){var n=e('[class*="aria-error-check"]');if(n&&n.length){var a=t(function(e){e&&e.length&&e.map(function(e){if(e.target&&c(e.target,"aria-error-check")){var t=o('[id="'+u(e.target,"data-ulp-validation-target")+'"');if(t){var r=u(t,"aria-describedby");u(e.target,"data-is-error")?(n=t,a=r,i=e.target.id,a&&-1!==a.search(i)||l(n,"aria-describedby",a?a+" "+i:i),l(n,"aria-invalid",!0)):function(e,t,r){if(t){var n=f(t,r);n.length?l(e,"aria-describedby",n):(s(e,"aria-invalid"),s(e,"aria-describedby"))}else s(e,"aria-invalid"),s(e,"aria-describedby")}(t,r,e.target.id)}}var n,a,i})});a&&n.map(function(e){a.observe(e,{attributes:!0,attributeFilter:["class","data-is-error"]})})}}},o.exports)(u.querySelector,u.querySelectorAll,u.hasClass,u.getAttribute,u.setAttribute,u.removeAttribute,u.createMutationObserver,u.removeAndTrimString,c)}();
</script>
    </div>
  

</body></html>