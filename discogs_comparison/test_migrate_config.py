#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移脚本配置测试
用于验证配置和连接，不执行实际迁移操作
"""

import sys
from migrate_release_data import (
    MONGODB_CONFIG, 
    MIGRATION_CONFIG, 
    create_mongodb_connection,
    validate_collections,
    count_migration_records
)

def test_configuration():
    """测试配置参数"""
    print("="*60)
    print("配置参数测试")
    print("="*60)
    
    print("MongoDB配置:")
    print(f"  数据库: {MONGODB_CONFIG['database_name']}")
    print(f"  源集合: {MONGODB_CONFIG['source_collection']}")
    print(f"  目标集合: {MONGODB_CONFIG['target_collection']}")
    print(f"  连接超时: {MONGODB_CONFIG['connection_timeout']}ms")
    
    print("\n迁移配置:")
    print(f"  批量大小: {MIGRATION_CONFIG['batch_size']}")
    print(f"  筛选条件: status != 429")
    print(f"  启用事务: {MIGRATION_CONFIG['enable_transaction']}")
    print(f"  最大迁移数量: {MIGRATION_CONFIG['max_migration_count']}")
    print(f"  进度报告间隔: {MIGRATION_CONFIG['progress_report_interval']}")
    
    print("\n✅ 配置参数检查通过")

def test_connection():
    """测试数据库连接"""
    print("\n" + "="*60)
    print("数据库连接测试")
    print("="*60)
    
    # 测试连接
    client, database, source_collection, target_collection = create_mongodb_connection()
    
    if client is None:
        print("❌ 数据库连接失败")
        return False
    
    print("✅ 数据库连接成功")
    
    # 测试集合验证
    success, error_msg = validate_collections(database)
    if not success:
        print(f"❌ 集合验证失败: {error_msg}")
        client.close()
        return False
    
    print("✅ 集合验证通过")
    
    # 测试记录统计（不执行迁移）
    success, count, error_msg = count_migration_records(source_collection)
    if not success:
        print(f"❌ 记录统计失败: {error_msg}")
        client.close()
        return False
    
    print(f"✅ 记录统计成功: 找到 {count} 条符合条件的记录")
    
    # 关闭连接
    client.close()
    print("✅ 数据库连接已关闭")
    
    return True

def main():
    """主函数"""
    print("MongoDB数据迁移脚本 - 配置测试")
    print("此测试不会执行实际的数据迁移操作")
    
    try:
        # 测试配置
        test_configuration()
        
        # 测试连接
        if test_connection():
            print("\n" + "="*60)
            print("✅ 所有测试通过！迁移脚本准备就绪")
            print("可以运行 migrate_release_data.py 执行实际迁移")
            print("="*60)
            return True
        else:
            print("\n" + "="*60)
            print("❌ 连接测试失败，请检查配置和网络")
            print("="*60)
            return False
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
