#!/bin/bash
# MongoDB数据导出脚本启动器

echo "========================================"
echo "MongoDB数据导出脚本"
echo "========================================"
echo

echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请确保Python3已安装"
    exit 1
fi

echo "✅ Python环境检查通过"
echo "Python版本: $(python3 --version)"

echo
echo "📦 检查依赖包..."
if ! python3 -c "import pymongo" &> /dev/null; then
    echo "⚠️  pymongo未安装，正在安装依赖..."
    pip3 install pymongo
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败，请手动运行: pip3 install pymongo"
        exit 1
    fi
fi

echo "✅ 依赖检查通过"

echo
echo "🚀 开始执行数据导出..."
echo "📊 导出目标: release_new集合"
echo "📅 日期范围: 2025年7月29日-30日"
echo "📄 导出字段: id"
echo

python3 export_release_data.py

if [ $? -eq 0 ]; then
    echo
    echo "✅ 导出完成！"
    echo "📁 输出文件: release_data_july_29_30.csv"
    echo "📋 日志文件: export_release_data.log"
else
    echo
    echo "❌ 导出失败，请查看日志文件 export_release_data.log"
    exit 1
fi
