@echo off
chcp 65001 >nul
echo ============================================================
echo 阿里云OSS上传功能测试 - Windows批处理脚本
echo ============================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Python未安装或未添加到PATH环境变量
    echo 请先安装Python 3.7+并确保添加到PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 检查是否存在requirements.txt
if exist requirements.txt (
    echo 🔄 检测到requirements.txt，正在安装依赖...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ⚠️  警告: 依赖安装可能存在问题，继续尝试运行测试
    ) else (
        echo ✅ 依赖安装完成
    )
    echo.
) else (
    echo 🔄 未找到requirements.txt，尝试安装oss2...
    pip install oss2
    if %errorlevel% neq 0 (
        echo ❌ 错误: oss2安装失败
        echo 请手动运行: pip install oss2
        pause
        exit /b 1
    )
    echo ✅ oss2安装完成
    echo.
)

REM 检查测试脚本是否存在
if not exist test_oss_upload.py (
    echo ❌ 错误: 测试脚本test_oss_upload.py不存在
    echo 请确保在正确的目录中运行此批处理文件
    pause
    exit /b 1
)

echo 🚀 开始运行OSS上传测试...
echo.

REM 运行测试脚本
python test_oss_upload.py

REM 检查测试结果
if %errorlevel% equ 0 (
    echo.
    echo ============================================================
    echo 🎉 OSS测试成功完成！
    echo ============================================================
) else (
    echo.
    echo ============================================================
    echo ❌ OSS测试失败，请检查配置和网络连接
    echo ============================================================
)

echo.
echo 按任意键退出...
pause >nul
