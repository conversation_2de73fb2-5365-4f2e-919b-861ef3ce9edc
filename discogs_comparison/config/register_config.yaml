# Discogs 自动注册配置文件
# 支持多账号批量注册

# 注册页面配置
registration:
  # 登录页面URL（用于点击Sign up链接）
  login_url: "https://login.discogs.com/u/login"
  # 注册页面URL（备用直接访问）
  url: "https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg"
  timeout_seconds: 30
  retry_attempts: 3
  retry_delay_seconds: 5

# 账号信息配置（支持多个账号）
accounts:
  - username: "ywl2025005"
    email: "<EMAIL>"
    password: "Abcd@1234qaz"
    enabled: true
  # 可以添加更多账号
  - username: "ywl2025006"
    email: "<EMAIL>"
    password: "Abcd@1234qaz"
    enabled: true
  - username: "ywl2025007"
    email: "<EMAIL>"
    password: "Abcd@1234qaz"
    enabled: true
  - username: "ywl2025008"
    email: "<EMAIL>"
    password: "Abcd@1234qaz"
    enabled: true
  - username: "ywl2025009"
    email: "<EMAIL>"
    password: "Abcd@1234."
    enabled: true

# 浏览器配置
browser:
  # 浏览器类型: chrome, firefox, edge, safari
  type: "safari"
  # 是否显示浏览器窗口（False为无头模式）
  show_window: true
  # 窗口大小
  window_size:
    width: 1280
    height: 720
  # 等待时间配置
  implicit_wait: 10
  page_load_timeout: 30
  # User-Agent轮换
  user_agents:
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
    - "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"

# 表单字段选择器配置
selectors:
  username_field: "input[name='username']"
  email_field: "input[name='email']"
  password_field: "input[name='password']"
  confirm_password_field: "input[name='password_confirmation']"
  submit_button: "button[type='submit'], input[type='submit']"
  # 验证码相关选择器
  captcha_container: ".captcha, .recaptcha, [id*='captcha'], [class*='captcha']"
  # 错误消息选择器
  error_messages: ".error, .alert-danger, [class*='error']"
  # 成功消息选择器
  success_messages: ".success, .alert-success, [class*='success']"

# 自动化行为配置
automation:
  # 操作间随机延迟范围（秒）
  min_delay: 1
  max_delay: 3
  # 输入文字时的延迟（秒）
  typing_delay: 0.1
  # 验证码处理
  captcha_handling:
    # 是否启用验证码检测
    enabled: true
    # 检测到验证码时的等待时间（秒）
    wait_time: 60
    # 是否暂停等待人工处理
    manual_intervention: true

# 日志配置
logging:
  level: "INFO"
  log_file: "discogs_register.log"
  max_size_mb: 50
  backup_count: 3
  # 是否记录详细的浏览器操作
  detailed_browser_logs: false

# 代理配置（可选）
proxy:
  enabled: false
  # 代理服务器地址
  # server: "http://proxy.example.com:8080"
  # 代理认证（如果需要）
  # username: ""
  # password: ""

# 成功验证配置
verification:
  # 注册成功后的验证方式
  success_indicators:
    - "registration successful"
    - "account created"
    - "verify your email"
    - "check your email"
    - "email verification"
    - "activate your account"
  # 验证超时时间（秒）
  timeout: 30
