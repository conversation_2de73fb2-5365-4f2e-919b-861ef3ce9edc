# Discogs 数据比较批处理配置文件
# 此文件包含批处理执行的各种配置选项

# 执行配置
execution:
  # 单个任务的超时时间（秒）
  timeout_seconds: 7200  # 2小时
  
  # 失败重试次数
  retry_attempts: 3
  
  # 重试间隔时间（秒）
  retry_delay_seconds: 60

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  level: INFO
  
  # 日志格式
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  
  # 单个日志文件最大大小（MB）
  file_max_size_mb: 100
  
  # 保留的日志文件备份数量
  backup_count: 5

# 支持的数据类型
data_types:
  - release
  - master
  - artists
  - label

# 输出配置
output:
  # 是否启用本地备份
  local_backup: true
  
  # 本地备份保留天数
  backup_days: 7

# 性能配置
performance:
  # 内存使用监控间隔（秒）
  memory_check_interval: 300
  
  # 最大内存使用阈值（GB）
  max_memory_gb: 8

# 通知配置（可选）
notifications:
  # 是否启用邮件通知
  email_enabled: false
  
  # 邮件配置（如果启用）
  email:
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    from_address: ""
    to_addresses: []
  
  # 是否启用钉钉通知
  dingtalk_enabled: false
  
  # 钉钉机器人配置（如果启用）
  dingtalk:
    webhook_url: ""
    secret: ""
