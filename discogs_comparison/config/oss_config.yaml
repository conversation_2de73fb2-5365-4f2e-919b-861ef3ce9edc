# 阿里云OSS配置文件
# 请根据您的阿里云OSS实际配置填写以下信息

# OSS基本配置
# OSS服务端点（根据您的bucket所在地域填写）
# 例如：
# - 华东1（杭州）：oss-cn-hangzhou.aliyuncs.com
# - 华北2（北京）：oss-cn-beijing.aliyuncs.com
# - 华南1（深圳）：oss-cn-shenzhen.aliyuncs.com
endpoint: ""

# 访问密钥ID（从阿里云控制台获取）
access_key_id: ""

# 访问密钥Secret（从阿里云控制台获取）
access_key_secret: ""

# 存储桶名称
bucket_name: ""

# 上传路径前缀（可选，默认为 discogs_comparison/）
upload_path_prefix: "discogs_comparison/"

# 连接配置
# 连接超时时间（秒）
connection_timeout: 30

# 是否启用CRC校验
enable_crc: true

# 分片上传配置
# 分片上传阈值（字节），大于此大小的文件将使用分片上传
multipart_threshold: 104857600  # 100MB

# 分片大小（字节）
part_size: 10485760  # 10MB

# 高级配置
advanced:
  # 最大重试次数
  max_retry_times: 3
  
  # 重试间隔（秒）
  retry_interval: 1
  
  # 是否启用日志
  enable_logging: true
  
  # 自定义域名（如果有）
  custom_domain: ""

# 安全配置
security:
  # 是否使用HTTPS
  use_https: true
  
  # 是否验证SSL证书
  verify_ssl: true

# 示例配置（请删除此部分并填写实际配置）
# endpoint: "oss-cn-hangzhou.aliyuncs.com"
# access_key_id: "LTAI5tXXXXXXXXXXXXXX"
# access_key_secret: "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
# bucket_name: "my-discogs-bucket"
