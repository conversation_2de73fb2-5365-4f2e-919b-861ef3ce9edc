#!/bin/bash
# MongoDB数据删除脚本启动器

echo "========================================"
echo "MongoDB数据删除脚本"
echo "========================================"
echo

echo "⚠️  警告: 此脚本将删除MongoDB中的数据！"
echo "📋 目标数据库: music_test"
echo "📋 目标集合: release_new"
echo "📄 参考文件: release_data_july_29_30.csv"
echo

echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请确保Python3已安装"
    exit 1
fi

echo "✅ Python环境检查通过"
echo "Python版本: $(python3 --version)"

echo
echo "📦 检查依赖包..."
if ! python3 -c "import pymongo" &> /dev/null; then
    echo "⚠️  pymongo未安装，正在安装依赖..."
    pip3 install pymongo
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败，请手动运行: pip3 install pymongo"
        exit 1
    fi
fi

echo "✅ 依赖检查通过"

echo
echo "📄 检查CSV文件..."
if [ ! -f "release_data_july_29_30.csv" ]; then
    echo "❌ 错误: 未找到CSV文件 release_data_july_29_30.csv"
    echo "   请确保CSV文件存在于当前目录"
    exit 1
fi

echo "✅ CSV文件检查通过"

echo
echo "⚠️  最后警告: 此操作将永久删除数据，无法撤销！"
echo "📊 脚本将会："
echo "   1. 读取CSV文件中的id列表"
echo "   2. 在MongoDB中查找匹配的记录"
echo "   3. 显示删除统计信息"
echo "   4. 要求您确认后执行删除"
echo

read -p "确定要继续吗？(输入 Y 继续，任意其他键取消): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 0
fi

echo
echo "🚀 开始执行数据删除脚本..."
echo

python3 delete_release_data.py

if [ $? -eq 0 ]; then
    echo
    echo "✅ 脚本执行完成！"
    echo "📋 详细日志: delete_release_data.log"
else
    echo
    echo "❌ 删除操作失败，请查看日志文件 delete_release_data.log"
    exit 1
fi
