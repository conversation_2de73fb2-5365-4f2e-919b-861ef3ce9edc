#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB数据导出脚本
用于导出release_new集合中7月29日和7月30日的数据
"""

import csv
import sys
from datetime import datetime, timedelta
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('export_release_data.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# MongoDB连接配置
MONGODB_CONFIG = {
    'connection_string': '**********************************************************',
    'database_name': 'music_test',
    'collection_name': 'release_new',
    'connection_timeout': 30000,  # 30秒超时
    'server_selection_timeout': 30000  # 30秒服务器选择超时
}

# 导出配置
EXPORT_CONFIG = {
    'output_file': 'release_data_july_29_30.csv',
    'target_year': 2025,
    'target_month': 7,
    'start_day': 29,
    'end_day': 30,
    'export_fields': ['id'],  # 只导出id字段
    'batch_size': 1000  # 批量处理大小
}


def create_mongodb_connection():
    """
    创建MongoDB连接
    
    Returns:
        tuple: (client, database, collection) 或 (None, None, None) 如果连接失败
    """
    try:
        logger.info("正在连接到MongoDB...")
        logger.info(f"连接地址: {MONGODB_CONFIG['connection_string'].split('@')[1]}")
        
        # 创建MongoDB客户端
        client = MongoClient(
            MONGODB_CONFIG['connection_string'],
            connectTimeoutMS=MONGODB_CONFIG['connection_timeout'],
            serverSelectionTimeoutMS=MONGODB_CONFIG['server_selection_timeout']
        )
        
        # 测试连接
        client.admin.command('ping')
        logger.info("MongoDB连接成功")
        
        # 获取数据库和集合
        database = client[MONGODB_CONFIG['database_name']]
        collection = database[MONGODB_CONFIG['collection_name']]
        
        # 检查集合是否存在
        if MONGODB_CONFIG['collection_name'] not in database.list_collection_names():
            logger.error(f"集合 '{MONGODB_CONFIG['collection_name']}' 不存在")
            return None, None, None
        
        logger.info(f"成功连接到数据库: {MONGODB_CONFIG['database_name']}")
        logger.info(f"目标集合: {MONGODB_CONFIG['collection_name']}")
        
        return client, database, collection
        
    except ConnectionFailure as e:
        logger.error(f"MongoDB连接失败: {e}")
        return None, None, None
    except Exception as e:
        logger.error(f"连接MongoDB时发生未知错误: {e}")
        return None, None, None


def build_date_query():
    """
    构建日期范围查询条件
    
    Returns:
        dict: MongoDB查询条件
    """
    # 构建7月29日和7月30日的日期范围
    start_date = datetime(
        EXPORT_CONFIG['target_year'], 
        EXPORT_CONFIG['target_month'], 
        EXPORT_CONFIG['start_day']
    )
    
    # 结束日期是7月31日00:00:00（不包含）
    end_date = datetime(
        EXPORT_CONFIG['target_year'], 
        EXPORT_CONFIG['target_month'], 
        EXPORT_CONFIG['end_day'] + 1
    )
    
    logger.info(f"查询日期范围: {start_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 构建查询条件
    # 支持多种日期格式：ISO字符串和Date对象
    query = {
        '$or': [
            # 如果created_at是Date对象
            {
                'created_at': {
                    '$gte': start_date,
                    '$lt': end_date
                }
            },
            # 如果created_at是ISO字符串格式
            {
                'created_at': {
                    '$gte': start_date.isoformat(),
                    '$lt': end_date.isoformat()
                }
            },
            # 如果created_at是字符串格式（YYYY-MM-DD开头）
            {
                'created_at': {
                    '$regex': f'^{EXPORT_CONFIG["target_year"]}-{EXPORT_CONFIG["target_month"]:02d}-(29|30)'
                }
            }
        ]
    }
    
    return query


def export_data_to_csv(collection):
    """
    导出数据到CSV文件
    
    Args:
        collection: MongoDB集合对象
        
    Returns:
        bool: 导出是否成功
    """
    try:
        # 构建查询条件
        query = build_date_query()
        
        # 构建投影（只选择需要的字段）
        projection = {field: 1 for field in EXPORT_CONFIG['export_fields']}
        projection['_id'] = 0  # 排除MongoDB的_id字段
        
        logger.info("开始查询数据...")
        logger.info(f"查询条件: {query}")
        logger.info(f"导出字段: {EXPORT_CONFIG['export_fields']}")
        
        # 获取总记录数
        total_count = collection.count_documents(query)
        logger.info(f"找到符合条件的记录数: {total_count}")
        
        if total_count == 0:
            logger.warning("没有找到符合条件的数据")
            return False
        
        # 创建CSV文件
        output_file = EXPORT_CONFIG['output_file']
        logger.info(f"开始导出到文件: {output_file}")
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入表头
            writer.writerow(EXPORT_CONFIG['export_fields'])
            
            # 批量查询和写入数据
            exported_count = 0
            cursor = collection.find(query, projection).batch_size(EXPORT_CONFIG['batch_size'])
            
            for document in cursor:
                # 提取字段值
                row = []
                for field in EXPORT_CONFIG['export_fields']:
                    value = document.get(field, '')
                    row.append(value)
                
                # 写入CSV
                writer.writerow(row)
                exported_count += 1
                
                # 显示进度
                if exported_count % EXPORT_CONFIG['batch_size'] == 0:
                    progress = (exported_count / total_count) * 100
                    logger.info(f"导出进度: {exported_count}/{total_count} ({progress:.1f}%)")
        
        logger.info(f"数据导出完成！")
        logger.info(f"总共导出 {exported_count} 条记录")
        logger.info(f"输出文件: {output_file}")
        
        return True
        
    except OperationFailure as e:
        logger.error(f"MongoDB查询操作失败: {e}")
        return False
    except Exception as e:
        logger.error(f"导出数据时发生错误: {e}")
        return False


def main():
    """主函数"""
    logger.info("="*60)
    logger.info("MongoDB数据导出脚本启动")
    logger.info(f"目标集合: {MONGODB_CONFIG['collection_name']}")
    logger.info(f"导出日期: {EXPORT_CONFIG['target_year']}年{EXPORT_CONFIG['target_month']}月{EXPORT_CONFIG['start_day']}-{EXPORT_CONFIG['end_day']}日")
    logger.info(f"导出字段: {', '.join(EXPORT_CONFIG['export_fields'])}")
    logger.info("="*60)
    
    # 创建数据库连接
    client, database, collection = create_mongodb_connection()

    if client is None or database is None or collection is None:
        logger.error("无法连接到数据库，程序退出")
        sys.exit(1)
    
    try:
        # 导出数据
        success = export_data_to_csv(collection)
        
        if success:
            logger.info("数据导出成功完成！")
            sys.exit(0)
        else:
            logger.error("数据导出失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行过程中发生未知错误: {e}")
        sys.exit(1)
    finally:
        # 关闭数据库连接
        if client:
            client.close()
            logger.info("数据库连接已关闭")


if __name__ == '__main__':
    main()
