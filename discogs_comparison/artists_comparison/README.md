# Discogs Artists 数据比较工具

## 项目概述

这是一个用于比较 Discogs 音乐数据库中 Artists（艺术家）XML 文件的 Python 工具。该工具能够解析两个不同时间点的 Discogs artists XML 压缩文件，识别数据变化，并生成包含变化记录的 CSV 报告。

### 主要功能

- 🔍 **数据比较**：比较两个时间点的艺术家数据，识别新增、删除和修改的记录
- 📊 **大数据处理**：支持处理包含数百万条记录的大型 XML 文件（400MB+ 压缩文件）
- 💾 **高效存储**：使用 SQLite 临时数据库进行内存高效的数据比较
- 📝 **CSV 输出**：生成包含变化记录的 CSV 文件，便于后续分析
- 🚀 **进度显示**：实时显示处理进度和统计信息

### 核心特性

- **完整字段解析**：支持艺术家的所有业务字段，包括复杂的关联数据
- **智能比较**：基于哈希值的高效数据比较算法
- **变化分类**：自动识别 CREATE（新增）、UPDATE（修改）、DELETE（删除）操作
- **数据完整性**：保持原始数据的完整性和准确性
- **错误处理**：robust 的错误处理和异常恢复机制

## 环境要求

- **Python**: 3.7+
- **操作系统**: Linux, macOS, Windows
- **内存**: 建议 4GB+ RAM（处理大文件时）
- **磁盘空间**: 至少 2GB 可用空间（用于临时数据库和输出文件）

## 依赖安装

### 1. 进入项目目录

```bash
cd /path/to/discogs/artists_comparison
```

### 2. 安装依赖

本工具使用 Python 标准库，无需额外安装依赖包：

- `sqlite3` - 数据库操作
- `csv` - CSV 文件处理
- `gzip` - 压缩文件读取
- `re` - 正则表达式
- `json` - JSON 数据处理
- `hashlib` - 哈希计算
- `datetime` - 时间处理

### 3. 导入枚举模块

确保 `enums.py` 文件存在于 `../artists/` 目录中，包含以下枚举定义：

```python
from enum import Enum

class Source(Enum):
    DISCOGS = 1
    DISKUNION = 2
    CUSTOM = 3

class Permissions(Enum):
    ALL_VISIBLE = 1
    RESTRICTED_VISIBILITY = 2
    SPECIFIC_USER = 3

class Status(Enum):
    ACTIVE = 1
    INACTIVE = 2
    DELETED = 3

class DeleteStatus(Enum):
    NOT_DELETED = 0
    DELETED = 1
```

## 使用方法

### 1. 准备数据文件

将两个 Discogs artists XML 压缩文件放置在项目目录中：

- `discogs_20250501_artists.xml.gz` - 旧版本数据
- `discogs_20250601_artists.xml.gz` - 新版本数据

### 2. 运行比较工具

```bash
python3 compare_artists.py
```

### 3. 查看结果

工具运行完成后，会在当前目录生成：

- `artists_comparison_changes_only.csv` - 包含所有变化记录的 CSV 文件

## 数据格式

### 输入格式

- **文件类型**: Gzip 压缩的 XML 文件
- **文件命名**: `discogs_YYYYMMDD_artists.xml.gz`
- **XML 结构**: 每行一个完整的 `<artist>` 记录

### 输出格式

生成的 CSV 文件包含以下字段：

| 字段名           | 类型   | 说明                            |
| ---------------- | ------ | ------------------------------- |
| id               | String | 原始 Discogs 艺术家 ID          |
| y_id             | String | 序列化 ID (YA1, YA2, ...)       |
| name             | String | 艺术家名称                      |
| realname         | String | 真实姓名                        |
| profile          | String | 艺术家简介                      |
| variations       | JSON   | 名称变体列表                    |
| aliases          | JSON   | 别名列表 (包含 id 和 name)      |
| groups           | JSON   | 组合/团体列表 (包含 id 和 name) |
| members          | JSON   | 成员列表 (包含 id 和 name)      |
| created_at       | String | 创建时间 (ISO 格式)             |
| operation_status | String | 操作类型 (CREATE/UPDATE/DELETE) |

### 操作状态说明

- **CREATE**: 在新文件中新增的艺术家记录
- **UPDATE**: 在两个文件中都存在但内容有变化的记录
- **DELETE**: 在旧文件中存在但在新文件中不存在的记录

## 性能说明

### 处理能力

- **数据规模**: 支持处理 900万+ 艺术家记录
- **文件大小**: 支持 400MB+ 压缩文件
- **内存使用**: 优化的内存使用，峰值 < 2GB
- **处理时间**: 约 1-2 小时（取决于硬件配置）

### 进度显示

工具会实时显示处理进度：

```
============================================================
Discogs Artists 数据比较工具
============================================================

第一步：加载20250501数据...
正在处理文件: discogs_20250501_artists.xml.gz
已处理 100000 条记录...
已读取 1000000 行，处理了 576038 条记录
...

第二步：加载20250601数据...
...

第三步：比较数据并生成CSV...
...

操作统计:
CREATE: 1234 条
UPDATE: 5678 条
DELETE: 910 条
总计: 7822 条
```

## 注意事项

### 1. 文件要求

- 确保输入文件是有效的 Discogs XML 格式
- 文件名必须遵循 `discogs_YYYYMMDD_artists.xml.gz` 格式
- 确保有足够的磁盘空间存储临时数据和输出文件

### 2. 处理时间

- 大文件处理需要较长时间，请耐心等待
- 程序会定期显示进度信息
- 可以通过 Ctrl+C 中断处理（临时文件会自动清理）

### 3. 内存管理

- 工具使用 SQLite 临时数据库优化内存使用
- 建议在处理大文件时关闭其他占用内存的应用程序
- 临时数据库文件会在处理完成后自动清理

### 4. 数据完整性

- 输出的 CSV 文件只包含有变化的记录
- 相同的记录不会包含在输出中
- 所有时间戳使用 ISO 格式

## 故障排除

### 常见问题

1. **导入错误**: 确保 `enums.py` 文件存在于正确位置
2. **内存不足**: 增加系统内存或关闭其他应用程序
3. **磁盘空间不足**: 清理磁盘空间或更换存储位置
4. **文件格式错误**: 确认输入文件是有效的 Discogs XML 格式

### 日志信息

工具会输出详细的处理日志，包括：
- 文件读取进度
- 记录处理数量
- 错误信息和异常
- 最终统计结果

## 技术实现

### 核心算法

1. **XML 解析**: 逐行解析压缩的 XML 文件
2. **数据提取**: 使用正则表达式提取艺术家字段
3. **哈希比较**: 基于业务字段计算 MD5 哈希值进行比较
4. **SQLite 存储**: 使用临时数据库进行高效数据比较

### 数据流程

```
输入文件 → XML解析 → 字段提取 → 哈希计算 → SQLite存储 → 数据比较 → CSV输出
```

## 更新日志

### v1.0.0 (2025-06-27)

- ✨ 初始版本发布
- 🔧 支持完整的 Discogs artists XML 文件处理
- 📊 实现高效的数据比较算法
- 📝 生成详细的变化记录 CSV 报告
- 🚀 优化大文件处理性能

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 Issue
- 发送邮件至：[<EMAIL>]
