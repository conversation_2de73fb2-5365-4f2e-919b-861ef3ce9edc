#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import csv
import re
import hashlib
import json
import time
from datetime import datetime
import os
import gzip


# 配置参数
FILE_OLD = 'discogs_20250501_artists.xml.gz'
FILE_NEW = 'discogs_20250601_artists.xml.gz'
OUTPUT_CSV = 'artists_comparison_diff.csv'
TEMP_DB = 'temp_artists_comparison.db'
# 处理全部数据，无限制

# CSV字段定义（只包含业务字段）
CSV_FIELDS = [
    'id', 'y_id', 'name', 'realname', 'profile', 'variations', 'aliases',
    'groups', 'members', 'created_at', 'operation_status'
]

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None

def extract_namevariations(content):
    """提取namevariations字段"""
    namevariations = []
    namevariations_match = re.search(r'<namevariations>(.*?)</namevariations>', content, re.DOTALL)
    if not namevariations_match:
        return namevariations

    namevariations_content = namevariations_match.group(1)
    extracted_variations = re.findall(r'<name>(.*?)</name>', namevariations_content, re.DOTALL)
    # 清理名称变体，去除前后空白字符
    return [variation.strip() for variation in extracted_variations if variation.strip()]


def extract_aliases(content):
    """提取aliases字段"""
    aliases = []
    aliases_match = re.search(r'<aliases>(.*?)</aliases>', content, re.DOTALL)
    if not aliases_match:
        return aliases

    aliases_content = aliases_match.group(1)

    # 使用正则表达式提取aliases
    alias_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    alias_matches = re.findall(alias_pattern, aliases_content)

    if alias_matches:
        for alias_id, alias_text in alias_matches:
            clean_text = alias_text.strip()
            if clean_text:  # 只添加非空的别名
                aliases.append({
                    'id': alias_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        alias_tags = aliases_content.split('</name>')
        for tag in alias_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        alias_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            alias_text = tag[text_start:].strip()
                            aliases.append({
                                'id': alias_id,
                                'name': alias_text
                            })

    return aliases


def extract_groups(content):
    """提取groups字段"""
    groups = []
    groups_match = re.search(r'<groups>(.*?)</groups>', content, re.DOTALL)
    if not groups_match:
        return groups

    groups_content = groups_match.group(1)

    # 使用正则表达式提取groups
    group_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    group_matches = re.findall(group_pattern, groups_content)

    if group_matches:
        for group_id, group_text in group_matches:
            clean_text = group_text.strip()
            if clean_text:  # 只添加非空的组合名称
                groups.append({
                    'id': group_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        group_tags = groups_content.split('</name>')
        for tag in group_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        group_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            group_text = tag[text_start:].strip()
                            groups.append({
                                'id': group_id,
                                'name': group_text
                            })

    return groups

def extract_members(content):
    """提取members字段"""
    members = []
    members_match = re.search(r'<members>(.*?)</members>', content, re.DOTALL)
    if not members_match:
        return members

    members_content = members_match.group(1)

    # 使用正则表达式提取members
    member_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    member_matches = re.findall(member_pattern, members_content)

    if member_matches:
        for member_id, member_text in member_matches:
            clean_text = member_text.strip()
            if clean_text:  # 只添加非空的成员名称
                members.append({
                    'id': member_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        member_tags = members_content.split('</name>')
        for tag in member_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        member_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            member_text = tag[text_start:].strip()
                            members.append({
                                'id': member_id,
                                'name': member_text
                            })

    return members

def process_artist_content(buffer, sequential_id):
    """处理单个artist标签的内容"""
    # 提取ID
    artist_id = extract_field(buffer, 'id')
    if not artist_id:
        return None

    # 创建artist文档
    artist_doc = {
        'id': artist_id,
        'y_id': f"YA{sequential_id}",
        'name': extract_field(buffer, 'name'),
        'realname': extract_field(buffer, 'realname'),
        'profile': extract_field(buffer, 'profile'),
        'variations': json.dumps(extract_namevariations(buffer), ensure_ascii=False),
        'aliases': json.dumps(extract_aliases(buffer), ensure_ascii=False),
        'groups': json.dumps(extract_groups(buffer), ensure_ascii=False),
        'members': json.dumps(extract_members(buffer), ensure_ascii=False),
        'created_at': datetime.now().isoformat(),
    }

    return artist_doc

def create_hash(artist_data):
    """为artist数据创建哈希值，用于比较"""
    # 排除时间戳和y_id字段，只对业务数据计算哈希
    hash_data = {
        'id': artist_data.get('id', ''),
        'name': artist_data.get('name', ''),
        'realname': artist_data.get('realname', ''),
        'profile': artist_data.get('profile', ''),
        'variations': artist_data.get('variations', ''),
        'aliases': artist_data.get('aliases', ''),
        'groups': artist_data.get('groups', ''),
        'members': artist_data.get('members', ''),
    }

    hash_string = json.dumps(hash_data, sort_keys=True, ensure_ascii=False)
    return hashlib.md5(hash_string.encode('utf-8')).hexdigest()

def load_artists_to_db(filename, table_name, conn):
    """从gz文件加载artists数据到SQLite数据库"""
    print(f"正在处理文件: {filename}")

    cursor = conn.cursor()

    # 创建表
    cursor.execute(f'''
        CREATE TABLE IF NOT EXISTS {table_name} (
            id TEXT PRIMARY KEY,
            y_id TEXT,
            name TEXT,
            realname TEXT,
            profile TEXT,
            variations TEXT,
            aliases TEXT,
            groups TEXT,
            members TEXT,
            created_at TEXT,
            hash TEXT
        )
    ''')

    processed_count = 0
    sequential_id = 0

    try:
        with gzip.open(filename, 'rt', encoding='utf-8') as f:
            total_lines = 0

            for line in f:
                total_lines += 1
                if total_lines % 1000000 == 0:
                    print(f"已读取 {total_lines} 行，处理了 {processed_count} 条记录")

                # 每行都是一个完整的artist记录
                if '<artist>' in line and '</artist>' in line:
                    # 处理artist内容
                    sequential_id += 1
                    artist_doc = process_artist_content(line, sequential_id)
                    if artist_doc:
                        # 计算哈希值
                        hash_value = create_hash(artist_doc)

                        # 插入数据库
                        cursor.execute(f'''
                            INSERT OR REPLACE INTO {table_name}
                            (id, y_id, name, realname, profile, variations, aliases, groups, members, created_at, hash)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)  
                        ''', (
                            artist_doc['id'], artist_doc['y_id'], artist_doc['name'],
                            artist_doc['realname'], artist_doc['profile'], artist_doc['variations'],
                            artist_doc['aliases'], artist_doc['groups'], artist_doc['members'], artist_doc['created_at'], 
                            hash_value
                        ))

                        processed_count += 1

                        # 显示进度
                        if processed_count % 50000 == 0:
                            print(f"已处理 {processed_count} 条记录...")
                            conn.commit()  # 定期提交





    except Exception as e:
        print(f"处理文件 {filename} 时出错: {e}")
        return 0

    conn.commit()
    print(f"文件 {filename} 处理完成，共处理 {processed_count} 条记录")
    return processed_count

def compare_and_generate_csv(conn):
    """比较两个数据集并生成CSV文件"""
    print("开始比较数据...")

    cursor = conn.cursor()

    # 获取所有唯一的ID
    cursor.execute('''
        SELECT DISTINCT id FROM (
            SELECT id FROM artists_20250501
            UNION
            SELECT id FROM artists_20250601
        )
    ''')

    all_ids = [row[0] for row in cursor.fetchall()]
    print(f"共找到 {len(all_ids)} 个唯一ID")

    changes = []
    processed = 0

    for artist_id in all_ids:
        # 获取两个版本的数据
        cursor.execute('SELECT * FROM artists_20250501 WHERE id = ?', (artist_id,))
        old_data = cursor.fetchone()

        cursor.execute('SELECT * FROM artists_20250601 WHERE id = ?', (artist_id,))
        new_data = cursor.fetchone()

        if old_data and new_data:
            # 两个文件都存在，检查是否有变化
            old_hash = old_data[-1]  # hash字段在最后
            new_hash = new_data[-1]

            if old_hash != new_hash:
                # 数据有变化，记录UPDATE
                record = list(new_data[:-1])  # 排除hash字段
                record.append('UPDATE')  # 添加operation_status
                changes.append(record)
        elif new_data:
            # 只在新文件中存在，记录CREATE
            record = list(new_data[:-1])  # 排除hash字段
            record.append('CREATE')  # 添加operation_status
            changes.append(record)
        elif old_data:
            # 只在旧文件中存在，记录DELETE
            record = list(old_data[:-1])  # 排除hash字段
            record.append('DELETE')  # 添加operation_status
            changes.append(record)

        processed += 1
        if processed % 50000 == 0:
            print(f"已比较 {processed}/{len(all_ids)} 条记录...")

    print(f"比较完成，共发现 {len(changes)} 条变化记录")

    # 写入CSV文件
    print(f"正在写入CSV文件: {OUTPUT_CSV}")
    with open(OUTPUT_CSV, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)

        # 写入表头
        writer.writerow(CSV_FIELDS)

        # 写入数据
        for record in changes:
            writer.writerow(record)

    print(f"CSV文件生成完成: {OUTPUT_CSV}")
    print(f"共写入 {len(changes)} 条变化记录")

    # 统计各种操作类型的数量
    create_count = sum(1 for record in changes if record[-1] == 'CREATE')
    update_count = sum(1 for record in changes if record[-1] == 'UPDATE')
    delete_count = sum(1 for record in changes if record[-1] == 'DELETE')

    print(f"\n操作统计:")
    print(f"CREATE: {create_count} 条")
    print(f"UPDATE: {update_count} 条")
    print(f"DELETE: {delete_count} 条")
    print(f"总计: {len(changes)} 条")

def main():
    """主函数"""
    start_time = time.time()

    print("="*60)
    print("Discogs Artists 数据比较工具 (完整数据集)")
    print("="*60)

    # 检查输入文件是否存在
    if not os.path.exists(FILE_OLD):
        print(f"错误：文件 {FILE_OLD} 不存在")
        return

    if not os.path.exists(FILE_NEW):
        print(f"错误：文件 {FILE_NEW} 不存在")
        return

    # 删除旧的临时数据库和输出文件
    if os.path.exists(TEMP_DB):
        os.remove(TEMP_DB)
    if os.path.exists(OUTPUT_CSV):
        os.remove(OUTPUT_CSV)

    try:
        # 创建SQLite连接
        conn = sqlite3.connect(TEMP_DB)

        # 加载两个文件的数据
        print("\n第一步：加载20250501数据...")
        count1 = load_artists_to_db(FILE_OLD, 'artists_20250501', conn)

        print("\n第二步：加载20250601数据...")
        count2 = load_artists_to_db(FILE_NEW, 'artists_20250601', conn)

        print(f"\n数据加载完成:")
        print(f"20250501: {count1} 条记录")
        print(f"20250601: {count2} 条记录")

        # 比较数据并生成CSV
        print("\n第三步：比较数据并生成CSV...")
        compare_and_generate_csv(conn)

        # 关闭数据库连接
        conn.close()

        # 清理临时数据库
        if os.path.exists(TEMP_DB):
            os.remove(TEMP_DB)
            print(f"临时数据库 {TEMP_DB} 已清理")

    except Exception as e:
        print(f"处理过程中出错: {e}")
        # 确保清理临时文件
        if os.path.exists(TEMP_DB):
            os.remove(TEMP_DB)

    # 计算总处理时间
    total_time = time.time() - start_time
    print(f"\n总处理时间: {total_time:.2f} 秒")
    print("="*60)

if __name__ == "__main__":
    main()