#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云OSS上传功能测试脚本
严格按照阿里云官方文档实现

使用方法:
1. 确保已安装依赖: pip install oss2
2. 运行脚本: python test_oss_upload.py
3. 查看测试结果

参考文档: https://help.aliyun.com/zh/oss/developer-reference/simple-upload-using-oss-sdk-for-python-v2
作者: AI Assistant
创建时间: 2025-01-14
兼容环境: Windows/Linux/macOS
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, Tuple

# 导入oss2模块
import oss2
# 导入OSS异常类 - 如果IDE报错可忽略，运行时正常
try:
    from oss2.exceptions import OssError
except ImportError:
    # 备用导入方式（理论上不会执行到这里）
    OssError = oss2.exceptions.OssError

# OSS配置参数 - 您的真实OSS服务配置
OSS_CONFIG = {
    "access_key_id": "11",
    "access_key_secret": "12345678",
    "endpoint": "https://music.yrwantlist.com",  # 使用完整的HTTPS URL
    "bucket_name": "yrwishlist",
    "region": "music",
    "upload_path_prefix": "discogs_comparison/",
    "enable_upload": True,
    "connection_timeout": 30,
    "enable_crc": False,  # 禁用CRC校验
    "exp_after": 300000
}


class StandardOSSClient:
    """标准OSS客户端，严格按照阿里云官方文档实现"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化OSS客户端，按照官方文档标准

        Args:
            config: OSS配置字典
        """
        self.config = config
        self.auth = None
        self.bucket = None
        self.connected = False

        # 验证配置
        self._validate_config()

        # 初始化客户端
        self._init_client()

    def _validate_config(self):
        """验证OSS配置"""
        required_fields = ['endpoint', 'access_key_id', 'access_key_secret', 'bucket_name']
        missing_fields = []

        for field in required_fields:
            if not self.config.get(field):
                missing_fields.append(field)

        if missing_fields:
            raise ValueError(f"OSS配置缺少必需字段: {', '.join(missing_fields)}")

    def _init_client(self):
        """初始化OSS客户端，按照官方文档标准"""
        try:
            print("🔄 正在初始化标准OSS客户端...")
            print(f"   Access Key ID: {self.config['access_key_id']}")
            print(f"   Endpoint: {self.config['endpoint']}")
            print(f"   Bucket: {self.config['bucket_name']}")

            # 创建认证对象 - 按照官方文档
            self.auth = oss2.Auth(
                self.config['access_key_id'],
                self.config['access_key_secret']
            )

            # 创建Bucket对象 - 按照官方文档
            self.bucket = oss2.Bucket(
                self.auth,
                self.config['endpoint'],
                self.config['bucket_name'],
                connect_timeout=self.config.get('connection_timeout', 30)
            )

            print("✅ 标准OSS客户端初始化成功")

        except Exception as e:
            error_msg = f"OSS客户端初始化失败: {e}"
            print(f"❌ {error_msg}")
            raise RuntimeError(error_msg)
    
    def test_connection(self) -> Tuple[bool, Optional[str]]:
        """测试OSS连接，使用正确的oss2 API方法"""
        print("🔄 正在测试标准OSS连接...")
        print(f"   Endpoint: {self.config['endpoint']}")
        print(f"   Bucket: {self.config['bucket_name']}")

        try:
            # 使用get_bucket_info()方法测试连接
            print("   正在获取bucket信息...")
            bucket_info = self.bucket.get_bucket_info()

            print("✅ OSS连接测试成功")
            print(f"   Bucket名称: {bucket_info.name}")
            print(f"   创建时间: {bucket_info.creation_date}")
            print(f"   存储类型: {bucket_info.storage_class}")
            self.connected = True
            return True, None

        except OssError as e:
            error_msg = f"OSS错误: {e}"
            print(f"❌ {error_msg}")
            print(f"   错误代码: {getattr(e, 'code', 'Unknown')}")
            print(f"   HTTP状态: {getattr(e, 'status', 'Unknown')}")
            print(f"   请求ID: {getattr(e, 'request_id', 'Unknown')}")
            return False, error_msg

        except Exception as e:
            error_msg = f"连接测试失败: {e}"
            print(f"❌ {error_msg}")
            print("   建议检查:")
            print(f"     1. Endpoint格式: {self.config['endpoint']}")
            print("     2. 网络连接")
            print("     3. 访问密钥")
            print("     4. Bucket名称")
            return False, error_msg
    
    def upload_file(self, local_file_path: str, oss_key: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        上传文件到OSS
        
        Args:
            local_file_path: 本地文件路径
            oss_key: OSS对象键名，如果为None则自动生成
            
        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息或OSS键名)
        """
        if not self.connected:
            return False, "OSS未连接，请先测试连接"
        
        local_path = Path(local_file_path)
        
        # 检查本地文件是否存在
        if not local_path.exists():
            error_msg = f"本地文件不存在: {local_file_path}"
            print(f"❌ {error_msg}")
            return False, error_msg
        
        # 生成OSS键名
        if oss_key is None:
            oss_key = self._generate_oss_key(local_path)
        
        try:
            print(f"🔄 正在上传文件: {local_path.name}")
            print(f"   本地路径: {local_file_path}")
            print(f"   OSS键名: {oss_key}")
            
            start_time = time.time()
            
            # 获取文件大小
            file_size = local_path.stat().st_size
            print(f"   文件大小: {self._format_file_size(file_size)}")
            
            # 上传文件
            result = self.bucket.put_object_from_file(oss_key, str(local_path))
            
            duration = time.time() - start_time
            
            print(f"✅ 文件上传成功!")
            print(f"   耗时: {duration:.2f}秒")
            print(f"   ETag: {result.etag}")
            print(f"   OSS键名: {oss_key}")
            
            return True, oss_key
            
        except OssError as e:
            error_msg = f"OSS上传失败: {e}"
            print(f"❌ {error_msg}")
            return False, error_msg
        
        except Exception as e:
            error_msg = f"上传过程中发生未知错误: {e}"
            print(f"❌ {error_msg}")
            return False, error_msg
    
    def _generate_oss_key(self, local_path: Path) -> str:
        """生成OSS对象键名"""
        # 获取配置的路径前缀
        prefix = self.config.get('upload_path_prefix', 'discogs_comparison/')
        
        # 确保前缀以/结尾
        if prefix and not prefix.endswith('/'):
            prefix += '/'
        
        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"test_{timestamp}_{local_path.name}"
        
        return f"{prefix}{filename}"
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小显示"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def object_exists(self, oss_key: str) -> bool:
        """检查OSS对象是否存在"""
        try:
            self.bucket.head_object(oss_key)
            return True
        except OssError as e:
            if e.status == 404:
                return False
            raise e
    
    def get_object_info(self, oss_key: str) -> Optional[Dict[str, Any]]:
        """获取OSS对象信息"""
        try:
            result = self.bucket.head_object(oss_key)
            return {
                'key': oss_key,
                'size': result.content_length,
                'last_modified': result.last_modified,
                'etag': result.etag,
                'content_type': result.content_type
            }
        except OssError as e:
            print(f"❌ 获取OSS对象信息失败: {e}")
            return None


def create_test_file(filename: str = "test_upload.txt") -> str:
    """
    创建测试文件
    
    Args:
        filename: 测试文件名
        
    Returns:
        str: 创建的测试文件路径
    """
    print("🔄 正在创建测试文件...")
    
    # 确保在当前目录创建文件
    file_path = Path(filename)
    
    # 创建测试内容
    test_content = f"""OSS上传测试文件
===================

创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试目的: 验证阿里云OSS上传功能
文件编码: UTF-8

配置信息:
- Endpoint: {OSS_CONFIG['endpoint']}
- Bucket: {OSS_CONFIG['bucket_name']}
- 上传路径前缀: {OSS_CONFIG['upload_path_prefix']}

测试内容:
这是一个用于测试OSS上传功能的示例文件。
文件包含中文字符以测试编码兼容性。
包含特殊字符: !@#$%^&*()_+-=[]{{}}|;:'"<>?,.

数字序列: 0123456789
字母序列: abcdefghijklmnopqrstuvwxyz
大写字母: ABCDEFGHIJKLMNOPQRSTUVWXYZ

测试完成标记: ✅ SUCCESS
"""
    
    try:
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        file_size = file_path.stat().st_size
        print(f"✅ 测试文件创建成功:")
        print(f"   文件路径: {file_path.absolute()}")
        print(f"   文件大小: {file_size} 字节")
        
        return str(file_path)
        
    except Exception as e:
        error_msg = f"创建测试文件失败: {e}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)


def print_config_info():
    """打印配置信息"""
    print("📋 当前OSS配置:")
    print(f"   Access Key ID: {OSS_CONFIG['access_key_id']}")
    print(f"   Access Key Secret: {'*' * len(OSS_CONFIG['access_key_secret'])}")
    print(f"   Endpoint: {OSS_CONFIG['endpoint']}")
    print(f"   Bucket Name: {OSS_CONFIG['bucket_name']}")
    print(f"   Upload Path Prefix: {OSS_CONFIG['upload_path_prefix']}")
    print(f"   Enable Upload: {OSS_CONFIG['enable_upload']}")
    print()


def main():
    """主函数"""
    print("=" * 60)
    print("🚀 阿里云OSS上传功能测试")
    print("=" * 60)
    print()

    # 打印配置信息
    print_config_info()

    # 检查是否启用上传
    if not OSS_CONFIG.get('enable_upload', False):
        print("⚠️  警告: enable_upload设置为False，跳过上传测试")
        return False

    try:
        # 1. 创建测试文件
        test_file_path = create_test_file()
        print()

        # 2. 初始化标准OSS客户端
        oss_client = StandardOSSClient(OSS_CONFIG)
        print()

        # 3. 测试连接
        connection_success, connection_error = oss_client.test_connection()
        print()

        if not connection_success:
            print(f"❌ 连接测试失败，终止上传测试")
            print(f"   错误信息: {connection_error}")
            return False

        # 4. 上传测试文件
        upload_success, upload_result = oss_client.upload_file(test_file_path)
        print()

        if upload_success:
            print("🎉 OSS上传测试完全成功!")
            print(f"   上传的文件键名: {upload_result}")

            # 5. 验证上传的文件
            if oss_client.object_exists(upload_result):
                print("✅ 文件存在性验证成功")

                # 获取文件信息
                file_info = oss_client.get_object_info(upload_result)
                if file_info:
                    print("📊 上传文件信息:")
                    print(f"   大小: {file_info['size']} 字节")
                    print(f"   最后修改: {file_info['last_modified']}")
                    print(f"   ETag: {file_info['etag']}")
                    print(f"   内容类型: {file_info['content_type']}")
            else:
                print("⚠️  警告: 文件上传成功但验证时未找到")
        else:
            print("❌ OSS上传测试失败")
            print(f"   错误信息: {upload_result}")
            return False

        # 6. 清理测试文件
        try:
            os.remove(test_file_path)
            print(f"🧹 本地测试文件已清理: {test_file_path}")
        except Exception as e:
            print(f"⚠️  清理本地测试文件失败: {e}")

        print()
        print("=" * 60)
        print("✅ 所有测试完成!")
        print("=" * 60)
        return True

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        print()
        print("=" * 60)
        print("❌ 测试失败!")
        print("=" * 60)
        return False


if __name__ == "__main__":
    # Windows环境兼容性设置
    if sys.platform.startswith('win'):
        # 设置控制台编码为UTF-8
        try:
            import locale
            if locale.getpreferredencoding().lower() != 'utf-8':
                print("提示: 建议在Windows命令行中设置UTF-8编码")
                print("运行命令: chcp 65001")
                print()
        except:
            pass

    # 运行测试
    success = main()

    # 退出码
    sys.exit(0 if success else 1)
