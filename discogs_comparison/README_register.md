# Discogs 自动注册工具

这是一个用于在Discogs网站上自动注册新账号的Python脚本，支持多账号批量注册，包含完善的错误处理和日志记录功能。

## 功能特性

- ✅ **多账号批量注册** - 支持配置多个账号信息，一次性批量注册
- ✅ **智能表单填写** - 自动识别并填写用户名、邮箱、密码等字段
- ✅ **验证码处理** - 自动检测验证码并支持人工干预处理
- ✅ **重试机制** - 注册失败时自动重试，提高成功率
- ✅ **详细日志** - 完整记录注册过程，便于问题排查
- ✅ **Windows优化** - 针对Windows环境进行了特别优化
- ✅ **配置灵活** - 支持YAML配置文件，易于管理和修改
- ✅ **浏览器支持** - 支持Chrome、Firefox、Edge浏览器

## 系统要求

- **操作系统**: Windows 10/11 (推荐)
- **Python版本**: Python 3.8 或更高版本
- **浏览器**: Chrome、Firefox 或 Edge (推荐Chrome)
- **网络**: 稳定的互联网连接

## 安装说明

### 1. 安装Python依赖

```bash
# 安装所有依赖包
pip install -r requirements.txt

# 或者手动安装主要依赖
pip install selenium webdriver-manager PyYAML
```

### 2. 浏览器驱动

脚本使用 `webdriver-manager` 自动管理浏览器驱动，首次运行时会自动下载对应的驱动程序。

## 配置说明

### 1. 编辑配置文件

编辑 `config/register_config.yaml` 文件，配置您的账号信息：

```yaml
# 账号信息配置（支持多个账号）
accounts:
  - username: "ywl2025005"
    email: "<EMAIL>"
    password: "Abcd@1234qaz"
    enabled: true
  - username: "ywl2025006"
    email: "<EMAIL>"
    password: "Password@123"
    enabled: true
```

### 2. 主要配置项说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `accounts` | 账号列表，支持多个账号 | - |
| `browser.type` | 浏览器类型 (chrome/firefox/edge) | chrome |
| `browser.show_window` | 是否显示浏览器窗口 | true |
| `automation.min_delay` | 操作间最小延迟(秒) | 1 |
| `automation.max_delay` | 操作间最大延迟(秒) | 3 |
| `registration.retry_attempts` | 重试次数 | 3 |

## 使用方法

### 1. 基本使用

```bash
# 批量注册所有启用的账号
python discogs_auto_register.py

# 使用自定义配置文件
python discogs_auto_register.py --config my_config.yaml
```

### 2. 高级使用

```bash
# 仅注册指定用户名的账号
python discogs_auto_register.py --account ywl2025005

# 试运行模式（仅验证配置，不执行注册）
python discogs_auto_register.py --dry-run

# 查看帮助信息
python discogs_auto_register.py --help
```

### 3. Windows批处理脚本

为了方便Windows用户使用，可以创建批处理文件 `register.bat`：

```batch
@echo off
echo 🚀 启动Discogs自动注册工具...
python discogs_auto_register.py
pause
```

## 验证码处理

当检测到验证码时，脚本会：

1. **自动暂停** - 停止自动操作，等待人工处理
2. **提示用户** - 在控制台显示提示信息
3. **等待确认** - 用户完成验证码后按回车键继续
4. **超时处理** - 如果超时未确认，自动跳过当前账号

### 验证码处理步骤：

1. 脚本检测到验证码后会暂停
2. 在浏览器中手动完成验证码
3. 回到控制台按回车键继续
4. 脚本继续执行后续步骤

## 日志说明

### 日志文件位置

- **主日志**: `logs/discogs_register.log`
- **错误日志**: `logs/discogs_register_error.log`

### 日志级别

- **INFO**: 正常操作信息
- **WARNING**: 警告信息（如验证码检测）
- **ERROR**: 错误信息（如注册失败）

### 日志内容示例

```
2024-01-15 10:30:15 - discogs_register - INFO - 开始注册账号: ywl2025005
2024-01-15 10:30:20 - discogs_register - INFO - 用户名填写完成
2024-01-15 10:30:25 - discogs_register - WARNING - 检测到验证码
2024-01-15 10:31:00 - discogs_register - INFO - 账号 ywl2025005 注册成功！
```

## 故障排除

### 常见问题

1. **浏览器驱动问题**
   ```
   解决方案: 删除 ~/.wdm 目录，重新运行脚本自动下载驱动
   ```

2. **网络连接超时**
   ```
   解决方案: 检查网络连接，增加配置文件中的超时时间
   ```

3. **验证码无法处理**
   ```
   解决方案: 确保浏览器窗口可见，手动完成验证码后按回车
   ```

4. **表单字段无法找到**
   ```
   解决方案: 检查配置文件中的CSS选择器是否正确
   ```

### 调试模式

启用详细日志记录：

```yaml
logging:
  level: "DEBUG"
  detailed_browser_logs: true
```

## 注意事项

⚠️ **重要提醒**:

1. **合规使用** - 请确保遵守Discogs的服务条款
2. **频率控制** - 避免过于频繁的注册请求
3. **账号安全** - 妥善保管配置文件中的密码信息
4. **网络环境** - 建议在稳定的网络环境下运行
5. **人工监督** - 建议在有人监督的情况下运行脚本

## 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查配置文件格式是否正确
3. 确认网络连接和浏览器环境
4. 尝试使用试运行模式验证配置

## 更新日志

- **v1.0.0** (2024-01-15)
  - 初始版本发布
  - 支持多账号批量注册
  - 集成验证码处理
  - 完善的日志系统
