# Discogs Labels 比较工具

这个工具用于比较两个 Discogs 标签 XML 文件，识别创建、更新和删除的记录，并生成包含所有变化的 CSV 文件。

## 功能特性

- **高效处理大文件**: 使用流式处理和 SQLite 临时数据库，可以处理数百万条记录
- **完整字段提取**: 提取与 process_labels.py 相同的所有字段
- **操作状态识别**: 自动识别 CREATE、UPDATE、DELETE 操作
- **进度监控**: 实时显示处理进度
- **内存优化**: 避免将整个文件加载到内存中

## 文件说明

- `compare_labels.py`: 主比较脚本
- `test_compare.py`: 测试数据生成脚本
- `README.md`: 使用说明文档

## 使用方法

### 1. 处理真实文件

确保以下文件存在于当前目录：

- `discogs_20250501_labels.xml` (基准文件)
- `discogs_20250601_labels.xml` (比较文件)

运行比较：

```bash
python3 compare_labels.py
```

### 2. 测试功能

首先生成测试文件：

```bash
python test_compare.py
```

然后修改`compare_labels.py`中的配置，取消注释测试文件配置：

```python
# 如果要测试小样本，请取消注释以下行：
FILE_1 = 'test_file1.xml'
FILE_2 = 'test_file2.xml'
OUTPUT_CSV = 'test_comparison_result.csv'
```

运行测试：

```bash
python compare_labels.py
```

## 输出文件

### CSV 字段说明

生成的 CSV 文件包含以下字段：

| 字段名           | 说明                            |
| ---------------- | ------------------------------- |
| id               | 原始 Discogs ID                 |
| y_id             | 序列化 ID (YL1, YL2, ...)       |
| name             | 标签名称                        |
| profile          | 标签简介                        |
| contactinfo      | 联系信息                        |
| sublabels        | 子标签列表 (JSON 格式)          |
| parent_label     | 父标签信息 (JSON 格式)          |
| delete_status    | 删除状态 (0=未删除, 1=已删除)   |
| deleted_at       | 删除时间                        |
| created_at       | 创建时间                        |
| updated_at       | 更新时间                        |
| source           | 数据源 (1=Discogs)              |
| permissions      | 权限设置                        |
| status           | 状态                            |
| operation_status | 操作类型 (CREATE/UPDATE/DELETE) |

### 操作状态说明

- **CREATE**: 在第二个文件中新增的记录
- **UPDATE**: 在两个文件中都存在但内容有变化的记录
- **DELETE**: 在第一个文件中存在但在第二个文件中不存在的记录
- **NO_CHANGE**: 内容完全相同的记录（不会包含在输出 CSV 中）

## 性能说明

- 处理 220 万条记录大约需要 10-15 分钟
- 内存使用量保持在合理范围内（< 1GB）
- 使用 SQLite 临时数据库进行高效比较
- 每 50,000 条记录显示一次进度

## 注意事项

1. 确保有足够的磁盘空间存储临时数据库和输出 CSV 文件
2. 处理大文件时请耐心等待，程序会显示进度信息
3. 如果程序意外中断，临时数据库文件会被自动清理
4. 输出的 CSV 文件只包含有变化的记录，相同的记录不会被包含

## 依赖要求

- Python 3.6+
- 标准库: sqlite3, csv, re, hashlib, json, time, datetime, os, sys
- 自定义模块: enums.py (来自../label/目录)

## 故障排除

如果遇到导入错误，请确保：

1. enums.py 文件存在于../label/目录中
2. Python 路径设置正确
3. 所有必需的 XML 文件存在于当前目录中
