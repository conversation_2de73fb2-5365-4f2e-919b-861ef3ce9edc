#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import csv
import re
import hashlib
import json
import time
from datetime import datetime
import os
import gzip
from enum import Enum


class Source(Enum):
    """数据源枚举"""
    DISCOGS = "discogs"


# 配置参数 - 设置为处理真实文件
FILE_1 = 'discogs_20250501_labels.xml.gz'  # 第一个文件（基准）
FILE_2 = 'discogs_20250601_labels.xml.gz'  # 第二个文件（比较）
OUTPUT_CSV = 'label_comparison_diff.csv'
TEMP_DB = 'temp_comparison.db'

# 处理选项
OUTPUT_ALL_RECORDS = False  # True: 输出所有记录, False: 仅输出变化记录
BATCH_SIZE = 10000  # 批量处理大小
PROGRESS_INTERVAL = 50000  # 进度报告间隔
DEBUG_MODE = True  # 调试模式，输出前几条记录的详细比较信息
DEBUG_RECORD_COUNT = 5  # 调试模式下输出的记录数量

# 如果要测试小样本，请取消注释以下行：
# FILE_1 = 'test_file1.xml'
# FILE_2 = 'test_file2.xml'
# OUTPUT_CSV = 'test_comparison_result.csv'
# OUTPUT_ALL_RECORDS = False

def write_log(message, print_to_console=True):
    """写入日志信息"""
    if print_to_console:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

def estimate_total_records(filename):
    """估算XML文件中的记录总数"""
    write_log(f"正在估算文件 {filename} 的记录总数...")
    try:
        # 对于gz文件，使用zcat和grep快速计算记录数
        import subprocess
        result = subprocess.run(['zcat', filename, '|', 'grep', '-c', '<label>'],
                              shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            count = int(result.stdout.strip())
            write_log(f"估算记录总数: {count:,}")
            return count
    except Exception as e:
        write_log(f"估算记录数失败: {e}")

    # 备用方法：快速扫描压缩文件
    count = 0
    try:
        with gzip.open(filename, 'rt', encoding='utf-8') as f:
            for line in f:
                if '<label>' in line:
                    count += 1
        write_log(f"扫描得到记录总数: {count:,}")
        return count
    except Exception as e:
        write_log(f"扫描文件失败: {e}")
        return 0

def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        return f"{seconds/60:.1f}分钟"
    else:
        return f"{seconds/3600:.1f}小时"

def show_progress(current, total, start_time, operation="处理"):
    """显示进度信息"""
    if total > 0:
        percentage = (current / total) * 100
        elapsed = time.time() - start_time
        if current > 0:
            avg_time = elapsed / current
            remaining = (total - current) * avg_time
            eta = format_time(remaining)
            speed = current / elapsed if elapsed > 0 else 0
            write_log(f"{operation}: {current:,}/{total:,} ({percentage:.1f}%) "
                     f"速度: {speed:.1f}条/秒 预计剩余: {eta}")
        else:
            write_log(f"{operation}: {current:,}/{total:,} ({percentage:.1f}%)")
    else:
        write_log(f"{operation}: {current:,} 条记录")

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    if match:
        # 清理文本，移除换行符和多余空格，转义CSV特殊字符
        text = match.group(1).strip()
        # 替换换行符为空格
        text = re.sub(r'\s+', ' ', text)
        # 转义双引号
        text = text.replace('"', '""')
        return text
    return None

def extract_sublabels(content):
    """提取sublabels字段"""
    sublabels = []
    sublabels_match = re.search(r'<sublabels>(.*?)</sublabels>', content, re.DOTALL)
    if not sublabels_match:
        return sublabels

    sublabels_content = sublabels_match.group(1)
    sublabel_pattern = r'<label id="?(\d+)"?>(.*?)</label>'
    sublabel_matches = re.findall(sublabel_pattern, sublabels_content)

    for sublabel_id, sublabel_text in sublabel_matches:
        sublabels.append({
            'id': sublabel_id,
            'text': sublabel_text.strip()
        })

    return sublabels

def extract_parent_label(content):
    """提取parent label信息，包括id和name"""
    try:
        parent_label_pattern = r'<parentLabel id="?(\d+)"?>(.*?)</parentLabel>'
        match = re.search(parent_label_pattern, content, re.DOTALL)

        if match:
            parent_id = match.group(1)
            parent_name = match.group(2).strip()
            return {
                'id': parent_id,
                'name': parent_name
            }
        return None
    except Exception as e:
        write_log(f"解析parent_label时出错: {e}")
        return None

def extract_label_fields(xml_content):
    """提取标签的所有字段，返回字典格式"""
    label_id = extract_field(xml_content, 'id')
    if not label_id:
        return None
    
    parent_label_info = extract_parent_label(xml_content)
    sublabels = extract_sublabels(xml_content)
    
    label_data = {
        'id': label_id,
        'name': extract_field(xml_content, 'name'),  # XML中使用<n>标签表示名称
        'profile': extract_field(xml_content, 'profile'),
        'contactinfo': extract_field(xml_content, 'contactinfo'),
        'sublabels': sublabels,
        'parent_label': parent_label_info,
    }
    
    return label_data

def calculate_content_hash(label_data):
    """计算标签内容的哈希值，用于快速比较，只包含业务字段"""
    # 只包含业务字段，排除ID和其他元数据
    business_data = {
        'name': label_data.get('name', ''),
        'profile': label_data.get('profile', ''),
        'contactinfo': label_data.get('contactinfo', ''),
        'sublabels': label_data.get('sublabels', []),
        'parent_label': label_data.get('parent_label', None),
    }

    # 创建一个包含所有重要字段的字符串
    content_str = json.dumps(business_data, sort_keys=True, ensure_ascii=False)
    return hashlib.md5(content_str.encode('utf-8')).hexdigest()

def setup_database():
    """设置临时数据库，优化性能"""
    # 删除已存在的临时数据库
    if os.path.exists(TEMP_DB):
        os.remove(TEMP_DB)

    conn = sqlite3.connect(TEMP_DB)
    cursor = conn.cursor()

    # 优化SQLite性能设置
    cursor.execute('PRAGMA journal_mode = WAL')
    cursor.execute('PRAGMA synchronous = NORMAL')
    cursor.execute('PRAGMA cache_size = 10000')
    cursor.execute('PRAGMA temp_store = MEMORY')

    # 创建表存储第一个文件的数据
    cursor.execute('''
        CREATE TABLE labels_file1 (
            id TEXT PRIMARY KEY,
            content_hash TEXT NOT NULL,
            full_data TEXT NOT NULL
        )
    ''')

    # 创建索引提高查询性能
    cursor.execute('CREATE INDEX idx_content_hash ON labels_file1(content_hash)')

    conn.commit()
    return conn

def process_first_file(filename, db_connection):
    """处理第一个文件，将数据存储到数据库，使用批量操作优化性能"""
    write_log(f"开始处理第一个文件: {filename}")
    cursor = db_connection.cursor()

    # 估算总记录数用于进度显示
    total_records = estimate_total_records(filename)
    start_time = time.time()

    processed_count = 0
    batch_data = []

    try:
        with gzip.open(filename, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_label = False

            for line in f:
                # 处理单行格式的label标签
                if '<label><id>' in line and '</label>' in line:
                    # 这是一个完整的单行label
                    label_data = extract_label_fields(line)
                    if label_data:
                        content_hash = calculate_content_hash(label_data)

                        # 添加到批量数据
                        batch_data.append((
                            label_data['id'],
                            content_hash,
                            json.dumps(label_data, ensure_ascii=False)
                        ))

                        processed_count += 1

                        # 批量插入数据
                        if len(batch_data) >= BATCH_SIZE:
                            cursor.executemany('''
                                INSERT OR REPLACE INTO labels_file1 (id, content_hash, full_data)
                                VALUES (?, ?, ?)
                            ''', batch_data)
                            db_connection.commit()
                            batch_data = []

                        # 显示进度
                        if processed_count % PROGRESS_INTERVAL == 0:
                            show_progress(processed_count, total_records, start_time, "处理文件1")

                # 处理多行格式的label标签
                elif '<label>' in line and not '<label><id>' in line:
                    buffer = line
                    in_label = True
                elif '</label>' in line and in_label:
                    buffer += line
                    in_label = False

                    # 提取标签数据
                    label_data = extract_label_fields(buffer)
                    if label_data:
                        content_hash = calculate_content_hash(label_data)

                        # 添加到批量数据
                        batch_data.append((
                            label_data['id'],
                            content_hash,
                            json.dumps(label_data, ensure_ascii=False)
                        ))

                        processed_count += 1

                        # 批量插入数据
                        if len(batch_data) >= BATCH_SIZE:
                            cursor.executemany('''
                                INSERT OR REPLACE INTO labels_file1 (id, content_hash, full_data)
                                VALUES (?, ?, ?)
                            ''', batch_data)
                            db_connection.commit()
                            batch_data = []

                        # 显示进度
                        if processed_count % PROGRESS_INTERVAL == 0:
                            show_progress(processed_count, total_records, start_time, "处理文件1")

                    buffer = ""
                elif in_label:
                    buffer += line

        # 处理剩余的批量数据
        if batch_data:
            cursor.executemany('''
                INSERT OR REPLACE INTO labels_file1 (id, content_hash, full_data)
                VALUES (?, ?, ?)
            ''', batch_data)

    except Exception as e:
        write_log(f"处理第一个文件时出错: {e}")
        raise

    db_connection.commit()
    elapsed = time.time() - start_time
    write_log(f"第一个文件处理完成，共处理 {processed_count:,} 条记录，耗时 {format_time(elapsed)}")
    return processed_count

def create_label_document(label_data, sequential_id, operation_status):
    """创建完整的标签文档，包含所有process_labels.py中的字段"""
    current_time = datetime.now()

    return {
        'id': label_data['id'],
        'y_id': f"YL{sequential_id}",
        'name': label_data['name'],
        'profile': label_data['profile'],
        'contactinfo': label_data['contactinfo'],
        'sublabels': (json.dumps(label_data['sublabels'], ensure_ascii=False)
                     if label_data['sublabels'] else ''),
        'parent_label': (json.dumps(label_data['parent_label'], ensure_ascii=False)
                        if label_data['parent_label'] else ''),
        'source': Source.DISCOGS.value,
        'created_at': current_time.isoformat(),
        'operation_status': operation_status  # CREATE, UPDATE, DELETE
    }

def process_record_comparison(label_data, cursor, file2_ids, csv_writer,
                            sequential_id, stats):
    """处理单条记录的比较逻辑"""
    label_id = label_data['id']
    file2_ids.add(label_id)
    content_hash = calculate_content_hash(label_data)

    # 查询第一个文件中是否存在该记录
    cursor.execute('SELECT content_hash FROM labels_file1 WHERE id = ?', (label_id,))
    result = cursor.fetchone()

    if result is None:
        operation_status = 'CREATE'
        stats['create_count'] += 1
    elif result[0] != content_hash:
        operation_status = 'UPDATE'
        stats['update_count'] += 1
    else:
        operation_status = 'NO_CHANGE'
        stats['no_change_count'] += 1

    # 调试信息：输出前几条记录的详细比较信息
    if DEBUG_MODE and stats['processed_count'] < DEBUG_RECORD_COUNT:
        write_log(f"[DEBUG] 记录 {label_id}:")
        write_log(f"  名称: {label_data.get('name', 'N/A')}")
        write_log(f"  文件2哈希: {content_hash}")
        if result:
            write_log(f"  文件1哈希: {result[0]}")
            write_log(f"  哈希匹配: {result[0] == content_hash}")
        else:
            write_log(f"  文件1中不存在此记录")
        write_log(f"  操作状态: {operation_status}")

    # 根据配置决定是否输出记录
    should_output = OUTPUT_ALL_RECORDS or operation_status != 'NO_CHANGE'

    if should_output:
        doc = create_label_document(label_data, sequential_id[0], operation_status)
        csv_writer.writerow(doc)
        sequential_id[0] += 1

    return operation_status

def compare_and_generate_csv(filename, db_connection, csv_writer):
    """比较第二个文件并生成CSV，优化性能和内存使用"""
    write_log(f"开始比较第二个文件: {filename}")
    cursor = db_connection.cursor()

    # 估算总记录数
    total_records = estimate_total_records(filename)
    start_time = time.time()

    # 统计信息
    stats = {
        'processed_count': 0,
        'create_count': 0,
        'update_count': 0,
        'no_change_count': 0
    }

    sequential_id = [1]  # 使用列表以便在函数间传递引用
    file2_ids = set()

    try:
        with gzip.open(filename, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_label = False

            for line in f:
                # 处理单行格式的label标签
                if '<label><id>' in line and '</label>' in line:
                    # 这是一个完整的单行label
                    label_data = extract_label_fields(line)
                    if label_data:
                        process_record_comparison(label_data, cursor, file2_ids,
                                                csv_writer, sequential_id, stats)
                        stats['processed_count'] += 1

                        # 显示进度
                        if stats['processed_count'] % PROGRESS_INTERVAL == 0:
                            show_progress(stats['processed_count'], total_records,
                                        start_time, "比较文件2")

                # 处理多行格式的label标签
                elif '<label>' in line and not '<label><id>' in line:
                    buffer = line
                    in_label = True
                elif '</label>' in line and in_label:
                    buffer += line
                    in_label = False

                    # 提取标签数据
                    label_data = extract_label_fields(buffer)
                    if label_data:
                        process_record_comparison(label_data, cursor, file2_ids,
                                                csv_writer, sequential_id, stats)
                        stats['processed_count'] += 1

                        # 显示进度
                        if stats['processed_count'] % PROGRESS_INTERVAL == 0:
                            show_progress(stats['processed_count'], total_records,
                                        start_time, "比较文件2")

                    buffer = ""
                elif in_label:
                    buffer += line

    except Exception as e:
        write_log(f"比较第二个文件时出错: {e}")
        raise

    # 查找被删除的记录
    delete_count = process_deleted_records(cursor, file2_ids, csv_writer, sequential_id)

    write_log(f"比较完成 - 文件2总计: {stats['processed_count']:,} 条记录")
    write_log(f"新增: {stats['create_count']:,}, 更新: {stats['update_count']:,}, "
             f"无变化: {stats['no_change_count']:,}, 删除: {delete_count:,}")

    return (stats['processed_count'], stats['create_count'],
            stats['update_count'], delete_count, stats['no_change_count'])

def process_deleted_records(cursor, file2_ids, csv_writer, sequential_id):
    """处理被删除的记录"""
    write_log("查找被删除的记录...")
    cursor.execute('SELECT COUNT(*) FROM labels_file1')
    total_file1_records = cursor.fetchone()[0]

    cursor.execute('SELECT id, full_data FROM labels_file1')
    delete_count = 0
    processed = 0

    for row in cursor.fetchall():
        label_id, full_data_json = row
        processed += 1

        if label_id not in file2_ids:
            # 这是一个被删除的记录，删除记录属于变化，应该输出
            label_data = json.loads(full_data_json)
            doc = create_label_document(label_data, sequential_id[0], 'DELETE')
            csv_writer.writerow(doc)
            sequential_id[0] += 1
            delete_count += 1

        # 显示删除检查进度
        if processed % PROGRESS_INTERVAL == 0:
            percentage = (processed / total_file1_records) * 100
            write_log(f"检查删除记录: {processed:,}/{total_file1_records:,} "
                     f"({percentage:.1f}%) 已发现删除: {delete_count:,}")

    return delete_count

def main():
    """主函数"""
    start_time = time.time()
    write_log("="*60)
    write_log("标签比较程序启动")
    write_log("="*60)
    write_log("配置信息:")
    write_log(f"  文件1: {FILE_1}")
    write_log(f"  文件2: {FILE_2}")
    write_log(f"  输出文件: {OUTPUT_CSV}")
    write_log(f"  输出模式: {'所有记录' if OUTPUT_ALL_RECORDS else '仅变化记录'}")
    write_log(f"  批量大小: {BATCH_SIZE:,}")
    write_log(f"  进度间隔: {PROGRESS_INTERVAL:,}")
    write_log("="*60)

    try:
        # 设置数据库
        write_log("设置临时数据库...")
        db_conn = setup_database()

        # 处理第一个文件
        file1_count = process_first_file(FILE_1, db_conn)

        # 准备CSV输出
        csv_fieldnames = [
            'id', 'y_id', 'name', 'profile', 'contactinfo', 'sublabels', 'parent_label',
            'source', 'created_at', 'operation_status'
        ]

        write_log(f"开始生成CSV文件: {OUTPUT_CSV}")
        with open(OUTPUT_CSV, 'w', newline='', encoding='utf-8') as csvfile:
            csv_writer = csv.DictWriter(csvfile, fieldnames=csv_fieldnames, quoting=csv.QUOTE_ALL)
            csv_writer.writeheader()

            # 比较第二个文件并生成CSV
            (total_count, create_count, update_count,
             delete_count, no_change_count) = compare_and_generate_csv(
                FILE_2, db_conn, csv_writer
            )

        # 清理临时数据库
        db_conn.close()
        if os.path.exists(TEMP_DB):
            os.remove(TEMP_DB)

        # 输出最终统计
        processing_time = time.time() - start_time
        total_changes = create_count + update_count + delete_count

        write_log("="*60)
        write_log("比较结果统计")
        write_log("="*60)
        write_log(f"第一个文件记录数: {file1_count:,}")
        write_log(f"第二个文件记录数: {total_count:,}")
        write_log(f"新增记录: {create_count:,}")
        write_log(f"更新记录: {update_count:,}")
        write_log(f"删除记录: {delete_count:,}")
        write_log(f"无变化记录: {no_change_count:,}")
        write_log(f"总变化记录: {total_changes:,}")
        write_log(f"变化率: {(total_changes/max(file1_count, total_count)*100):.2f}%")
        write_log(f"处理时间: {format_time(processing_time)}")
        write_log(f"平均处理速度: {(file1_count + total_count)/processing_time:.1f} 条/秒")
        write_log(f"输出模式: {'所有记录' if OUTPUT_ALL_RECORDS else '仅变化记录'}")
        write_log(f"结果已保存到: {OUTPUT_CSV}")
        write_log("="*60)

    except Exception as e:
        write_log(f"程序执行出错: {e}")
        raise

if __name__ == "__main__":
    main()
