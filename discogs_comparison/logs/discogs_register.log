2025-08-02 19:37:54,577 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-02 19:38:03,135 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-02 19:38:03,136 - discogs_register - INFO - ==================================================
2025-08-02 19:38:03,136 - discogs_register - INFO - Discogs 自动注册程序启动
2025-08-02 19:38:03,136 - discogs_register - INFO - ==================================================
2025-08-02 19:38:03,136 - discogs_register - INFO - 注册配置信息 | Extra: {"registration": {"url": "https://login.discogs.com/u/signup?state=hKFo2SBkWkFnT2lGLVpXQjI5S2tSZmtjT2tfS1ItSmJ3X2x0d6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFpOVcwQnVlNmE5eFRpN0ZSX2NUMGs2S3E5YTNuQmRko2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg", "timeout_seconds": 30, "retry_attempts": 3, "retry_delay_seconds": 5}, "accounts": [{"username": "ywl2025005", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025006", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025007", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025008", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025009", "email": "<EMAIL>", "password": "Abcd@1234.", "enabled": true}], "browser": {"type": "chrome", "show_window": true, "window_size": {"width": 1280, "height": 720}, "implicit_wait": 10, "page_load_timeout": 30, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/121.0"]}, "selectors": {"username_field": "input[name='username']", "email_field": "input[name='email']", "password_field": "in******************']", "confirm_password_field": "in*******************************']", "submit_button": "button[type='submit'], input[type='submit']", "captcha_container": ".captcha, .recaptcha, [id*='captcha'], [class*='captcha']", "error_messages": ".error, .alert-danger, [class*='error']", "success_messages": ".success, .alert-success, [class*='success']"}, "automation": {"min_delay": 1, "max_delay": 3, "typing_delay": 0.1, "captcha_handling": {"enabled": true, "wait_time": 60, "manual_intervention": true}}, "logging": {"level": "INFO", "log_file": "discogs_register.log", "max_size_mb": 50, "backup_count": 3, "detailed_browser_logs": false}, "proxy": {"enabled": false}, "verification": {"success_indicators": ["welcome", "registration successful", "account created", "verify your email"], "timeout": 30}}
2025-08-02 19:38:03,136 - discogs_register - INFO - 开始批量注册，共 5 个账号
2025-08-02 19:38:03,136 - discogs_register - INFO - 正在处理第 1/5 个账号: ywl2025005
2025-08-02 19:38:03,136 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 19:38:03,136 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:38:03,136 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:38:03,137 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:39:54,544 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:54,546 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:54,549 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:54,554 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025005
2025-08-02 19:39:59,559 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 19:39:59,560 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:39:59,560 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:39:59,560 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:39:59,879 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:59,879 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:59,879 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:59,880 - discogs_register - INFO - 第 3 次重试注册账号: ywl2025005
2025-08-02 19:40:04,885 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 19:40:04,885 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:40:04,885 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:40:04,886 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:40:05,013 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:05,013 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:05,013 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:05,014 - discogs_register - INFO - 等待 7.9 秒后处理下一个账号...
2025-08-02 19:40:12,921 - discogs_register - INFO - 正在处理第 2/5 个账号: ywl2025006
2025-08-02 19:40:12,922 - discogs_register - INFO - 开始注册账号: ywl2025006
2025-08-02 19:40:12,922 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:40:12,923 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:40:12,923 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:40:13,066 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:13,067 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:13,067 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:13,068 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025006
2025-08-02 19:40:18,074 - discogs_register - INFO - 开始注册账号: ywl2025006
2025-08-02 19:40:18,074 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:40:18,075 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:40:18,075 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:40:18,217 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:18,217 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:18,217 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:18,218 - discogs_register - INFO - 第 3 次重试注册账号: ywl2025006
2025-08-02 19:40:23,221 - discogs_register - INFO - 开始注册账号: ywl2025006
2025-08-02 19:40:23,222 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:40:23,222 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:40:23,222 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:40:23,364 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:23,365 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:23,365 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:23,365 - discogs_register - INFO - 等待 9.9 秒后处理下一个账号...
2025-08-02 19:40:33,232 - discogs_register - INFO - 正在处理第 3/5 个账号: ywl2025007
2025-08-02 19:40:33,233 - discogs_register - INFO - 开始注册账号: ywl2025007
2025-08-02 19:40:33,233 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:40:33,233 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:40:33,233 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:40:33,366 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:33,367 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:33,367 - discogs_register - ERROR - 注册账号 ywl2025007 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:33,368 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025007
2025-08-02 19:40:38,373 - discogs_register - INFO - 开始注册账号: ywl2025007
2025-08-02 19:40:38,374 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:40:38,374 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:40:38,374 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:40:38,520 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:38,521 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:38,521 - discogs_register - ERROR - 注册账号 ywl2025007 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:38,522 - discogs_register - INFO - 第 3 次重试注册账号: ywl2025007
2025-08-02 19:40:43,527 - discogs_register - INFO - 开始注册账号: ywl2025007
2025-08-02 19:40:43,527 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:40:43,527 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:40:43,527 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:40:43,676 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:43,676 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:43,677 - discogs_register - ERROR - 注册账号 ywl2025007 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:43,677 - discogs_register - INFO - 等待 13.4 秒后处理下一个账号...
2025-08-02 19:40:57,043 - discogs_register - INFO - 正在处理第 4/5 个账号: ywl2025008
2025-08-02 19:40:57,044 - discogs_register - INFO - 开始注册账号: ywl2025008
2025-08-02 19:40:57,044 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:40:57,044 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:40:57,044 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:40:57,189 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:57,189 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:57,189 - discogs_register - ERROR - 注册账号 ywl2025008 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:57,189 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025008
2025-08-02 19:41:02,191 - discogs_register - INFO - 开始注册账号: ywl2025008
2025-08-02 19:41:02,191 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:41:02,191 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:41:02,191 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:41:22,130 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:22,131 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:22,131 - discogs_register - ERROR - 注册账号 ywl2025008 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:22,131 - discogs_register - INFO - 第 3 次重试注册账号: ywl2025008
2025-08-02 19:41:27,137 - discogs_register - INFO - 开始注册账号: ywl2025008
2025-08-02 19:41:27,138 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:41:27,138 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:41:27,138 - discogs_register - INFO - 尝试使用PATH中的chromedriver...
2025-08-02 19:41:27,496 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:27,497 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:27,497 - discogs_register - ERROR - 注册账号 ywl2025008 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:27,497 - discogs_register - INFO - 等待 13.2 秒后处理下一个账号...
2025-08-02 19:41:42,208 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-02 19:41:42,208 - discogs_register - INFO - ==================================================
2025-08-02 19:41:42,208 - discogs_register - INFO - Discogs 自动注册程序启动
2025-08-02 19:41:42,208 - discogs_register - INFO - ==================================================
2025-08-02 19:41:42,208 - discogs_register - INFO - 注册配置信息 | Extra: {"registration": {"url": "https://login.discogs.com/u/signup?state=hKFo2SBkWkFnT2lGLVpXQjI5S2tSZmtjT2tfS1ItSmJ3X2x0d6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFpOVcwQnVlNmE5eFRpN0ZSX2NUMGs2S3E5YTNuQmRko2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg", "timeout_seconds": 30, "retry_attempts": 3, "retry_delay_seconds": 5}, "accounts": [{"username": "ywl2025005", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025006", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025007", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025008", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025009", "email": "<EMAIL>", "password": "Abcd@1234.", "enabled": true}], "browser": {"type": "chrome", "show_window": true, "window_size": {"width": 1280, "height": 720}, "implicit_wait": 10, "page_load_timeout": 30, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/121.0"]}, "selectors": {"username_field": "input[name='username']", "email_field": "input[name='email']", "password_field": "in******************']", "confirm_password_field": "in*******************************']", "submit_button": "button[type='submit'], input[type='submit']", "captcha_container": ".captcha, .recaptcha, [id*='captcha'], [class*='captcha']", "error_messages": ".error, .alert-danger, [class*='error']", "success_messages": ".success, .alert-success, [class*='success']"}, "automation": {"min_delay": 1, "max_delay": 3, "typing_delay": 0.1, "captcha_handling": {"enabled": true, "wait_time": 60, "manual_intervention": true}}, "logging": {"level": "INFO", "log_file": "discogs_register.log", "max_size_mb": 50, "backup_count": 3, "detailed_browser_logs": false}, "proxy": {"enabled": false}, "verification": {"success_indicators": ["welcome", "registration successful", "account created", "verify your email"], "timeout": 30}}
2025-08-02 19:41:42,208 - discogs_register - INFO - 开始批量注册，共 5 个账号
2025-08-02 19:41:42,208 - discogs_register - INFO - 正在处理第 1/5 个账号: ywl2025005
2025-08-02 19:41:42,208 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 19:41:42,208 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:41:42,208 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:41:52,425 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x000000010065d6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100654b73 chromedriver + 4901747
2   chromedriver                        0x0000000100212616 chromedriver + 435734
3   chromedriver                        0x0000000100244d10 chromedriver + 642320
4   chromedriver                        0x000000010024018a chromedriver + 622986
5   chromedriver                        0x000000010023c67c chromedriver + 607868
6   chromedriver                        0x0000000100283a08 chromedriver + 899592
7   chromedriver                        0x0000000100282ebf chromedriver + 896703
8   chromedriver                        0x0000000100279de3 chromedriver + 859619
9   chromedriver                        0x0000000100247d7f chromedriver + 654719
10  chromedriver                        0x00000001002490de chromedriver + 659678
11  chromedriver                        0x00000001006192ad chromedriver + 4657837
12  chromedriver                        0x000000010061e130 chromedriver + 4677936
13  chromedriver                        0x0000000100624def chromedriver + 4705775
14  chromedriver                        0x000000010061f05a chromedriver + 4681818
15  chromedriver                        0x00000001005f192c chromedriver + 4495660
16  chromedriver                        0x000000010063c838 chromedriver + 4802616
17  chromedriver                        0x000000010063c9b7 chromedriver + 4802999
18  chromedriver                        0x000000010064d99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:41:52,425 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x000000010065d6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100654b73 chromedriver + 4901747
2   chromedriver                        0x0000000100212616 chromedriver + 435734
3   chromedriver                        0x0000000100244d10 chromedriver + 642320
4   chromedriver                        0x000000010024018a chromedriver + 622986
5   chromedriver                        0x000000010023c67c chromedriver + 607868
6   chromedriver                        0x0000000100283a08 chromedriver + 899592
7   chromedriver                        0x0000000100282ebf chromedriver + 896703
8   chromedriver                        0x0000000100279de3 chromedriver + 859619
9   chromedriver                        0x0000000100247d7f chromedriver + 654719
10  chromedriver                        0x00000001002490de chromedriver + 659678
11  chromedriver                        0x00000001006192ad chromedriver + 4657837
12  chromedriver                        0x000000010061e130 chromedriver + 4677936
13  chromedriver                        0x0000000100624def chromedriver + 4705775
14  chromedriver                        0x000000010061f05a chromedriver + 4681818
15  chromedriver                        0x00000001005f192c chromedriver + 4495660
16  chromedriver                        0x000000010063c838 chromedriver + 4802616
17  chromedriver                        0x000000010063c9b7 chromedriver + 4802999
18  chromedriver                        0x000000010064d99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:41:52,425 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x000000010065d6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100654b73 chromedriver + 4901747
2   chromedriver                        0x0000000100212616 chromedriver + 435734
3   chromedriver                        0x0000000100244d10 chromedriver + 642320
4   chromedriver                        0x000000010024018a chromedriver + 622986
5   chromedriver                        0x000000010023c67c chromedriver + 607868
6   chromedriver                        0x0000000100283a08 chromedriver + 899592
7   chromedriver                        0x0000000100282ebf chromedriver + 896703
8   chromedriver                        0x0000000100279de3 chromedriver + 859619
9   chromedriver                        0x0000000100247d7f chromedriver + 654719
10  chromedriver                        0x00000001002490de chromedriver + 659678
11  chromedriver                        0x00000001006192ad chromedriver + 4657837
12  chromedriver                        0x000000010061e130 chromedriver + 4677936
13  chromedriver                        0x0000000100624def chromedriver + 4705775
14  chromedriver                        0x000000010061f05a chromedriver + 4681818
15  chromedriver                        0x00000001005f192c chromedriver + 4495660
16  chromedriver                        0x000000010063c838 chromedriver + 4802616
17  chromedriver                        0x000000010063c9b7 chromedriver + 4802999
18  chromedriver                        0x000000010064d99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x000000010065d6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100654b73 chromedriver + 4901747
2   chromedriver                        0x0000000100212616 chromedriver + 435734
3   chromedriver                        0x0000000100244d10 chromedriver + 642320
4   chromedriver                        0x000000010024018a chromedriver + 622986
5   chromedriver                        0x000000010023c67c chromedriver + 607868
6   chromedriver                        0x0000000100283a08 chromedriver + 899592
7   chromedriver                        0x0000000100282ebf chromedriver + 896703
8   chromedriver                        0x0000000100279de3 chromedriver + 859619
9   chromedriver                        0x0000000100247d7f chromedriver + 654719
10  chromedriver                        0x00000001002490de chromedriver + 659678
11  chromedriver                        0x00000001006192ad chromedriver + 4657837
12  chromedriver                        0x000000010061e130 chromedriver + 4677936
13  chromedriver                        0x0000000100624def chromedriver + 4705775
14  chromedriver                        0x000000010061f05a chromedriver + 4681818
15  chromedriver                        0x00000001005f192c chromedriver + 4495660
16  chromedriver                        0x000000010063c838 chromedriver + 4802616
17  chromedriver                        0x000000010063c9b7 chromedriver + 4802999
18  chromedriver                        0x000000010064d99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:41:55,308 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025005
2025-08-02 19:42:00,314 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 19:42:00,319 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:42:00,319 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:42:06,278 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001027816b8 chromedriver + 4937400
1   chromedriver                        0x0000000102778b73 chromedriver + 4901747
2   chromedriver                        0x0000000102336616 chromedriver + 435734
3   chromedriver                        0x0000000102368d10 chromedriver + 642320
4   chromedriver                        0x000000010236418a chromedriver + 622986
5   chromedriver                        0x000000010236067c chromedriver + 607868
6   chromedriver                        0x00000001023a7a08 chromedriver + 899592
7   chromedriver                        0x00000001023a6ebf chromedriver + 896703
8   chromedriver                        0x000000010239dde3 chromedriver + 859619
9   chromedriver                        0x000000010236bd7f chromedriver + 654719
10  chromedriver                        0x000000010236d0de chromedriver + 659678
11  chromedriver                        0x000000010273d2ad chromedriver + 4657837
12  chromedriver                        0x0000000102742130 chromedriver + 4677936
13  chromedriver                        0x0000000102748def chromedriver + 4705775
14  chromedriver                        0x000000010274305a chromedriver + 4681818
15  chromedriver                        0x000000010271592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001027609b7 chromedriver + 4802999
18  chromedriver                        0x000000010277199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:06,278 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001027816b8 chromedriver + 4937400
1   chromedriver                        0x0000000102778b73 chromedriver + 4901747
2   chromedriver                        0x0000000102336616 chromedriver + 435734
3   chromedriver                        0x0000000102368d10 chromedriver + 642320
4   chromedriver                        0x000000010236418a chromedriver + 622986
5   chromedriver                        0x000000010236067c chromedriver + 607868
6   chromedriver                        0x00000001023a7a08 chromedriver + 899592
7   chromedriver                        0x00000001023a6ebf chromedriver + 896703
8   chromedriver                        0x000000010239dde3 chromedriver + 859619
9   chromedriver                        0x000000010236bd7f chromedriver + 654719
10  chromedriver                        0x000000010236d0de chromedriver + 659678
11  chromedriver                        0x000000010273d2ad chromedriver + 4657837
12  chromedriver                        0x0000000102742130 chromedriver + 4677936
13  chromedriver                        0x0000000102748def chromedriver + 4705775
14  chromedriver                        0x000000010274305a chromedriver + 4681818
15  chromedriver                        0x000000010271592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001027609b7 chromedriver + 4802999
18  chromedriver                        0x000000010277199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:06,278 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001027816b8 chromedriver + 4937400
1   chromedriver                        0x0000000102778b73 chromedriver + 4901747
2   chromedriver                        0x0000000102336616 chromedriver + 435734
3   chromedriver                        0x0000000102368d10 chromedriver + 642320
4   chromedriver                        0x000000010236418a chromedriver + 622986
5   chromedriver                        0x000000010236067c chromedriver + 607868
6   chromedriver                        0x00000001023a7a08 chromedriver + 899592
7   chromedriver                        0x00000001023a6ebf chromedriver + 896703
8   chromedriver                        0x000000010239dde3 chromedriver + 859619
9   chromedriver                        0x000000010236bd7f chromedriver + 654719
10  chromedriver                        0x000000010236d0de chromedriver + 659678
11  chromedriver                        0x000000010273d2ad chromedriver + 4657837
12  chromedriver                        0x0000000102742130 chromedriver + 4677936
13  chromedriver                        0x0000000102748def chromedriver + 4705775
14  chromedriver                        0x000000010274305a chromedriver + 4681818
15  chromedriver                        0x000000010271592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001027609b7 chromedriver + 4802999
18  chromedriver                        0x000000010277199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001027816b8 chromedriver + 4937400
1   chromedriver                        0x0000000102778b73 chromedriver + 4901747
2   chromedriver                        0x0000000102336616 chromedriver + 435734
3   chromedriver                        0x0000000102368d10 chromedriver + 642320
4   chromedriver                        0x000000010236418a chromedriver + 622986
5   chromedriver                        0x000000010236067c chromedriver + 607868
6   chromedriver                        0x00000001023a7a08 chromedriver + 899592
7   chromedriver                        0x00000001023a6ebf chromedriver + 896703
8   chromedriver                        0x000000010239dde3 chromedriver + 859619
9   chromedriver                        0x000000010236bd7f chromedriver + 654719
10  chromedriver                        0x000000010236d0de chromedriver + 659678
11  chromedriver                        0x000000010273d2ad chromedriver + 4657837
12  chromedriver                        0x0000000102742130 chromedriver + 4677936
13  chromedriver                        0x0000000102748def chromedriver + 4705775
14  chromedriver                        0x000000010274305a chromedriver + 4681818
15  chromedriver                        0x000000010271592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001027609b7 chromedriver + 4802999
18  chromedriver                        0x000000010277199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:08,568 - discogs_register - INFO - 第 3 次重试注册账号: ywl2025005
2025-08-02 19:42:13,573 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 19:42:13,574 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:42:13,574 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:42:18,701 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000100fbb6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100fb2b73 chromedriver + 4901747
2   chromedriver                        0x0000000100b70616 chromedriver + 435734
3   chromedriver                        0x0000000100ba2d10 chromedriver + 642320
4   chromedriver                        0x0000000100b9e18a chromedriver + 622986
5   chromedriver                        0x0000000100b9a67c chromedriver + 607868
6   chromedriver                        0x0000000100be1a08 chromedriver + 899592
7   chromedriver                        0x0000000100be0ebf chromedriver + 896703
8   chromedriver                        0x0000000100bd7de3 chromedriver + 859619
9   chromedriver                        0x0000000100ba5d7f chromedriver + 654719
10  chromedriver                        0x0000000100ba70de chromedriver + 659678
11  chromedriver                        0x0000000100f772ad chromedriver + 4657837
12  chromedriver                        0x0000000100f7c130 chromedriver + 4677936
13  chromedriver                        0x0000000100f82def chromedriver + 4705775
14  chromedriver                        0x0000000100f7d05a chromedriver + 4681818
15  chromedriver                        0x0000000100f4f92c chromedriver + 4495660
16  chromedriver                        0x0000000100f9a838 chromedriver + 4802616
17  chromedriver                        0x0000000100f9a9b7 chromedriver + 4802999
18  chromedriver                        0x0000000100fab99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:18,702 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000100fbb6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100fb2b73 chromedriver + 4901747
2   chromedriver                        0x0000000100b70616 chromedriver + 435734
3   chromedriver                        0x0000000100ba2d10 chromedriver + 642320
4   chromedriver                        0x0000000100b9e18a chromedriver + 622986
5   chromedriver                        0x0000000100b9a67c chromedriver + 607868
6   chromedriver                        0x0000000100be1a08 chromedriver + 899592
7   chromedriver                        0x0000000100be0ebf chromedriver + 896703
8   chromedriver                        0x0000000100bd7de3 chromedriver + 859619
9   chromedriver                        0x0000000100ba5d7f chromedriver + 654719
10  chromedriver                        0x0000000100ba70de chromedriver + 659678
11  chromedriver                        0x0000000100f772ad chromedriver + 4657837
12  chromedriver                        0x0000000100f7c130 chromedriver + 4677936
13  chromedriver                        0x0000000100f82def chromedriver + 4705775
14  chromedriver                        0x0000000100f7d05a chromedriver + 4681818
15  chromedriver                        0x0000000100f4f92c chromedriver + 4495660
16  chromedriver                        0x0000000100f9a838 chromedriver + 4802616
17  chromedriver                        0x0000000100f9a9b7 chromedriver + 4802999
18  chromedriver                        0x0000000100fab99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:18,702 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000100fbb6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100fb2b73 chromedriver + 4901747
2   chromedriver                        0x0000000100b70616 chromedriver + 435734
3   chromedriver                        0x0000000100ba2d10 chromedriver + 642320
4   chromedriver                        0x0000000100b9e18a chromedriver + 622986
5   chromedriver                        0x0000000100b9a67c chromedriver + 607868
6   chromedriver                        0x0000000100be1a08 chromedriver + 899592
7   chromedriver                        0x0000000100be0ebf chromedriver + 896703
8   chromedriver                        0x0000000100bd7de3 chromedriver + 859619
9   chromedriver                        0x0000000100ba5d7f chromedriver + 654719
10  chromedriver                        0x0000000100ba70de chromedriver + 659678
11  chromedriver                        0x0000000100f772ad chromedriver + 4657837
12  chromedriver                        0x0000000100f7c130 chromedriver + 4677936
13  chromedriver                        0x0000000100f82def chromedriver + 4705775
14  chromedriver                        0x0000000100f7d05a chromedriver + 4681818
15  chromedriver                        0x0000000100f4f92c chromedriver + 4495660
16  chromedriver                        0x0000000100f9a838 chromedriver + 4802616
17  chromedriver                        0x0000000100f9a9b7 chromedriver + 4802999
18  chromedriver                        0x0000000100fab99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000100fbb6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100fb2b73 chromedriver + 4901747
2   chromedriver                        0x0000000100b70616 chromedriver + 435734
3   chromedriver                        0x0000000100ba2d10 chromedriver + 642320
4   chromedriver                        0x0000000100b9e18a chromedriver + 622986
5   chromedriver                        0x0000000100b9a67c chromedriver + 607868
6   chromedriver                        0x0000000100be1a08 chromedriver + 899592
7   chromedriver                        0x0000000100be0ebf chromedriver + 896703
8   chromedriver                        0x0000000100bd7de3 chromedriver + 859619
9   chromedriver                        0x0000000100ba5d7f chromedriver + 654719
10  chromedriver                        0x0000000100ba70de chromedriver + 659678
11  chromedriver                        0x0000000100f772ad chromedriver + 4657837
12  chromedriver                        0x0000000100f7c130 chromedriver + 4677936
13  chromedriver                        0x0000000100f82def chromedriver + 4705775
14  chromedriver                        0x0000000100f7d05a chromedriver + 4681818
15  chromedriver                        0x0000000100f4f92c chromedriver + 4495660
16  chromedriver                        0x0000000100f9a838 chromedriver + 4802616
17  chromedriver                        0x0000000100f9a9b7 chromedriver + 4802999
18  chromedriver                        0x0000000100fab99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:20,978 - discogs_register - INFO - 等待 13.6 秒后处理下一个账号...
2025-08-02 19:42:34,564 - discogs_register - INFO - 正在处理第 2/5 个账号: ywl2025006
2025-08-02 19:42:34,566 - discogs_register - INFO - 开始注册账号: ywl2025006
2025-08-02 19:42:34,566 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:42:34,566 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:42:39,780 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001005f56b8 chromedriver + 4937400
1   chromedriver                        0x00000001005ecb73 chromedriver + 4901747
2   chromedriver                        0x00000001001aa616 chromedriver + 435734
3   chromedriver                        0x00000001001dcd10 chromedriver + 642320
4   chromedriver                        0x00000001001d818a chromedriver + 622986
5   chromedriver                        0x00000001001d467c chromedriver + 607868
6   chromedriver                        0x000000010021ba08 chromedriver + 899592
7   chromedriver                        0x000000010021aebf chromedriver + 896703
8   chromedriver                        0x0000000100211de3 chromedriver + 859619
9   chromedriver                        0x00000001001dfd7f chromedriver + 654719
10  chromedriver                        0x00000001001e10de chromedriver + 659678
11  chromedriver                        0x00000001005b12ad chromedriver + 4657837
12  chromedriver                        0x00000001005b6130 chromedriver + 4677936
13  chromedriver                        0x00000001005bcdef chromedriver + 4705775
14  chromedriver                        0x00000001005b705a chromedriver + 4681818
15  chromedriver                        0x000000010058992c chromedriver + 4495660
16  chromedriver                        0x00000001005d4838 chromedriver + 4802616
17  chromedriver                        0x00000001005d49b7 chromedriver + 4802999
18  chromedriver                        0x00000001005e599f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:39,781 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001005f56b8 chromedriver + 4937400
1   chromedriver                        0x00000001005ecb73 chromedriver + 4901747
2   chromedriver                        0x00000001001aa616 chromedriver + 435734
3   chromedriver                        0x00000001001dcd10 chromedriver + 642320
4   chromedriver                        0x00000001001d818a chromedriver + 622986
5   chromedriver                        0x00000001001d467c chromedriver + 607868
6   chromedriver                        0x000000010021ba08 chromedriver + 899592
7   chromedriver                        0x000000010021aebf chromedriver + 896703
8   chromedriver                        0x0000000100211de3 chromedriver + 859619
9   chromedriver                        0x00000001001dfd7f chromedriver + 654719
10  chromedriver                        0x00000001001e10de chromedriver + 659678
11  chromedriver                        0x00000001005b12ad chromedriver + 4657837
12  chromedriver                        0x00000001005b6130 chromedriver + 4677936
13  chromedriver                        0x00000001005bcdef chromedriver + 4705775
14  chromedriver                        0x00000001005b705a chromedriver + 4681818
15  chromedriver                        0x000000010058992c chromedriver + 4495660
16  chromedriver                        0x00000001005d4838 chromedriver + 4802616
17  chromedriver                        0x00000001005d49b7 chromedriver + 4802999
18  chromedriver                        0x00000001005e599f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:39,781 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001005f56b8 chromedriver + 4937400
1   chromedriver                        0x00000001005ecb73 chromedriver + 4901747
2   chromedriver                        0x00000001001aa616 chromedriver + 435734
3   chromedriver                        0x00000001001dcd10 chromedriver + 642320
4   chromedriver                        0x00000001001d818a chromedriver + 622986
5   chromedriver                        0x00000001001d467c chromedriver + 607868
6   chromedriver                        0x000000010021ba08 chromedriver + 899592
7   chromedriver                        0x000000010021aebf chromedriver + 896703
8   chromedriver                        0x0000000100211de3 chromedriver + 859619
9   chromedriver                        0x00000001001dfd7f chromedriver + 654719
10  chromedriver                        0x00000001001e10de chromedriver + 659678
11  chromedriver                        0x00000001005b12ad chromedriver + 4657837
12  chromedriver                        0x00000001005b6130 chromedriver + 4677936
13  chromedriver                        0x00000001005bcdef chromedriver + 4705775
14  chromedriver                        0x00000001005b705a chromedriver + 4681818
15  chromedriver                        0x000000010058992c chromedriver + 4495660
16  chromedriver                        0x00000001005d4838 chromedriver + 4802616
17  chromedriver                        0x00000001005d49b7 chromedriver + 4802999
18  chromedriver                        0x00000001005e599f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001005f56b8 chromedriver + 4937400
1   chromedriver                        0x00000001005ecb73 chromedriver + 4901747
2   chromedriver                        0x00000001001aa616 chromedriver + 435734
3   chromedriver                        0x00000001001dcd10 chromedriver + 642320
4   chromedriver                        0x00000001001d818a chromedriver + 622986
5   chromedriver                        0x00000001001d467c chromedriver + 607868
6   chromedriver                        0x000000010021ba08 chromedriver + 899592
7   chromedriver                        0x000000010021aebf chromedriver + 896703
8   chromedriver                        0x0000000100211de3 chromedriver + 859619
9   chromedriver                        0x00000001001dfd7f chromedriver + 654719
10  chromedriver                        0x00000001001e10de chromedriver + 659678
11  chromedriver                        0x00000001005b12ad chromedriver + 4657837
12  chromedriver                        0x00000001005b6130 chromedriver + 4677936
13  chromedriver                        0x00000001005bcdef chromedriver + 4705775
14  chromedriver                        0x00000001005b705a chromedriver + 4681818
15  chromedriver                        0x000000010058992c chromedriver + 4495660
16  chromedriver                        0x00000001005d4838 chromedriver + 4802616
17  chromedriver                        0x00000001005d49b7 chromedriver + 4802999
18  chromedriver                        0x00000001005e599f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:43,588 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025006
2025-08-02 19:42:48,589 - discogs_register - INFO - 开始注册账号: ywl2025006
2025-08-02 19:42:48,589 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:42:48,589 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:42:53,792 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001010916b8 chromedriver + 4937400
1   chromedriver                        0x0000000101088b73 chromedriver + 4901747
2   chromedriver                        0x0000000100c46616 chromedriver + 435734
3   chromedriver                        0x0000000100c78d10 chromedriver + 642320
4   chromedriver                        0x0000000100c7418a chromedriver + 622986
5   chromedriver                        0x0000000100c7067c chromedriver + 607868
6   chromedriver                        0x0000000100cb7a08 chromedriver + 899592
7   chromedriver                        0x0000000100cb6ebf chromedriver + 896703
8   chromedriver                        0x0000000100cadde3 chromedriver + 859619
9   chromedriver                        0x0000000100c7bd7f chromedriver + 654719
10  chromedriver                        0x0000000100c7d0de chromedriver + 659678
11  chromedriver                        0x000000010104d2ad chromedriver + 4657837
12  chromedriver                        0x0000000101052130 chromedriver + 4677936
13  chromedriver                        0x0000000101058def chromedriver + 4705775
14  chromedriver                        0x000000010105305a chromedriver + 4681818
15  chromedriver                        0x000000010102592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001010709b7 chromedriver + 4802999
18  chromedriver                        0x000000010108199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:53,792 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001010916b8 chromedriver + 4937400
1   chromedriver                        0x0000000101088b73 chromedriver + 4901747
2   chromedriver                        0x0000000100c46616 chromedriver + 435734
3   chromedriver                        0x0000000100c78d10 chromedriver + 642320
4   chromedriver                        0x0000000100c7418a chromedriver + 622986
5   chromedriver                        0x0000000100c7067c chromedriver + 607868
6   chromedriver                        0x0000000100cb7a08 chromedriver + 899592
7   chromedriver                        0x0000000100cb6ebf chromedriver + 896703
8   chromedriver                        0x0000000100cadde3 chromedriver + 859619
9   chromedriver                        0x0000000100c7bd7f chromedriver + 654719
10  chromedriver                        0x0000000100c7d0de chromedriver + 659678
11  chromedriver                        0x000000010104d2ad chromedriver + 4657837
12  chromedriver                        0x0000000101052130 chromedriver + 4677936
13  chromedriver                        0x0000000101058def chromedriver + 4705775
14  chromedriver                        0x000000010105305a chromedriver + 4681818
15  chromedriver                        0x000000010102592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001010709b7 chromedriver + 4802999
18  chromedriver                        0x000000010108199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:53,792 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001010916b8 chromedriver + 4937400
1   chromedriver                        0x0000000101088b73 chromedriver + 4901747
2   chromedriver                        0x0000000100c46616 chromedriver + 435734
3   chromedriver                        0x0000000100c78d10 chromedriver + 642320
4   chromedriver                        0x0000000100c7418a chromedriver + 622986
5   chromedriver                        0x0000000100c7067c chromedriver + 607868
6   chromedriver                        0x0000000100cb7a08 chromedriver + 899592
7   chromedriver                        0x0000000100cb6ebf chromedriver + 896703
8   chromedriver                        0x0000000100cadde3 chromedriver + 859619
9   chromedriver                        0x0000000100c7bd7f chromedriver + 654719
10  chromedriver                        0x0000000100c7d0de chromedriver + 659678
11  chromedriver                        0x000000010104d2ad chromedriver + 4657837
12  chromedriver                        0x0000000101052130 chromedriver + 4677936
13  chromedriver                        0x0000000101058def chromedriver + 4705775
14  chromedriver                        0x000000010105305a chromedriver + 4681818
15  chromedriver                        0x000000010102592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001010709b7 chromedriver + 4802999
18  chromedriver                        0x000000010108199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001010916b8 chromedriver + 4937400
1   chromedriver                        0x0000000101088b73 chromedriver + 4901747
2   chromedriver                        0x0000000100c46616 chromedriver + 435734
3   chromedriver                        0x0000000100c78d10 chromedriver + 642320
4   chromedriver                        0x0000000100c7418a chromedriver + 622986
5   chromedriver                        0x0000000100c7067c chromedriver + 607868
6   chromedriver                        0x0000000100cb7a08 chromedriver + 899592
7   chromedriver                        0x0000000100cb6ebf chromedriver + 896703
8   chromedriver                        0x0000000100cadde3 chromedriver + 859619
9   chromedriver                        0x0000000100c7bd7f chromedriver + 654719
10  chromedriver                        0x0000000100c7d0de chromedriver + 659678
11  chromedriver                        0x000000010104d2ad chromedriver + 4657837
12  chromedriver                        0x0000000101052130 chromedriver + 4677936
13  chromedriver                        0x0000000101058def chromedriver + 4705775
14  chromedriver                        0x000000010105305a chromedriver + 4681818
15  chromedriver                        0x000000010102592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001010709b7 chromedriver + 4802999
18  chromedriver                        0x000000010108199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:43:01,661 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-02 19:43:01,661 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 19:43:01,661 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-02 19:43:01,661 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-02 19:43:06,843 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000102e6f6b8 chromedriver + 4937400
1   chromedriver                        0x0000000102e66b73 chromedriver + 4901747
2   chromedriver                        0x0000000102a24616 chromedriver + 435734
3   chromedriver                        0x0000000102a56d10 chromedriver + 642320
4   chromedriver                        0x0000000102a5218a chromedriver + 622986
5   chromedriver                        0x0000000102a4e67c chromedriver + 607868
6   chromedriver                        0x0000000102a95a08 chromedriver + 899592
7   chromedriver                        0x0000000102a94ebf chromedriver + 896703
8   chromedriver                        0x0000000102a8bde3 chromedriver + 859619
9   chromedriver                        0x0000000102a59d7f chromedriver + 654719
10  chromedriver                        0x0000000102a5b0de chromedriver + 659678
11  chromedriver                        0x0000000102e2b2ad chromedriver + 4657837
12  chromedriver                        0x0000000102e30130 chromedriver + 4677936
13  chromedriver                        0x0000000102e36def chromedriver + 4705775
14  chromedriver                        0x0000000102e3105a chromedriver + 4681818
15  chromedriver                        0x0000000102e0392c chromedriver + 4495660
16  chromedriver                        0x0000000102e4e838 chromedriver + 4802616
17  chromedriver                        0x0000000102e4e9b7 chromedriver + 4802999
18  chromedriver                        0x0000000102e5f99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:43:06,843 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000102e6f6b8 chromedriver + 4937400
1   chromedriver                        0x0000000102e66b73 chromedriver + 4901747
2   chromedriver                        0x0000000102a24616 chromedriver + 435734
3   chromedriver                        0x0000000102a56d10 chromedriver + 642320
4   chromedriver                        0x0000000102a5218a chromedriver + 622986
5   chromedriver                        0x0000000102a4e67c chromedriver + 607868
6   chromedriver                        0x0000000102a95a08 chromedriver + 899592
7   chromedriver                        0x0000000102a94ebf chromedriver + 896703
8   chromedriver                        0x0000000102a8bde3 chromedriver + 859619
9   chromedriver                        0x0000000102a59d7f chromedriver + 654719
10  chromedriver                        0x0000000102a5b0de chromedriver + 659678
11  chromedriver                        0x0000000102e2b2ad chromedriver + 4657837
12  chromedriver                        0x0000000102e30130 chromedriver + 4677936
13  chromedriver                        0x0000000102e36def chromedriver + 4705775
14  chromedriver                        0x0000000102e3105a chromedriver + 4681818
15  chromedriver                        0x0000000102e0392c chromedriver + 4495660
16  chromedriver                        0x0000000102e4e838 chromedriver + 4802616
17  chromedriver                        0x0000000102e4e9b7 chromedriver + 4802999
18  chromedriver                        0x0000000102e5f99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:43:06,843 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000102e6f6b8 chromedriver + 4937400
1   chromedriver                        0x0000000102e66b73 chromedriver + 4901747
2   chromedriver                        0x0000000102a24616 chromedriver + 435734
3   chromedriver                        0x0000000102a56d10 chromedriver + 642320
4   chromedriver                        0x0000000102a5218a chromedriver + 622986
5   chromedriver                        0x0000000102a4e67c chromedriver + 607868
6   chromedriver                        0x0000000102a95a08 chromedriver + 899592
7   chromedriver                        0x0000000102a94ebf chromedriver + 896703
8   chromedriver                        0x0000000102a8bde3 chromedriver + 859619
9   chromedriver                        0x0000000102a59d7f chromedriver + 654719
10  chromedriver                        0x0000000102a5b0de chromedriver + 659678
11  chromedriver                        0x0000000102e2b2ad chromedriver + 4657837
12  chromedriver                        0x0000000102e30130 chromedriver + 4677936
13  chromedriver                        0x0000000102e36def chromedriver + 4705775
14  chromedriver                        0x0000000102e3105a chromedriver + 4681818
15  chromedriver                        0x0000000102e0392c chromedriver + 4495660
16  chromedriver                        0x0000000102e4e838 chromedriver + 4802616
17  chromedriver                        0x0000000102e4e9b7 chromedriver + 4802999
18  chromedriver                        0x0000000102e5f99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000102e6f6b8 chromedriver + 4937400
1   chromedriver                        0x0000000102e66b73 chromedriver + 4901747
2   chromedriver                        0x0000000102a24616 chromedriver + 435734
3   chromedriver                        0x0000000102a56d10 chromedriver + 642320
4   chromedriver                        0x0000000102a5218a chromedriver + 622986
5   chromedriver                        0x0000000102a4e67c chromedriver + 607868
6   chromedriver                        0x0000000102a95a08 chromedriver + 899592
7   chromedriver                        0x0000000102a94ebf chromedriver + 896703
8   chromedriver                        0x0000000102a8bde3 chromedriver + 859619
9   chromedriver                        0x0000000102a59d7f chromedriver + 654719
10  chromedriver                        0x0000000102a5b0de chromedriver + 659678
11  chromedriver                        0x0000000102e2b2ad chromedriver + 4657837
12  chromedriver                        0x0000000102e30130 chromedriver + 4677936
13  chromedriver                        0x0000000102e36def chromedriver + 4705775
14  chromedriver                        0x0000000102e3105a chromedriver + 4681818
15  chromedriver                        0x0000000102e0392c chromedriver + 4495660
16  chromedriver                        0x0000000102e4e838 chromedriver + 4802616
17  chromedriver                        0x0000000102e4e9b7 chromedriver + 4802999
18  chromedriver                        0x0000000102e5f99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:43:54,074 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-02 19:43:54,074 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 19:43:54,074 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-02 19:43:54,074 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-02 19:43:55,130 - discogs_register - ERROR - WebDriver初始化失败: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-02 19:43:55,130 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 491, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 108, in _setup_webdriver
    return self._setup_safari_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 227, in _setup_safari_driver
    return webdriver.Safari()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/safari/webdriver.py", line 114, in __init__
    super().__init__(command_executor=executor, options=options, desired_capabilities=desired_capabilities)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-02 20:24:33,714 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-02 20:24:41,723 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-02 20:24:41,723 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 20:24:41,723 - discogs_register - INFO - 正在初始化edge浏览器...
2025-08-02 20:24:41,724 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找EdgeDriver...
2025-08-02 20:24:41,724 - discogs_register - INFO - 尝试使用PATH中的msedgedriver...
2025-08-02 20:24:41,795 - discogs_register - ERROR - Edge WebDriver初始化失败: Message: 'msedgedriver' executable needs to be in PATH. Please download from https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/

2025-08-02 20:24:41,795 - discogs_register - ERROR - WebDriver初始化失败: Message: 'msedgedriver' executable needs to be in PATH. Please download from https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/

2025-08-02 20:24:41,795 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: 'msedgedriver' executable needs to be in PATH. Please download from https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'edge')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 551, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 106, in _setup_webdriver
    return self._setup_edge_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 272, in _setup_edge_driver
    driver = webdriver.Edge(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/edge/webdriver.py", line 73, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'msedgedriver' executable needs to be in PATH. Please download from https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/

2025-08-02 20:25:10,882 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-02 20:25:10,882 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-02 20:25:10,882 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-02 20:25:10,882 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-02 20:25:11,408 - discogs_register - ERROR - WebDriver初始化失败: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-02 20:25:11,408 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 551, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 108, in _setup_webdriver
    return self._setup_safari_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 287, in _setup_safari_driver
    return webdriver.Safari()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/safari/webdriver.py", line 114, in __init__
    super().__init__(command_executor=executor, options=options, desired_capabilities=desired_capabilities)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-03 14:03:43,217 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-03 14:03:43,218 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:03:43,218 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:03:43,218 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:03:44,397 - discogs_register - ERROR - WebDriver初始化失败: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-03 14:03:44,398 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 551, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 108, in _setup_webdriver
    return self._setup_safari_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 287, in _setup_safari_driver
    return webdriver.Safari()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/safari/webdriver.py", line 114, in __init__
    super().__init__(command_executor=executor, options=options, desired_capabilities=desired_capabilities)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-03 14:05:37,510 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-03 14:05:37,511 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:05:37,512 - discogs_register - INFO - 正在初始化chrome浏览器...
2025-08-03 14:05:37,512 - discogs_register - INFO - webdriver-manager不可用，尝试手动查找ChromeDriver...
2025-08-03 14:06:26,075 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001046f96b8 chromedriver + 4937400
1   chromedriver                        0x00000001046f0b73 chromedriver + 4901747
2   chromedriver                        0x00000001042ae616 chromedriver + 435734
3   chromedriver                        0x00000001042e0d10 chromedriver + 642320
4   chromedriver                        0x00000001042dc18a chromedriver + 622986
5   chromedriver                        0x00000001042d867c chromedriver + 607868
6   chromedriver                        0x000000010431fa08 chromedriver + 899592
7   chromedriver                        0x000000010431eebf chromedriver + 896703
8   chromedriver                        0x0000000104315de3 chromedriver + 859619
9   chromedriver                        0x00000001042e3d7f chromedriver + 654719
10  chromedriver                        0x00000001042e50de chromedriver + 659678
11  chromedriver                        0x00000001046b52ad chromedriver + 4657837
12  chromedriver                        0x00000001046ba130 chromedriver + 4677936
13  chromedriver                        0x00000001046c0def chromedriver + 4705775
14  chromedriver                        0x00000001046bb05a chromedriver + 4681818
15  chromedriver                        0x000000010468d92c chromedriver + 4495660
16  chromedriver                        0x00000001046d8838 chromedriver + 4802616
17  chromedriver                        0x00000001046d89b7 chromedriver + 4802999
18  chromedriver                        0x00000001046e999f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff80a006df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff80a002857 thread_start + 15

2025-08-03 14:06:26,076 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001046f96b8 chromedriver + 4937400
1   chromedriver                        0x00000001046f0b73 chromedriver + 4901747
2   chromedriver                        0x00000001042ae616 chromedriver + 435734
3   chromedriver                        0x00000001042e0d10 chromedriver + 642320
4   chromedriver                        0x00000001042dc18a chromedriver + 622986
5   chromedriver                        0x00000001042d867c chromedriver + 607868
6   chromedriver                        0x000000010431fa08 chromedriver + 899592
7   chromedriver                        0x000000010431eebf chromedriver + 896703
8   chromedriver                        0x0000000104315de3 chromedriver + 859619
9   chromedriver                        0x00000001042e3d7f chromedriver + 654719
10  chromedriver                        0x00000001042e50de chromedriver + 659678
11  chromedriver                        0x00000001046b52ad chromedriver + 4657837
12  chromedriver                        0x00000001046ba130 chromedriver + 4677936
13  chromedriver                        0x00000001046c0def chromedriver + 4705775
14  chromedriver                        0x00000001046bb05a chromedriver + 4681818
15  chromedriver                        0x000000010468d92c chromedriver + 4495660
16  chromedriver                        0x00000001046d8838 chromedriver + 4802616
17  chromedriver                        0x00000001046d89b7 chromedriver + 4802999
18  chromedriver                        0x00000001046e999f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff80a006df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff80a002857 thread_start + 15

2025-08-03 14:06:26,078 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001046f96b8 chromedriver + 4937400
1   chromedriver                        0x00000001046f0b73 chromedriver + 4901747
2   chromedriver                        0x00000001042ae616 chromedriver + 435734
3   chromedriver                        0x00000001042e0d10 chromedriver + 642320
4   chromedriver                        0x00000001042dc18a chromedriver + 622986
5   chromedriver                        0x00000001042d867c chromedriver + 607868
6   chromedriver                        0x000000010431fa08 chromedriver + 899592
7   chromedriver                        0x000000010431eebf chromedriver + 896703
8   chromedriver                        0x0000000104315de3 chromedriver + 859619
9   chromedriver                        0x00000001042e3d7f chromedriver + 654719
10  chromedriver                        0x00000001042e50de chromedriver + 659678
11  chromedriver                        0x00000001046b52ad chromedriver + 4657837
12  chromedriver                        0x00000001046ba130 chromedriver + 4677936
13  chromedriver                        0x00000001046c0def chromedriver + 4705775
14  chromedriver                        0x00000001046bb05a chromedriver + 4681818
15  chromedriver                        0x000000010468d92c chromedriver + 4495660
16  chromedriver                        0x00000001046d8838 chromedriver + 4802616
17  chromedriver                        0x00000001046d89b7 chromedriver + 4802999
18  chromedriver                        0x00000001046e999f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff80a006df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff80a002857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 554, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001046f96b8 chromedriver + 4937400
1   chromedriver                        0x00000001046f0b73 chromedriver + 4901747
2   chromedriver                        0x00000001042ae616 chromedriver + 435734
3   chromedriver                        0x00000001042e0d10 chromedriver + 642320
4   chromedriver                        0x00000001042dc18a chromedriver + 622986
5   chromedriver                        0x00000001042d867c chromedriver + 607868
6   chromedriver                        0x000000010431fa08 chromedriver + 899592
7   chromedriver                        0x000000010431eebf chromedriver + 896703
8   chromedriver                        0x0000000104315de3 chromedriver + 859619
9   chromedriver                        0x00000001042e3d7f chromedriver + 654719
10  chromedriver                        0x00000001042e50de chromedriver + 659678
11  chromedriver                        0x00000001046b52ad chromedriver + 4657837
12  chromedriver                        0x00000001046ba130 chromedriver + 4677936
13  chromedriver                        0x00000001046c0def chromedriver + 4705775
14  chromedriver                        0x00000001046bb05a chromedriver + 4681818
15  chromedriver                        0x000000010468d92c chromedriver + 4495660
16  chromedriver                        0x00000001046d8838 chromedriver + 4802616
17  chromedriver                        0x00000001046d89b7 chromedriver + 4802999
18  chromedriver                        0x00000001046e999f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff80a006df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff80a002857 thread_start + 15

2025-08-03 14:20:25,172 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-03 14:20:33,868 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-03 14:20:33,868 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:20:33,868 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:20:33,868 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:20:34,964 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SBkWkFnT2lGLVpXQjI5S2tSZmtjT2tfS1ItSmJ3X2x0d6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFpOVcwQnVlNmE5eFRpN0ZSX2NUMGs2S3E5YTNuQmRko2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:20:39,931 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:20:39,932 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025005
2025-08-03 14:20:41,154 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:20:54,062 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:20:57,585 - discogs_register - INFO - 密码填写完成
2025-08-03 14:21:09,382 - discogs_register - INFO - 未找到确认密码字段
2025-08-03 14:21:11,966 - discogs_register - INFO - 注册表单填写完成
2025-08-03 14:21:21,960 - discogs_register - INFO - 正在提交注册表单...
2025-08-03 14:21:24,350 - discogs_register - INFO - 注册表单已提交
2025-08-03 14:21:24,351 - discogs_register - INFO - 正在验证注册结果...
2025-08-03 14:21:27,401 - discogs_register - ERROR - 注册失败，错误信息: Unknown username or email
2025-08-03 14:21:27,402 - discogs_register - ERROR - 账号 ywl2025005 注册失败
2025-08-03 14:21:42,569 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-03 14:21:42,569 - discogs_register - INFO - 开始注册账号: ywl2025006
2025-08-03 14:21:42,569 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:21:42,569 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:21:43,621 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SBkWkFnT2lGLVpXQjI5S2tSZmtjT2tfS1ItSmJ3X2x0d6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHFpOVcwQnVlNmE5eFRpN0ZSX2NUMGs2S3E5YTNuQmRko2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:21:49,418 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:21:49,419 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025006
2025-08-03 14:21:50,628 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:22:03,513 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:22:07,417 - discogs_register - INFO - 密码填写完成
2025-08-03 14:22:19,675 - discogs_register - INFO - 未找到确认密码字段
2025-08-03 14:22:21,051 - discogs_register - INFO - 注册表单填写完成
2025-08-03 14:22:31,020 - discogs_register - INFO - 正在提交注册表单...
2025-08-03 14:22:34,030 - discogs_register - INFO - 注册表单已提交
2025-08-03 14:22:34,030 - discogs_register - INFO - 正在验证注册结果...
2025-08-03 14:22:37,083 - discogs_register - ERROR - 注册失败，错误信息: Unknown username or email
2025-08-03 14:22:37,083 - discogs_register - ERROR - 账号 ywl2025006 注册失败
2025-08-03 14:32:56,848 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-03 14:32:56,848 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:32:56,849 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:32:56,849 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:32:57,917 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SBUMnJFSlI0WjZwc29VM2g4dnRXM3pRTjRoQTJDS1NFV6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHZ0eS1Na2dsV3RiaEhUX01EdllGMG5MRlhZeVJsTWFPo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:33:04,489 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:33:04,490 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025005
2025-08-03 14:33:05,564 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:33:18,560 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:33:21,637 - discogs_register - INFO - 密码填写完成
2025-08-03 14:33:33,378 - discogs_register - INFO - 未找到确认密码字段
2025-08-03 14:33:36,161 - discogs_register - INFO - 注册表单填写完成
2025-08-03 14:33:46,165 - discogs_register - INFO - 正在提交注册表单...
2025-08-03 14:33:47,405 - discogs_register - INFO - 注册表单已提交
2025-08-03 14:33:47,405 - discogs_register - INFO - 正在验证注册结果...
2025-08-03 14:34:10,429 - discogs_register - INFO - 检测到成功指示器: welcome
2025-08-03 14:34:10,430 - discogs_register - INFO - 账号 ywl2025005 注册成功！
2025-08-03 14:42:51,487 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-03 14:42:51,488 - discogs_register - INFO - ==================================================
2025-08-03 14:42:51,488 - discogs_register - INFO - Discogs 自动注册程序启动
2025-08-03 14:42:51,488 - discogs_register - INFO - ==================================================
2025-08-03 14:42:51,488 - discogs_register - INFO - 注册配置信息 | Extra: {"registration": {"url": "https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg", "timeout_seconds": 30, "retry_attempts": 3, "retry_delay_seconds": 5}, "accounts": [{"username": "ywl2025005", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025006", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025007", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025008", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025009", "email": "<EMAIL>", "password": "Abcd@1234.", "enabled": true}], "browser": {"type": "safari", "show_window": true, "window_size": {"width": 1280, "height": 720}, "implicit_wait": 10, "page_load_timeout": 30, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/121.0"]}, "selectors": {"username_field": "input[name='username']", "email_field": "input[name='email']", "password_field": "in******************']", "confirm_password_field": "in*******************************']", "submit_button": "button[type='submit'], input[type='submit']", "captcha_container": ".captcha, .recaptcha, [id*='captcha'], [class*='captcha']", "error_messages": ".error, .alert-danger, [class*='error']", "success_messages": ".success, .alert-success, [class*='success']"}, "automation": {"min_delay": 1, "max_delay": 3, "typing_delay": 0.1, "captcha_handling": {"enabled": true, "wait_time": 60, "manual_intervention": true}}, "logging": {"level": "INFO", "log_file": "discogs_register.log", "max_size_mb": 50, "backup_count": 3, "detailed_browser_logs": false}, "proxy": {"enabled": false}, "verification": {"success_indicators": ["registration successful", "account created", "verify your email", "check your email", "email verification", "activate your account"], "timeout": 30}}
2025-08-03 14:42:51,488 - discogs_register - INFO - 开始批量注册，共 5 个账号
2025-08-03 14:42:51,488 - discogs_register - INFO - 正在处理第 1/5 个账号: ywl2025005
2025-08-03 14:42:51,488 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:42:51,488 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:42:51,488 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:42:52,560 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:42:57,693 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:42:57,694 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025005
2025-08-03 14:42:58,922 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:43:11,668 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:43:13,504 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:43:16,178 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025005
2025-08-03 14:43:21,181 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:43:21,181 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:43:21,181 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:43:22,252 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:43:28,162 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:43:28,166 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025005
2025-08-03 14:43:30,748 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:43:43,052 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:43:45,012 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:43:49,029 - discogs_register - INFO - 第 3 次重试注册账号: ywl2025005
2025-08-03 14:43:54,033 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:43:54,034 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:43:54,034 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:43:55,237 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:44:00,221 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:44:00,221 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025005
2025-08-03 14:44:01,591 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:44:12,599 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:44:14,780 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:44:17,617 - discogs_register - INFO - 等待 6.5 秒后处理下一个账号...
2025-08-03 14:44:24,086 - discogs_register - INFO - 正在处理第 2/5 个账号: ywl2025006
2025-08-03 14:44:24,087 - discogs_register - INFO - 开始注册账号: ywl2025006
2025-08-03 14:44:24,087 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:44:24,088 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:44:25,143 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:44:29,751 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:44:29,752 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025006
2025-08-03 14:44:31,087 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:44:43,405 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:44:45,677 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:44:49,766 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025006
2025-08-03 14:44:54,769 - discogs_register - INFO - 开始注册账号: ywl2025006
2025-08-03 14:44:54,770 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:44:54,770 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:44:55,840 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:45:00,446 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:45:00,447 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025006
2025-08-03 14:45:02,055 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:45:13,942 - discogs_register - ERROR - 表单字段加载超时
2025-08-03 14:45:17,141 - discogs_register - INFO - 第 3 次重试注册账号: ywl2025006
2025-08-03 14:45:22,151 - discogs_register - INFO - 开始注册账号: ywl2025006
2025-08-03 14:45:22,151 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:45:22,152 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:45:23,210 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:45:27,468 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:45:27,468 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025006
2025-08-03 14:45:28,725 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:45:40,899 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:45:42,171 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:45:45,042 - discogs_register - INFO - 等待 13.1 秒后处理下一个账号...
2025-08-03 14:45:58,188 - discogs_register - INFO - 正在处理第 3/5 个账号: ywl2025007
2025-08-03 14:45:58,190 - discogs_register - INFO - 开始注册账号: ywl2025007
2025-08-03 14:45:58,190 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:45:58,190 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:45:59,253 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:46:05,595 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:46:05,599 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025007
2025-08-03 14:46:07,624 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:46:20,951 - discogs_register - ERROR - 表单字段加载超时
2025-08-03 14:46:24,156 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025007
2025-08-03 14:46:29,159 - discogs_register - INFO - 开始注册账号: ywl2025007
2025-08-03 14:46:29,160 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:46:29,160 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:46:30,211 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:46:35,568 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:46:35,569 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025007
2025-08-03 14:46:36,633 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:46:47,682 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:46:49,011 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:46:51,748 - discogs_register - INFO - 第 3 次重试注册账号: ywl2025007
2025-08-03 14:46:56,753 - discogs_register - INFO - 开始注册账号: ywl2025007
2025-08-03 14:46:56,754 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:46:56,754 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:46:57,831 - discogs_register - INFO - 正在访问注册页面: https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg
2025-08-03 14:47:03,114 - discogs_register - INFO - 注册页面加载成功
2025-08-03 14:47:03,116 - discogs_register - INFO - 开始填写注册表单，用户名: ywl2025007
2025-08-03 14:47:04,402 - discogs_register - INFO - 用户名填写完成
2025-08-03 14:47:16,637 - discogs_register - WARNING - 未找到邮箱字段，可能不需要填写
2025-08-03 14:47:20,978 - discogs_register - WARNING - 用户中断了注册程序
2025-08-03 14:51:01,588 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-03 14:51:01,588 - discogs_register - INFO - ==================================================
2025-08-03 14:51:01,588 - discogs_register - INFO - Discogs 自动注册程序启动
2025-08-03 14:51:01,588 - discogs_register - INFO - ==================================================
2025-08-03 14:51:01,588 - discogs_register - INFO - 注册配置信息 | Extra: {"registration": {"login_url": "https://login.discogs.com/u/login", "url": "https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg", "timeout_seconds": 30, "retry_attempts": 3, "retry_delay_seconds": 5}, "accounts": [{"username": "ywl2025005", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025006", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025007", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025008", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025009", "email": "<EMAIL>", "password": "Abcd@1234.", "enabled": true}], "browser": {"type": "safari", "show_window": true, "window_size": {"width": 1280, "height": 720}, "implicit_wait": 10, "page_load_timeout": 30, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/121.0"]}, "selectors": {"username_field": "input[name='username']", "email_field": "input[name='email']", "password_field": "in******************']", "confirm_password_field": "in*******************************']", "submit_button": "button[type='submit'], input[type='submit']", "captcha_container": ".captcha, .recaptcha, [id*='captcha'], [class*='captcha']", "error_messages": ".error, .alert-danger, [class*='error']", "success_messages": ".success, .alert-success, [class*='success']"}, "automation": {"min_delay": 1, "max_delay": 3, "typing_delay": 0.1, "captcha_handling": {"enabled": true, "wait_time": 60, "manual_intervention": true}}, "logging": {"level": "INFO", "log_file": "discogs_register.log", "max_size_mb": 50, "backup_count": 3, "detailed_browser_logs": false}, "proxy": {"enabled": false}, "verification": {"success_indicators": ["registration successful", "account created", "verify your email", "check your email", "email verification", "activate your account"], "timeout": 30}}
2025-08-03 14:51:01,588 - discogs_register - INFO - 开始批量注册，共 5 个账号
2025-08-03 14:51:01,588 - discogs_register - INFO - 正在处理第 1/5 个账号: ywl2025005
2025-08-03 14:51:01,588 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:51:01,588 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:51:01,588 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:51:02,651 - discogs_register - INFO - 正在访问登录页面: https://login.discogs.com/u/login
2025-08-03 14:51:08,590 - discogs_register - INFO - 登录页面加载成功
2025-08-03 14:51:08,591 - discogs_register - INFO - 正在查找Sign up链接...
2025-08-03 14:59:05,218 - discogs_register - WARNING - 未找到Sign up链接，尝试使用备用注册URL
2025-08-03 14:59:05,221 - discogs_register - ERROR - 导航到注册页面失败: HTTPConnectionPool(host='localhost', port=60940): Max retries exceeded with url: /session/9CFCB27D-B2EB-427D-BF43-A77F7CB0F635/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x105fc8f10>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-08-03 14:59:07,762 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025005
2025-08-03 14:59:12,766 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:59:12,767 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:59:12,767 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:59:13,829 - discogs_register - INFO - 正在访问登录页面: https://login.discogs.com/u/login
2025-08-03 14:59:19,632 - discogs_register - INFO - 登录页面加载成功
2025-08-03 14:59:19,633 - discogs_register - INFO - 正在查找Sign up链接...
2025-08-03 14:59:34,326 - discogs_register - WARNING - 未找到Sign up链接，尝试使用备用注册URL
2025-08-03 14:59:34,332 - discogs_register - ERROR - 导航到注册页面失败: HTTPConnectionPool(host='localhost', port=50537): Max retries exceeded with url: /session/73AF5B55-491F-4D2C-AC64-E6138A5815CC/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x105fc95a0>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-08-03 14:59:35,307 - discogs_register - WARNING - 用户中断了注册程序
2025-08-03 14:59:40,726 - discogs_register - INFO - Discogs自动注册器初始化完成
2025-08-03 14:59:40,726 - discogs_register - INFO - ==================================================
2025-08-03 14:59:40,727 - discogs_register - INFO - Discogs 自动注册程序启动
2025-08-03 14:59:40,727 - discogs_register - INFO - ==================================================
2025-08-03 14:59:40,727 - discogs_register - INFO - 注册配置信息 | Extra: {"registration": {"login_url": "https://login.discogs.com/u/login", "url": "https://login.discogs.com/u/signup?state=hKFo2SA5MFJ0NDJNei1ndDF5eVA0eVRaRzBrMzNIbmI2R3YzcqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIHIwYlhXejQ5OUtURFRCYjBfVEp3RnJPMW84UFcweUtzo2NpZNkgMDg2SDEyQklDVzFiZnRlMVUwQ056NmV4UVFtSk56SGg", "timeout_seconds": 30, "retry_attempts": 3, "retry_delay_seconds": 5}, "accounts": [{"username": "ywl2025005", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025006", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025007", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025008", "email": "<EMAIL>", "password": "Abcd@1234qaz", "enabled": true}, {"username": "ywl2025009", "email": "<EMAIL>", "password": "Abcd@1234.", "enabled": true}], "browser": {"type": "safari", "show_window": true, "window_size": {"width": 1280, "height": 720}, "implicit_wait": 10, "page_load_timeout": 30, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/121.0"]}, "selectors": {"username_field": "input[name='username']", "email_field": "input[name='email']", "password_field": "in******************']", "confirm_password_field": "in*******************************']", "submit_button": "button[type='submit'], input[type='submit']", "captcha_container": ".captcha, .recaptcha, [id*='captcha'], [class*='captcha']", "error_messages": ".error, .alert-danger, [class*='error']", "success_messages": ".success, .alert-success, [class*='success']"}, "automation": {"min_delay": 1, "max_delay": 3, "typing_delay": 0.1, "captcha_handling": {"enabled": true, "wait_time": 60, "manual_intervention": true}}, "logging": {"level": "INFO", "log_file": "discogs_register.log", "max_size_mb": 50, "backup_count": 3, "detailed_browser_logs": false}, "proxy": {"enabled": false}, "verification": {"success_indicators": ["registration successful", "account created", "verify your email", "check your email", "email verification", "activate your account"], "timeout": 30}}
2025-08-03 14:59:40,727 - discogs_register - INFO - 开始批量注册，共 5 个账号
2025-08-03 14:59:40,727 - discogs_register - INFO - 正在处理第 1/5 个账号: ywl2025005
2025-08-03 14:59:40,727 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:59:40,727 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:59:40,727 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 14:59:41,765 - discogs_register - INFO - 正在访问登录页面: https://login.discogs.com/u/login
2025-08-03 14:59:46,856 - discogs_register - INFO - 登录页面加载成功
2025-08-03 14:59:46,856 - discogs_register - INFO - 正在查找Sign up链接...
2025-08-03 14:59:52,158 - discogs_register - WARNING - 未找到Sign up链接，尝试使用备用注册URL
2025-08-03 14:59:52,160 - discogs_register - ERROR - 导航到注册页面失败: Message: 

2025-08-03 14:59:54,626 - discogs_register - INFO - 第 2 次重试注册账号: ywl2025005
2025-08-03 14:59:59,630 - discogs_register - INFO - 开始注册账号: ywl2025005
2025-08-03 14:59:59,630 - discogs_register - INFO - 正在初始化safari浏览器...
2025-08-03 14:59:59,630 - discogs_register - INFO - 使用Safari浏览器（macOS系统自带）
2025-08-03 15:00:00,044 - discogs_register - WARNING - 用户中断了注册程序
