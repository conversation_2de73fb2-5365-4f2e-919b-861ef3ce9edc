2025-08-02 19:39:54,544 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:54,546 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:54,549 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:59,879 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:59,879 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:39:59,879 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:05,013 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:05,013 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:05,013 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:13,066 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:13,067 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:13,067 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:18,217 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:18,217 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:18,217 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:23,364 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:23,365 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:23,365 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:33,366 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:33,367 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:33,367 - discogs_register - ERROR - 注册账号 ywl2025007 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:38,520 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:38,521 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:38,521 - discogs_register - ERROR - 注册账号 ywl2025007 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:43,676 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:43,676 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:43,677 - discogs_register - ERROR - 注册账号 ywl2025007 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:57,189 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:57,189 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:40:57,189 - discogs_register - ERROR - 注册账号 ywl2025008 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:22,130 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:22,131 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:22,131 - discogs_register - ERROR - 注册账号 ywl2025008 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:27,496 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:27,497 - discogs_register - ERROR - WebDriver初始化失败: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:27,497 - discogs_register - ERROR - 注册账号 ywl2025008 时发生异常: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'chrome')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH. Please see https://chromedriver.chromium.org/home

2025-08-02 19:41:52,425 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x000000010065d6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100654b73 chromedriver + 4901747
2   chromedriver                        0x0000000100212616 chromedriver + 435734
3   chromedriver                        0x0000000100244d10 chromedriver + 642320
4   chromedriver                        0x000000010024018a chromedriver + 622986
5   chromedriver                        0x000000010023c67c chromedriver + 607868
6   chromedriver                        0x0000000100283a08 chromedriver + 899592
7   chromedriver                        0x0000000100282ebf chromedriver + 896703
8   chromedriver                        0x0000000100279de3 chromedriver + 859619
9   chromedriver                        0x0000000100247d7f chromedriver + 654719
10  chromedriver                        0x00000001002490de chromedriver + 659678
11  chromedriver                        0x00000001006192ad chromedriver + 4657837
12  chromedriver                        0x000000010061e130 chromedriver + 4677936
13  chromedriver                        0x0000000100624def chromedriver + 4705775
14  chromedriver                        0x000000010061f05a chromedriver + 4681818
15  chromedriver                        0x00000001005f192c chromedriver + 4495660
16  chromedriver                        0x000000010063c838 chromedriver + 4802616
17  chromedriver                        0x000000010063c9b7 chromedriver + 4802999
18  chromedriver                        0x000000010064d99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:41:52,425 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x000000010065d6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100654b73 chromedriver + 4901747
2   chromedriver                        0x0000000100212616 chromedriver + 435734
3   chromedriver                        0x0000000100244d10 chromedriver + 642320
4   chromedriver                        0x000000010024018a chromedriver + 622986
5   chromedriver                        0x000000010023c67c chromedriver + 607868
6   chromedriver                        0x0000000100283a08 chromedriver + 899592
7   chromedriver                        0x0000000100282ebf chromedriver + 896703
8   chromedriver                        0x0000000100279de3 chromedriver + 859619
9   chromedriver                        0x0000000100247d7f chromedriver + 654719
10  chromedriver                        0x00000001002490de chromedriver + 659678
11  chromedriver                        0x00000001006192ad chromedriver + 4657837
12  chromedriver                        0x000000010061e130 chromedriver + 4677936
13  chromedriver                        0x0000000100624def chromedriver + 4705775
14  chromedriver                        0x000000010061f05a chromedriver + 4681818
15  chromedriver                        0x00000001005f192c chromedriver + 4495660
16  chromedriver                        0x000000010063c838 chromedriver + 4802616
17  chromedriver                        0x000000010063c9b7 chromedriver + 4802999
18  chromedriver                        0x000000010064d99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:41:52,425 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x000000010065d6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100654b73 chromedriver + 4901747
2   chromedriver                        0x0000000100212616 chromedriver + 435734
3   chromedriver                        0x0000000100244d10 chromedriver + 642320
4   chromedriver                        0x000000010024018a chromedriver + 622986
5   chromedriver                        0x000000010023c67c chromedriver + 607868
6   chromedriver                        0x0000000100283a08 chromedriver + 899592
7   chromedriver                        0x0000000100282ebf chromedriver + 896703
8   chromedriver                        0x0000000100279de3 chromedriver + 859619
9   chromedriver                        0x0000000100247d7f chromedriver + 654719
10  chromedriver                        0x00000001002490de chromedriver + 659678
11  chromedriver                        0x00000001006192ad chromedriver + 4657837
12  chromedriver                        0x000000010061e130 chromedriver + 4677936
13  chromedriver                        0x0000000100624def chromedriver + 4705775
14  chromedriver                        0x000000010061f05a chromedriver + 4681818
15  chromedriver                        0x00000001005f192c chromedriver + 4495660
16  chromedriver                        0x000000010063c838 chromedriver + 4802616
17  chromedriver                        0x000000010063c9b7 chromedriver + 4802999
18  chromedriver                        0x000000010064d99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x000000010065d6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100654b73 chromedriver + 4901747
2   chromedriver                        0x0000000100212616 chromedriver + 435734
3   chromedriver                        0x0000000100244d10 chromedriver + 642320
4   chromedriver                        0x000000010024018a chromedriver + 622986
5   chromedriver                        0x000000010023c67c chromedriver + 607868
6   chromedriver                        0x0000000100283a08 chromedriver + 899592
7   chromedriver                        0x0000000100282ebf chromedriver + 896703
8   chromedriver                        0x0000000100279de3 chromedriver + 859619
9   chromedriver                        0x0000000100247d7f chromedriver + 654719
10  chromedriver                        0x00000001002490de chromedriver + 659678
11  chromedriver                        0x00000001006192ad chromedriver + 4657837
12  chromedriver                        0x000000010061e130 chromedriver + 4677936
13  chromedriver                        0x0000000100624def chromedriver + 4705775
14  chromedriver                        0x000000010061f05a chromedriver + 4681818
15  chromedriver                        0x00000001005f192c chromedriver + 4495660
16  chromedriver                        0x000000010063c838 chromedriver + 4802616
17  chromedriver                        0x000000010063c9b7 chromedriver + 4802999
18  chromedriver                        0x000000010064d99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:06,278 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001027816b8 chromedriver + 4937400
1   chromedriver                        0x0000000102778b73 chromedriver + 4901747
2   chromedriver                        0x0000000102336616 chromedriver + 435734
3   chromedriver                        0x0000000102368d10 chromedriver + 642320
4   chromedriver                        0x000000010236418a chromedriver + 622986
5   chromedriver                        0x000000010236067c chromedriver + 607868
6   chromedriver                        0x00000001023a7a08 chromedriver + 899592
7   chromedriver                        0x00000001023a6ebf chromedriver + 896703
8   chromedriver                        0x000000010239dde3 chromedriver + 859619
9   chromedriver                        0x000000010236bd7f chromedriver + 654719
10  chromedriver                        0x000000010236d0de chromedriver + 659678
11  chromedriver                        0x000000010273d2ad chromedriver + 4657837
12  chromedriver                        0x0000000102742130 chromedriver + 4677936
13  chromedriver                        0x0000000102748def chromedriver + 4705775
14  chromedriver                        0x000000010274305a chromedriver + 4681818
15  chromedriver                        0x000000010271592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001027609b7 chromedriver + 4802999
18  chromedriver                        0x000000010277199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:06,278 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001027816b8 chromedriver + 4937400
1   chromedriver                        0x0000000102778b73 chromedriver + 4901747
2   chromedriver                        0x0000000102336616 chromedriver + 435734
3   chromedriver                        0x0000000102368d10 chromedriver + 642320
4   chromedriver                        0x000000010236418a chromedriver + 622986
5   chromedriver                        0x000000010236067c chromedriver + 607868
6   chromedriver                        0x00000001023a7a08 chromedriver + 899592
7   chromedriver                        0x00000001023a6ebf chromedriver + 896703
8   chromedriver                        0x000000010239dde3 chromedriver + 859619
9   chromedriver                        0x000000010236bd7f chromedriver + 654719
10  chromedriver                        0x000000010236d0de chromedriver + 659678
11  chromedriver                        0x000000010273d2ad chromedriver + 4657837
12  chromedriver                        0x0000000102742130 chromedriver + 4677936
13  chromedriver                        0x0000000102748def chromedriver + 4705775
14  chromedriver                        0x000000010274305a chromedriver + 4681818
15  chromedriver                        0x000000010271592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001027609b7 chromedriver + 4802999
18  chromedriver                        0x000000010277199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:06,278 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001027816b8 chromedriver + 4937400
1   chromedriver                        0x0000000102778b73 chromedriver + 4901747
2   chromedriver                        0x0000000102336616 chromedriver + 435734
3   chromedriver                        0x0000000102368d10 chromedriver + 642320
4   chromedriver                        0x000000010236418a chromedriver + 622986
5   chromedriver                        0x000000010236067c chromedriver + 607868
6   chromedriver                        0x00000001023a7a08 chromedriver + 899592
7   chromedriver                        0x00000001023a6ebf chromedriver + 896703
8   chromedriver                        0x000000010239dde3 chromedriver + 859619
9   chromedriver                        0x000000010236bd7f chromedriver + 654719
10  chromedriver                        0x000000010236d0de chromedriver + 659678
11  chromedriver                        0x000000010273d2ad chromedriver + 4657837
12  chromedriver                        0x0000000102742130 chromedriver + 4677936
13  chromedriver                        0x0000000102748def chromedriver + 4705775
14  chromedriver                        0x000000010274305a chromedriver + 4681818
15  chromedriver                        0x000000010271592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001027609b7 chromedriver + 4802999
18  chromedriver                        0x000000010277199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001027816b8 chromedriver + 4937400
1   chromedriver                        0x0000000102778b73 chromedriver + 4901747
2   chromedriver                        0x0000000102336616 chromedriver + 435734
3   chromedriver                        0x0000000102368d10 chromedriver + 642320
4   chromedriver                        0x000000010236418a chromedriver + 622986
5   chromedriver                        0x000000010236067c chromedriver + 607868
6   chromedriver                        0x00000001023a7a08 chromedriver + 899592
7   chromedriver                        0x00000001023a6ebf chromedriver + 896703
8   chromedriver                        0x000000010239dde3 chromedriver + 859619
9   chromedriver                        0x000000010236bd7f chromedriver + 654719
10  chromedriver                        0x000000010236d0de chromedriver + 659678
11  chromedriver                        0x000000010273d2ad chromedriver + 4657837
12  chromedriver                        0x0000000102742130 chromedriver + 4677936
13  chromedriver                        0x0000000102748def chromedriver + 4705775
14  chromedriver                        0x000000010274305a chromedriver + 4681818
15  chromedriver                        0x000000010271592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001027609b7 chromedriver + 4802999
18  chromedriver                        0x000000010277199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:18,701 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000100fbb6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100fb2b73 chromedriver + 4901747
2   chromedriver                        0x0000000100b70616 chromedriver + 435734
3   chromedriver                        0x0000000100ba2d10 chromedriver + 642320
4   chromedriver                        0x0000000100b9e18a chromedriver + 622986
5   chromedriver                        0x0000000100b9a67c chromedriver + 607868
6   chromedriver                        0x0000000100be1a08 chromedriver + 899592
7   chromedriver                        0x0000000100be0ebf chromedriver + 896703
8   chromedriver                        0x0000000100bd7de3 chromedriver + 859619
9   chromedriver                        0x0000000100ba5d7f chromedriver + 654719
10  chromedriver                        0x0000000100ba70de chromedriver + 659678
11  chromedriver                        0x0000000100f772ad chromedriver + 4657837
12  chromedriver                        0x0000000100f7c130 chromedriver + 4677936
13  chromedriver                        0x0000000100f82def chromedriver + 4705775
14  chromedriver                        0x0000000100f7d05a chromedriver + 4681818
15  chromedriver                        0x0000000100f4f92c chromedriver + 4495660
16  chromedriver                        0x0000000100f9a838 chromedriver + 4802616
17  chromedriver                        0x0000000100f9a9b7 chromedriver + 4802999
18  chromedriver                        0x0000000100fab99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:18,702 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000100fbb6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100fb2b73 chromedriver + 4901747
2   chromedriver                        0x0000000100b70616 chromedriver + 435734
3   chromedriver                        0x0000000100ba2d10 chromedriver + 642320
4   chromedriver                        0x0000000100b9e18a chromedriver + 622986
5   chromedriver                        0x0000000100b9a67c chromedriver + 607868
6   chromedriver                        0x0000000100be1a08 chromedriver + 899592
7   chromedriver                        0x0000000100be0ebf chromedriver + 896703
8   chromedriver                        0x0000000100bd7de3 chromedriver + 859619
9   chromedriver                        0x0000000100ba5d7f chromedriver + 654719
10  chromedriver                        0x0000000100ba70de chromedriver + 659678
11  chromedriver                        0x0000000100f772ad chromedriver + 4657837
12  chromedriver                        0x0000000100f7c130 chromedriver + 4677936
13  chromedriver                        0x0000000100f82def chromedriver + 4705775
14  chromedriver                        0x0000000100f7d05a chromedriver + 4681818
15  chromedriver                        0x0000000100f4f92c chromedriver + 4495660
16  chromedriver                        0x0000000100f9a838 chromedriver + 4802616
17  chromedriver                        0x0000000100f9a9b7 chromedriver + 4802999
18  chromedriver                        0x0000000100fab99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:18,702 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000100fbb6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100fb2b73 chromedriver + 4901747
2   chromedriver                        0x0000000100b70616 chromedriver + 435734
3   chromedriver                        0x0000000100ba2d10 chromedriver + 642320
4   chromedriver                        0x0000000100b9e18a chromedriver + 622986
5   chromedriver                        0x0000000100b9a67c chromedriver + 607868
6   chromedriver                        0x0000000100be1a08 chromedriver + 899592
7   chromedriver                        0x0000000100be0ebf chromedriver + 896703
8   chromedriver                        0x0000000100bd7de3 chromedriver + 859619
9   chromedriver                        0x0000000100ba5d7f chromedriver + 654719
10  chromedriver                        0x0000000100ba70de chromedriver + 659678
11  chromedriver                        0x0000000100f772ad chromedriver + 4657837
12  chromedriver                        0x0000000100f7c130 chromedriver + 4677936
13  chromedriver                        0x0000000100f82def chromedriver + 4705775
14  chromedriver                        0x0000000100f7d05a chromedriver + 4681818
15  chromedriver                        0x0000000100f4f92c chromedriver + 4495660
16  chromedriver                        0x0000000100f9a838 chromedriver + 4802616
17  chromedriver                        0x0000000100f9a9b7 chromedriver + 4802999
18  chromedriver                        0x0000000100fab99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000100fbb6b8 chromedriver + 4937400
1   chromedriver                        0x0000000100fb2b73 chromedriver + 4901747
2   chromedriver                        0x0000000100b70616 chromedriver + 435734
3   chromedriver                        0x0000000100ba2d10 chromedriver + 642320
4   chromedriver                        0x0000000100b9e18a chromedriver + 622986
5   chromedriver                        0x0000000100b9a67c chromedriver + 607868
6   chromedriver                        0x0000000100be1a08 chromedriver + 899592
7   chromedriver                        0x0000000100be0ebf chromedriver + 896703
8   chromedriver                        0x0000000100bd7de3 chromedriver + 859619
9   chromedriver                        0x0000000100ba5d7f chromedriver + 654719
10  chromedriver                        0x0000000100ba70de chromedriver + 659678
11  chromedriver                        0x0000000100f772ad chromedriver + 4657837
12  chromedriver                        0x0000000100f7c130 chromedriver + 4677936
13  chromedriver                        0x0000000100f82def chromedriver + 4705775
14  chromedriver                        0x0000000100f7d05a chromedriver + 4681818
15  chromedriver                        0x0000000100f4f92c chromedriver + 4495660
16  chromedriver                        0x0000000100f9a838 chromedriver + 4802616
17  chromedriver                        0x0000000100f9a9b7 chromedriver + 4802999
18  chromedriver                        0x0000000100fab99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:39,780 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001005f56b8 chromedriver + 4937400
1   chromedriver                        0x00000001005ecb73 chromedriver + 4901747
2   chromedriver                        0x00000001001aa616 chromedriver + 435734
3   chromedriver                        0x00000001001dcd10 chromedriver + 642320
4   chromedriver                        0x00000001001d818a chromedriver + 622986
5   chromedriver                        0x00000001001d467c chromedriver + 607868
6   chromedriver                        0x000000010021ba08 chromedriver + 899592
7   chromedriver                        0x000000010021aebf chromedriver + 896703
8   chromedriver                        0x0000000100211de3 chromedriver + 859619
9   chromedriver                        0x00000001001dfd7f chromedriver + 654719
10  chromedriver                        0x00000001001e10de chromedriver + 659678
11  chromedriver                        0x00000001005b12ad chromedriver + 4657837
12  chromedriver                        0x00000001005b6130 chromedriver + 4677936
13  chromedriver                        0x00000001005bcdef chromedriver + 4705775
14  chromedriver                        0x00000001005b705a chromedriver + 4681818
15  chromedriver                        0x000000010058992c chromedriver + 4495660
16  chromedriver                        0x00000001005d4838 chromedriver + 4802616
17  chromedriver                        0x00000001005d49b7 chromedriver + 4802999
18  chromedriver                        0x00000001005e599f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:39,781 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001005f56b8 chromedriver + 4937400
1   chromedriver                        0x00000001005ecb73 chromedriver + 4901747
2   chromedriver                        0x00000001001aa616 chromedriver + 435734
3   chromedriver                        0x00000001001dcd10 chromedriver + 642320
4   chromedriver                        0x00000001001d818a chromedriver + 622986
5   chromedriver                        0x00000001001d467c chromedriver + 607868
6   chromedriver                        0x000000010021ba08 chromedriver + 899592
7   chromedriver                        0x000000010021aebf chromedriver + 896703
8   chromedriver                        0x0000000100211de3 chromedriver + 859619
9   chromedriver                        0x00000001001dfd7f chromedriver + 654719
10  chromedriver                        0x00000001001e10de chromedriver + 659678
11  chromedriver                        0x00000001005b12ad chromedriver + 4657837
12  chromedriver                        0x00000001005b6130 chromedriver + 4677936
13  chromedriver                        0x00000001005bcdef chromedriver + 4705775
14  chromedriver                        0x00000001005b705a chromedriver + 4681818
15  chromedriver                        0x000000010058992c chromedriver + 4495660
16  chromedriver                        0x00000001005d4838 chromedriver + 4802616
17  chromedriver                        0x00000001005d49b7 chromedriver + 4802999
18  chromedriver                        0x00000001005e599f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:39,781 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001005f56b8 chromedriver + 4937400
1   chromedriver                        0x00000001005ecb73 chromedriver + 4901747
2   chromedriver                        0x00000001001aa616 chromedriver + 435734
3   chromedriver                        0x00000001001dcd10 chromedriver + 642320
4   chromedriver                        0x00000001001d818a chromedriver + 622986
5   chromedriver                        0x00000001001d467c chromedriver + 607868
6   chromedriver                        0x000000010021ba08 chromedriver + 899592
7   chromedriver                        0x000000010021aebf chromedriver + 896703
8   chromedriver                        0x0000000100211de3 chromedriver + 859619
9   chromedriver                        0x00000001001dfd7f chromedriver + 654719
10  chromedriver                        0x00000001001e10de chromedriver + 659678
11  chromedriver                        0x00000001005b12ad chromedriver + 4657837
12  chromedriver                        0x00000001005b6130 chromedriver + 4677936
13  chromedriver                        0x00000001005bcdef chromedriver + 4705775
14  chromedriver                        0x00000001005b705a chromedriver + 4681818
15  chromedriver                        0x000000010058992c chromedriver + 4495660
16  chromedriver                        0x00000001005d4838 chromedriver + 4802616
17  chromedriver                        0x00000001005d49b7 chromedriver + 4802999
18  chromedriver                        0x00000001005e599f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001005f56b8 chromedriver + 4937400
1   chromedriver                        0x00000001005ecb73 chromedriver + 4901747
2   chromedriver                        0x00000001001aa616 chromedriver + 435734
3   chromedriver                        0x00000001001dcd10 chromedriver + 642320
4   chromedriver                        0x00000001001d818a chromedriver + 622986
5   chromedriver                        0x00000001001d467c chromedriver + 607868
6   chromedriver                        0x000000010021ba08 chromedriver + 899592
7   chromedriver                        0x000000010021aebf chromedriver + 896703
8   chromedriver                        0x0000000100211de3 chromedriver + 859619
9   chromedriver                        0x00000001001dfd7f chromedriver + 654719
10  chromedriver                        0x00000001001e10de chromedriver + 659678
11  chromedriver                        0x00000001005b12ad chromedriver + 4657837
12  chromedriver                        0x00000001005b6130 chromedriver + 4677936
13  chromedriver                        0x00000001005bcdef chromedriver + 4705775
14  chromedriver                        0x00000001005b705a chromedriver + 4681818
15  chromedriver                        0x000000010058992c chromedriver + 4495660
16  chromedriver                        0x00000001005d4838 chromedriver + 4802616
17  chromedriver                        0x00000001005d49b7 chromedriver + 4802999
18  chromedriver                        0x00000001005e599f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:53,792 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001010916b8 chromedriver + 4937400
1   chromedriver                        0x0000000101088b73 chromedriver + 4901747
2   chromedriver                        0x0000000100c46616 chromedriver + 435734
3   chromedriver                        0x0000000100c78d10 chromedriver + 642320
4   chromedriver                        0x0000000100c7418a chromedriver + 622986
5   chromedriver                        0x0000000100c7067c chromedriver + 607868
6   chromedriver                        0x0000000100cb7a08 chromedriver + 899592
7   chromedriver                        0x0000000100cb6ebf chromedriver + 896703
8   chromedriver                        0x0000000100cadde3 chromedriver + 859619
9   chromedriver                        0x0000000100c7bd7f chromedriver + 654719
10  chromedriver                        0x0000000100c7d0de chromedriver + 659678
11  chromedriver                        0x000000010104d2ad chromedriver + 4657837
12  chromedriver                        0x0000000101052130 chromedriver + 4677936
13  chromedriver                        0x0000000101058def chromedriver + 4705775
14  chromedriver                        0x000000010105305a chromedriver + 4681818
15  chromedriver                        0x000000010102592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001010709b7 chromedriver + 4802999
18  chromedriver                        0x000000010108199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:53,792 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001010916b8 chromedriver + 4937400
1   chromedriver                        0x0000000101088b73 chromedriver + 4901747
2   chromedriver                        0x0000000100c46616 chromedriver + 435734
3   chromedriver                        0x0000000100c78d10 chromedriver + 642320
4   chromedriver                        0x0000000100c7418a chromedriver + 622986
5   chromedriver                        0x0000000100c7067c chromedriver + 607868
6   chromedriver                        0x0000000100cb7a08 chromedriver + 899592
7   chromedriver                        0x0000000100cb6ebf chromedriver + 896703
8   chromedriver                        0x0000000100cadde3 chromedriver + 859619
9   chromedriver                        0x0000000100c7bd7f chromedriver + 654719
10  chromedriver                        0x0000000100c7d0de chromedriver + 659678
11  chromedriver                        0x000000010104d2ad chromedriver + 4657837
12  chromedriver                        0x0000000101052130 chromedriver + 4677936
13  chromedriver                        0x0000000101058def chromedriver + 4705775
14  chromedriver                        0x000000010105305a chromedriver + 4681818
15  chromedriver                        0x000000010102592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001010709b7 chromedriver + 4802999
18  chromedriver                        0x000000010108199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:42:53,792 - discogs_register - ERROR - 注册账号 ywl2025006 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001010916b8 chromedriver + 4937400
1   chromedriver                        0x0000000101088b73 chromedriver + 4901747
2   chromedriver                        0x0000000100c46616 chromedriver + 435734
3   chromedriver                        0x0000000100c78d10 chromedriver + 642320
4   chromedriver                        0x0000000100c7418a chromedriver + 622986
5   chromedriver                        0x0000000100c7067c chromedriver + 607868
6   chromedriver                        0x0000000100cb7a08 chromedriver + 899592
7   chromedriver                        0x0000000100cb6ebf chromedriver + 896703
8   chromedriver                        0x0000000100cadde3 chromedriver + 859619
9   chromedriver                        0x0000000100c7bd7f chromedriver + 654719
10  chromedriver                        0x0000000100c7d0de chromedriver + 659678
11  chromedriver                        0x000000010104d2ad chromedriver + 4657837
12  chromedriver                        0x0000000101052130 chromedriver + 4677936
13  chromedriver                        0x0000000101058def chromedriver + 4705775
14  chromedriver                        0x000000010105305a chromedriver + 4681818
15  chromedriver                        0x000000010102592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001010709b7 chromedriver + 4802999
18  chromedriver                        0x000000010108199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001010916b8 chromedriver + 4937400
1   chromedriver                        0x0000000101088b73 chromedriver + 4901747
2   chromedriver                        0x0000000100c46616 chromedriver + 435734
3   chromedriver                        0x0000000100c78d10 chromedriver + 642320
4   chromedriver                        0x0000000100c7418a chromedriver + 622986
5   chromedriver                        0x0000000100c7067c chromedriver + 607868
6   chromedriver                        0x0000000100cb7a08 chromedriver + 899592
7   chromedriver                        0x0000000100cb6ebf chromedriver + 896703
8   chromedriver                        0x0000000100cadde3 chromedriver + 859619
9   chromedriver                        0x0000000100c7bd7f chromedriver + 654719
10  chromedriver                        0x0000000100c7d0de chromedriver + 659678
11  chromedriver                        0x000000010104d2ad chromedriver + 4657837
12  chromedriver                        0x0000000101052130 chromedriver + 4677936
13  chromedriver                        0x0000000101058def chromedriver + 4705775
14  chromedriver                        0x000000010105305a chromedriver + 4681818
15  chromedriver                        0x000000010102592c chromedriver + 4495660
16  chromedriver                        0x**************** chromedriver + 4802616
17  chromedriver                        0x00000001010709b7 chromedriver + 4802999
18  chromedriver                        0x000000010108199f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:43:06,843 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000102e6f6b8 chromedriver + 4937400
1   chromedriver                        0x0000000102e66b73 chromedriver + 4901747
2   chromedriver                        0x0000000102a24616 chromedriver + 435734
3   chromedriver                        0x0000000102a56d10 chromedriver + 642320
4   chromedriver                        0x0000000102a5218a chromedriver + 622986
5   chromedriver                        0x0000000102a4e67c chromedriver + 607868
6   chromedriver                        0x0000000102a95a08 chromedriver + 899592
7   chromedriver                        0x0000000102a94ebf chromedriver + 896703
8   chromedriver                        0x0000000102a8bde3 chromedriver + 859619
9   chromedriver                        0x0000000102a59d7f chromedriver + 654719
10  chromedriver                        0x0000000102a5b0de chromedriver + 659678
11  chromedriver                        0x0000000102e2b2ad chromedriver + 4657837
12  chromedriver                        0x0000000102e30130 chromedriver + 4677936
13  chromedriver                        0x0000000102e36def chromedriver + 4705775
14  chromedriver                        0x0000000102e3105a chromedriver + 4681818
15  chromedriver                        0x0000000102e0392c chromedriver + 4495660
16  chromedriver                        0x0000000102e4e838 chromedriver + 4802616
17  chromedriver                        0x0000000102e4e9b7 chromedriver + 4802999
18  chromedriver                        0x0000000102e5f99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:43:06,843 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000102e6f6b8 chromedriver + 4937400
1   chromedriver                        0x0000000102e66b73 chromedriver + 4901747
2   chromedriver                        0x0000000102a24616 chromedriver + 435734
3   chromedriver                        0x0000000102a56d10 chromedriver + 642320
4   chromedriver                        0x0000000102a5218a chromedriver + 622986
5   chromedriver                        0x0000000102a4e67c chromedriver + 607868
6   chromedriver                        0x0000000102a95a08 chromedriver + 899592
7   chromedriver                        0x0000000102a94ebf chromedriver + 896703
8   chromedriver                        0x0000000102a8bde3 chromedriver + 859619
9   chromedriver                        0x0000000102a59d7f chromedriver + 654719
10  chromedriver                        0x0000000102a5b0de chromedriver + 659678
11  chromedriver                        0x0000000102e2b2ad chromedriver + 4657837
12  chromedriver                        0x0000000102e30130 chromedriver + 4677936
13  chromedriver                        0x0000000102e36def chromedriver + 4705775
14  chromedriver                        0x0000000102e3105a chromedriver + 4681818
15  chromedriver                        0x0000000102e0392c chromedriver + 4495660
16  chromedriver                        0x0000000102e4e838 chromedriver + 4802616
17  chromedriver                        0x0000000102e4e9b7 chromedriver + 4802999
18  chromedriver                        0x0000000102e5f99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:43:06,843 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000102e6f6b8 chromedriver + 4937400
1   chromedriver                        0x0000000102e66b73 chromedriver + 4901747
2   chromedriver                        0x0000000102a24616 chromedriver + 435734
3   chromedriver                        0x0000000102a56d10 chromedriver + 642320
4   chromedriver                        0x0000000102a5218a chromedriver + 622986
5   chromedriver                        0x0000000102a4e67c chromedriver + 607868
6   chromedriver                        0x0000000102a95a08 chromedriver + 899592
7   chromedriver                        0x0000000102a94ebf chromedriver + 896703
8   chromedriver                        0x0000000102a8bde3 chromedriver + 859619
9   chromedriver                        0x0000000102a59d7f chromedriver + 654719
10  chromedriver                        0x0000000102a5b0de chromedriver + 659678
11  chromedriver                        0x0000000102e2b2ad chromedriver + 4657837
12  chromedriver                        0x0000000102e30130 chromedriver + 4677936
13  chromedriver                        0x0000000102e36def chromedriver + 4705775
14  chromedriver                        0x0000000102e3105a chromedriver + 4681818
15  chromedriver                        0x0000000102e0392c chromedriver + 4495660
16  chromedriver                        0x0000000102e4e838 chromedriver + 4802616
17  chromedriver                        0x0000000102e4e9b7 chromedriver + 4802999
18  chromedriver                        0x0000000102e5f99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 483, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 173, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x0000000102e6f6b8 chromedriver + 4937400
1   chromedriver                        0x0000000102e66b73 chromedriver + 4901747
2   chromedriver                        0x0000000102a24616 chromedriver + 435734
3   chromedriver                        0x0000000102a56d10 chromedriver + 642320
4   chromedriver                        0x0000000102a5218a chromedriver + 622986
5   chromedriver                        0x0000000102a4e67c chromedriver + 607868
6   chromedriver                        0x0000000102a95a08 chromedriver + 899592
7   chromedriver                        0x0000000102a94ebf chromedriver + 896703
8   chromedriver                        0x0000000102a8bde3 chromedriver + 859619
9   chromedriver                        0x0000000102a59d7f chromedriver + 654719
10  chromedriver                        0x0000000102a5b0de chromedriver + 659678
11  chromedriver                        0x0000000102e2b2ad chromedriver + 4657837
12  chromedriver                        0x0000000102e30130 chromedriver + 4677936
13  chromedriver                        0x0000000102e36def chromedriver + 4705775
14  chromedriver                        0x0000000102e3105a chromedriver + 4681818
15  chromedriver                        0x0000000102e0392c chromedriver + 4495660
16  chromedriver                        0x0000000102e4e838 chromedriver + 4802616
17  chromedriver                        0x0000000102e4e9b7 chromedriver + 4802999
18  chromedriver                        0x0000000102e5f99f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff818146df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff818142857 thread_start + 15

2025-08-02 19:43:55,130 - discogs_register - ERROR - WebDriver初始化失败: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-02 19:43:55,130 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 491, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 108, in _setup_webdriver
    return self._setup_safari_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 227, in _setup_safari_driver
    return webdriver.Safari()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/safari/webdriver.py", line 114, in __init__
    super().__init__(command_executor=executor, options=options, desired_capabilities=desired_capabilities)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-02 20:24:41,795 - discogs_register - ERROR - Edge WebDriver初始化失败: Message: 'msedgedriver' executable needs to be in PATH. Please download from https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/

2025-08-02 20:24:41,795 - discogs_register - ERROR - WebDriver初始化失败: Message: 'msedgedriver' executable needs to be in PATH. Please download from https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/

2025-08-02 20:24:41,795 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: 'msedgedriver' executable needs to be in PATH. Please download from https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 97, in start
    path = SeleniumManager.driver_location(browser)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 68, in driver_location
    result = SeleniumManager.run(args)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/selenium_manager.py", line 85, in run
    raise WebDriverException(f"Unsuccessful command executed: {args}")
selenium.common.exceptions.WebDriverException: Message: Unsuccessful command executed: ('/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/macos/selenium-manager', '--browser', 'edge')


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 551, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 106, in _setup_webdriver
    return self._setup_edge_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 272, in _setup_edge_driver
    driver = webdriver.Edge(options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/edge/webdriver.py", line 73, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 103, in __init__
    self.service.start()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 100, in start
    raise err
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 91, in start
    self._start_process(self.path)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/common/service.py", line 203, in _start_process
    raise WebDriverException(
selenium.common.exceptions.WebDriverException: Message: 'msedgedriver' executable needs to be in PATH. Please download from https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/

2025-08-02 20:25:11,408 - discogs_register - ERROR - WebDriver初始化失败: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-02 20:25:11,408 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 551, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 108, in _setup_webdriver
    return self._setup_safari_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 287, in _setup_safari_driver
    return webdriver.Safari()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/safari/webdriver.py", line 114, in __init__
    super().__init__(command_executor=executor, options=options, desired_capabilities=desired_capabilities)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-03 14:03:44,397 - discogs_register - ERROR - WebDriver初始化失败: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-03 14:03:44,398 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 551, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 108, in _setup_webdriver
    return self._setup_safari_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 287, in _setup_safari_driver
    return webdriver.Safari()
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/safari/webdriver.py", line 114, in __init__
    super().__init__(command_executor=executor, options=options, desired_capabilities=desired_capabilities)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: Could not create a session: You must enable 'Allow remote automation' in the Developer section of Safari Settings to control Safari via WebDriver.

2025-08-03 14:06:26,075 - discogs_register - ERROR - Chrome WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001046f96b8 chromedriver + 4937400
1   chromedriver                        0x00000001046f0b73 chromedriver + 4901747
2   chromedriver                        0x00000001042ae616 chromedriver + 435734
3   chromedriver                        0x00000001042e0d10 chromedriver + 642320
4   chromedriver                        0x00000001042dc18a chromedriver + 622986
5   chromedriver                        0x00000001042d867c chromedriver + 607868
6   chromedriver                        0x000000010431fa08 chromedriver + 899592
7   chromedriver                        0x000000010431eebf chromedriver + 896703
8   chromedriver                        0x0000000104315de3 chromedriver + 859619
9   chromedriver                        0x00000001042e3d7f chromedriver + 654719
10  chromedriver                        0x00000001042e50de chromedriver + 659678
11  chromedriver                        0x00000001046b52ad chromedriver + 4657837
12  chromedriver                        0x00000001046ba130 chromedriver + 4677936
13  chromedriver                        0x00000001046c0def chromedriver + 4705775
14  chromedriver                        0x00000001046bb05a chromedriver + 4681818
15  chromedriver                        0x000000010468d92c chromedriver + 4495660
16  chromedriver                        0x00000001046d8838 chromedriver + 4802616
17  chromedriver                        0x00000001046d89b7 chromedriver + 4802999
18  chromedriver                        0x00000001046e999f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff80a006df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff80a002857 thread_start + 15

2025-08-03 14:06:26,076 - discogs_register - ERROR - WebDriver初始化失败: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001046f96b8 chromedriver + 4937400
1   chromedriver                        0x00000001046f0b73 chromedriver + 4901747
2   chromedriver                        0x00000001042ae616 chromedriver + 435734
3   chromedriver                        0x00000001042e0d10 chromedriver + 642320
4   chromedriver                        0x00000001042dc18a chromedriver + 622986
5   chromedriver                        0x00000001042d867c chromedriver + 607868
6   chromedriver                        0x000000010431fa08 chromedriver + 899592
7   chromedriver                        0x000000010431eebf chromedriver + 896703
8   chromedriver                        0x0000000104315de3 chromedriver + 859619
9   chromedriver                        0x00000001042e3d7f chromedriver + 654719
10  chromedriver                        0x00000001042e50de chromedriver + 659678
11  chromedriver                        0x00000001046b52ad chromedriver + 4657837
12  chromedriver                        0x00000001046ba130 chromedriver + 4677936
13  chromedriver                        0x00000001046c0def chromedriver + 4705775
14  chromedriver                        0x00000001046bb05a chromedriver + 4681818
15  chromedriver                        0x000000010468d92c chromedriver + 4495660
16  chromedriver                        0x00000001046d8838 chromedriver + 4802616
17  chromedriver                        0x00000001046d89b7 chromedriver + 4802999
18  chromedriver                        0x00000001046e999f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff80a006df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff80a002857 thread_start + 15

2025-08-03 14:06:26,078 - discogs_register - ERROR - 注册账号 ywl2025005 时发生异常: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001046f96b8 chromedriver + 4937400
1   chromedriver                        0x00000001046f0b73 chromedriver + 4901747
2   chromedriver                        0x00000001042ae616 chromedriver + 435734
3   chromedriver                        0x00000001042e0d10 chromedriver + 642320
4   chromedriver                        0x00000001042dc18a chromedriver + 622986
5   chromedriver                        0x00000001042d867c chromedriver + 607868
6   chromedriver                        0x000000010431fa08 chromedriver + 899592
7   chromedriver                        0x000000010431eebf chromedriver + 896703
8   chromedriver                        0x0000000104315de3 chromedriver + 859619
9   chromedriver                        0x00000001042e3d7f chromedriver + 654719
10  chromedriver                        0x00000001042e50de chromedriver + 659678
11  chromedriver                        0x00000001046b52ad chromedriver + 4657837
12  chromedriver                        0x00000001046ba130 chromedriver + 4677936
13  chromedriver                        0x00000001046c0def chromedriver + 4705775
14  chromedriver                        0x00000001046bb05a chromedriver + 4681818
15  chromedriver                        0x000000010468d92c chromedriver + 4495660
16  chromedriver                        0x00000001046d8838 chromedriver + 4802616
17  chromedriver                        0x00000001046d89b7 chromedriver + 4802999
18  chromedriver                        0x00000001046e999f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff80a006df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff80a002857 thread_start + 15
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 554, in register_single_account
    self.driver = self._setup_webdriver()
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 102, in _setup_webdriver
    return self._setup_chrome_driver(browser_config)
  File "/Users/<USER>/Downloads/yingwuluo/discogs_comparison/discogs_auto_register.py", line 178, in _setup_chrome_driver
    driver = webdriver.Chrome(service=service, options=options)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chrome/webdriver.py", line 81, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/chromium/webdriver.py", line 106, in __init__
    super().__init__(
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 288, in __init__
    self.start_session(capabilities, browser_profile)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 381, in start_session
    response = self.execute(Command.NEW_SESSION, parameters)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/webdriver.py", line 444, in execute
    self.error_handler.check_response(response)
  File "/Library/Frameworks/Python.framework/Versions/3.10/lib/python3.10/site-packages/selenium/webdriver/remote/errorhandler.py", line 249, in check_response
    raise exception_class(message, screen, stacktrace)
selenium.common.exceptions.SessionNotCreatedException: Message: session not created: This version of ChromeDriver only supports Chrome version 114
Current browser version is 138.0.7204.184 with binary path /Applications/Google Chrome.app/Contents/MacOS/Google Chrome
Stacktrace:
0   chromedriver                        0x00000001046f96b8 chromedriver + 4937400
1   chromedriver                        0x00000001046f0b73 chromedriver + 4901747
2   chromedriver                        0x00000001042ae616 chromedriver + 435734
3   chromedriver                        0x00000001042e0d10 chromedriver + 642320
4   chromedriver                        0x00000001042dc18a chromedriver + 622986
5   chromedriver                        0x00000001042d867c chromedriver + 607868
6   chromedriver                        0x000000010431fa08 chromedriver + 899592
7   chromedriver                        0x000000010431eebf chromedriver + 896703
8   chromedriver                        0x0000000104315de3 chromedriver + 859619
9   chromedriver                        0x00000001042e3d7f chromedriver + 654719
10  chromedriver                        0x00000001042e50de chromedriver + 659678
11  chromedriver                        0x00000001046b52ad chromedriver + 4657837
12  chromedriver                        0x00000001046ba130 chromedriver + 4677936
13  chromedriver                        0x00000001046c0def chromedriver + 4705775
14  chromedriver                        0x00000001046bb05a chromedriver + 4681818
15  chromedriver                        0x000000010468d92c chromedriver + 4495660
16  chromedriver                        0x00000001046d8838 chromedriver + 4802616
17  chromedriver                        0x00000001046d89b7 chromedriver + 4802999
18  chromedriver                        0x00000001046e999f chromedriver + 4872607
19  libsystem_pthread.dylib             0x00007ff80a006df1 _pthread_start + 99
20  libsystem_pthread.dylib             0x00007ff80a002857 thread_start + 15

2025-08-03 14:21:27,401 - discogs_register - ERROR - 注册失败，错误信息: Unknown username or email
2025-08-03 14:21:27,402 - discogs_register - ERROR - 账号 ywl2025005 注册失败
2025-08-03 14:22:37,083 - discogs_register - ERROR - 注册失败，错误信息: Unknown username or email
2025-08-03 14:22:37,083 - discogs_register - ERROR - 账号 ywl2025006 注册失败
2025-08-03 14:43:13,504 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:43:45,012 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:44:14,780 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:44:45,677 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:45:13,942 - discogs_register - ERROR - 表单字段加载超时
2025-08-03 14:45:42,171 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:46:20,951 - discogs_register - ERROR - 表单字段加载超时
2025-08-03 14:46:49,011 - discogs_register - ERROR - 填写注册表单失败: Message: 

2025-08-03 14:59:05,221 - discogs_register - ERROR - 导航到注册页面失败: HTTPConnectionPool(host='localhost', port=60940): Max retries exceeded with url: /session/9CFCB27D-B2EB-427D-BF43-A77F7CB0F635/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x105fc8f10>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-08-03 14:59:34,332 - discogs_register - ERROR - 导航到注册页面失败: HTTPConnectionPool(host='localhost', port=50537): Max retries exceeded with url: /session/73AF5B55-491F-4D2C-AC64-E6138A5815CC/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x105fc95a0>: Failed to establish a new connection: [Errno 61] Connection refused'))
2025-08-03 14:59:52,160 - discogs_register - ERROR - 导航到注册页面失败: Message: 

