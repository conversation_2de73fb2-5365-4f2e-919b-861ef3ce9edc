# Discogs 数据比较批处理 Windows 部署脚本
# 适用于 Windows Server 2019/2022 和 Windows 10/11
# PowerShell 5.1 或更高版本

param(
    [switch]$SkipPythonInstall,
    [switch]$SkipDependencies,
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = "Red"
        "Green" = "Green" 
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 检查PowerShell版本
function Test-PowerShellVersion {
    Write-Info "检查PowerShell版本..."
    
    $version = $PSVersionTable.PSVersion
    Write-Info "PowerShell版本: $($version.ToString())"
    
    if ($version.Major -lt 5) {
        Write-Error "需要PowerShell 5.0或更高版本"
        return $false
    }
    
    Write-Success "PowerShell版本满足要求"
    return $true
}

# 检查Python安装
function Test-PythonInstallation {
    Write-Info "检查Python安装..."
    
    $pythonCommands = @("python", "python3", "py")
    $pythonPath = $null
    
    foreach ($cmd in $pythonCommands) {
        try {
            $version = & $cmd --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                $pythonPath = $cmd
                Write-Info "找到Python: $cmd - $version"
                break
            }
        }
        catch {
            continue
        }
    }
    
    if (-not $pythonPath) {
        Write-Warning "未找到Python安装"
        return $null
    }
    
    # 检查Python版本
    try {
        $versionOutput = & $pythonPath -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}')"
        $versionParts = $versionOutput.Split('.')
        $major = [int]$versionParts[0]
        $minor = [int]$versionParts[1]
        
        if ($major -lt 3 -or ($major -eq 3 -and $minor -lt 8)) {
            Write-Error "Python版本过低，需要3.8或更高版本，当前版本: $versionOutput"
            return $null
        }
        
        Write-Success "Python版本满足要求: $versionOutput"
        return $pythonPath
    }
    catch {
        Write-Error "无法检查Python版本"
        return $null
    }
}

# 安装Python（如果需要）
function Install-Python {
    Write-Info "准备安装Python..."
    
    # 检查是否有Chocolatey
    try {
        choco --version | Out-Null
        Write-Info "使用Chocolatey安装Python..."
        choco install python -y
        return $true
    }
    catch {
        Write-Warning "未找到Chocolatey包管理器"
    }
    
    # 检查是否有winget
    try {
        winget --version | Out-Null
        Write-Info "使用winget安装Python..."
        winget install Python.Python.3.11
        return $true
    }
    catch {
        Write-Warning "未找到winget包管理器"
    }
    
    Write-Error "无法自动安装Python，请手动安装Python 3.8或更高版本"
    Write-Info "下载地址: https://www.python.org/downloads/"
    return $false
}

# 创建虚拟环境
function New-VirtualEnvironment {
    param([string]$PythonPath)
    
    Write-Info "创建Python虚拟环境..."
    
    if (Test-Path "venv") {
        Write-Warning "虚拟环境已存在"
        $response = Read-Host "是否重新创建虚拟环境？(y/N)"
        if ($response -eq "y" -or $response -eq "Y") {
            Remove-Item -Recurse -Force "venv"
        } else {
            Write-Info "跳过虚拟环境创建"
            return $true
        }
    }
    
    try {
        & $PythonPath -m venv venv
        Write-Success "虚拟环境创建完成"
        return $true
    }
    catch {
        Write-Error "虚拟环境创建失败: $_"
        return $false
    }
}

# 激活虚拟环境并安装依赖
function Install-Dependencies {
    Write-Info "安装Python依赖..."
    
    if (-not (Test-Path "requirements.txt")) {
        Write-Error "requirements.txt 文件不存在"
        return $false
    }
    
    # 激活虚拟环境
    $activateScript = "venv\Scripts\Activate.ps1"
    if (-not (Test-Path $activateScript)) {
        Write-Error "虚拟环境激活脚本不存在"
        return $false
    }
    
    try {
        # 设置执行策略以允许运行脚本
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope Process -Force
        
        # 激活虚拟环境
        & $activateScript
        
        # 升级pip
        python -m pip install --upgrade pip
        
        # 安装依赖
        pip install -r requirements.txt
        
        Write-Success "Python依赖安装完成"
        return $true
    }
    catch {
        Write-Error "依赖安装失败: $_"
        return $false
    }
}

# 创建必要的目录
function New-RequiredDirectories {
    Write-Info "创建必要的目录..."
    
    $directories = @("logs", "config", "backup")
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir | Out-Null
            Write-Info "创建目录: $dir"
        }
    }
    
    Write-Success "目录创建完成"
}

# 测试批处理脚本
function Test-BatchScript {
    Write-Info "测试批处理脚本..."
    
    try {
        # 激活虚拟环境
        & "venv\Scripts\Activate.ps1"
        
        # 运行配置检查
        $result = python batch_run.py --config-check
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "批处理脚本测试通过"
            return $true
        } else {
            Write-Warning "批处理脚本测试失败，请检查配置"
            return $false
        }
    }
    catch {
        Write-Error "批处理脚本测试失败: $_"
        return $false
    }
}

# 创建Windows定时任务示例
function New-TaskSchedulerExample {
    Write-Info "创建Windows定时任务示例..."
    
    $currentPath = (Get-Location).Path
    $pythonPath = Join-Path $currentPath "venv\Scripts\python.exe"
    $scriptPath = Join-Path $currentPath "batch_run.py"
    
    $taskXml = @"
<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.4" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <RegistrationInfo>
    <Date>$(Get-Date -Format "yyyy-MM-ddTHH:mm:ss")</Date>
    <Author>Discogs Batch Processor</Author>
    <Description>Discogs 数据比较批处理任务</Description>
  </RegistrationInfo>
  <Triggers>
    <CalendarTrigger>
      <StartBoundary>$(Get-Date -Format "yyyy-MM-ddT02:00:00")</StartBoundary>
      <Enabled>true</Enabled>
      <ScheduleByDay>
        <DaysInterval>1</DaysInterval>
      </ScheduleByDay>
    </CalendarTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <UserId>S-1-5-18</UserId>
      <RunLevel>LeastPrivilege</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>false</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>true</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>true</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <DisallowStartOnRemoteAppSession>false</DisallowStartOnRemoteAppSession>
    <UseUnifiedSchedulingEngine>true</UseUnifiedSchedulingEngine>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT4H</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>$pythonPath</Command>
      <Arguments>$scriptPath --all</Arguments>
      <WorkingDirectory>$currentPath</WorkingDirectory>
    </Exec>
  </Actions>
</Task>
"@
    
    $taskXml | Out-File -FilePath "DiscogsBatchTask.xml" -Encoding UTF8
    
    Write-Success "Windows定时任务示例已创建: DiscogsBatchTask.xml"
    Write-Info "使用方法:"
    Write-Info "1. 打开任务计划程序 (taskschd.msc)"
    Write-Info "2. 选择 '导入任务'"
    Write-Info "3. 选择 DiscogsBatchTask.xml 文件"
    Write-Info "4. 根据需要调整任务设置"
}

# 显示完成信息
function Show-CompletionInfo {
    Write-Success "🎉 Windows部署完成！"
    Write-Host ""
    Write-Info "📋 接下来的步骤："
    Write-Info "1. 编辑OSS配置文件: config\oss_config.yaml"
    Write-Info "2. 根据需要调整批处理配置: config\batch_config.yaml"
    Write-Info "3. 测试批处理脚本:"
    Write-Info "   venv\Scripts\Activate.ps1"
    Write-Info "   python batch_run.py --config-check"
    Write-Info "   python batch_run.py --status"
    Write-Info "4. 设置定时任务:"
    Write-Info "   导入 DiscogsBatchTask.xml 到任务计划程序"
    Write-Host ""
    Write-Info "📖 更多信息请查看: WINDOWS_DEPLOYMENT_GUIDE.md"
}

# 主函数
function Main {
    Write-Host "Discogs 数据比较批处理 Windows 部署脚本" -ForegroundColor Cyan
    Write-Host "适用于 Windows Server 2019/2022 和 Windows 10/11" -ForegroundColor Cyan
    Write-Host "=================================================" -ForegroundColor Cyan
    Write-Host ""
    
    # 检查PowerShell版本
    if (-not (Test-PowerShellVersion)) {
        exit 1
    }
    
    # 检查管理员权限（可选）
    if (-not (Test-Administrator)) {
        Write-Warning "未以管理员身份运行，某些操作可能失败"
        $response = Read-Host "是否继续？(y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            exit 1
        }
    }
    
    # 检查Python安装
    $pythonPath = Test-PythonInstallation
    
    if (-not $pythonPath -and -not $SkipPythonInstall) {
        if (-not (Install-Python)) {
            Write-Error "Python安装失败，请手动安装后重新运行脚本"
            exit 1
        }
        
        # 重新检查Python
        $pythonPath = Test-PythonInstallation
        if (-not $pythonPath) {
            Write-Error "Python安装后仍无法找到，请检查PATH环境变量"
            exit 1
        }
    }
    
    # 创建虚拟环境
    if (-not (New-VirtualEnvironment -PythonPath $pythonPath)) {
        exit 1
    }
    
    # 安装依赖
    if (-not $SkipDependencies) {
        if (-not (Install-Dependencies)) {
            exit 1
        }
    }
    
    # 创建目录
    New-RequiredDirectories
    
    # 测试脚本
    Test-BatchScript
    
    # 创建定时任务示例
    New-TaskSchedulerExample
    
    # 显示完成信息
    Show-CompletionInfo
}

# 运行主函数
try {
    Main
}
catch {
    Write-Error "部署过程中发生错误: $_"
    exit 1
}
