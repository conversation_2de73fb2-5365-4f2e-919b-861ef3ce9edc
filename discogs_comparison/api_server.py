#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Discogs 数据比较 Web API 服务
提供HTTP接口来获取不同类型的diff文件
"""

from flask import Flask, jsonify, request, send_file
import os
import sys
from pathlib import Path
import datetime
import logging
from get_diff import DiffAPI

# 创建Flask应用
app = Flask(__name__)

# 简单的CORS支持
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建DiffAPI实例
diff_api = DiffAPI()


@app.route('/', methods=['GET'])
def home():
    """首页 - API文档"""
    return jsonify({
        'service': 'Discogs 数据比较 API',
        'version': '1.0.0',
        'description': '提供HTTP接口获取不同类型的diff文件',
        'endpoints': {
            'GET /': '显示API文档',
            'GET /api/health': '健康检查',
            'GET /api/status': '查看所有diff文件状态',
            'GET /api/types': '获取支持的数据类型',
            'GET /api/diff/<type>': '获取指定类型的diff文件',
            'GET /api/diff/<type>/download': '下载指定类型的diff文件'
        },
        'supported_types': ['release', 'master', 'artists', 'label'],
        'query_parameters': {
            'force': 'true/false - 是否强制重新生成diff文件'
        },
        'examples': [
            '/api/diff/release',
            '/api/diff/master?force=true',
            '/api/diff/artists/download'
        ]
    })


@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.datetime.now().isoformat(),
        'service': 'Discogs Diff API'
    })


@app.route('/api/types', methods=['GET'])
def get_supported_types():
    """获取支持的数据类型"""
    types_info = []
    for data_type, config in diff_api.config.items():
        types_info.append({
            'type': data_type,
            'description': config['description'],
            'output_file': config['output_file']
        })
    
    return jsonify({
        'supported_types': types_info,
        'total_count': len(types_info)
    })


@app.route('/api/status', methods=['GET'])
def get_diff_status():
    """获取所有diff文件的状态"""
    status_info = []
    
    for data_type, config in diff_api.config.items():
        script_dir = diff_api.base_dir / config['script_dir']
        output_file = script_dir / config['output_file']
        
        if output_file.exists():
            # 获取文件信息
            stat = output_file.stat()
            size_mb = stat.st_size / 1024 / 1024
            mtime = datetime.datetime.fromtimestamp(stat.st_mtime)
            
            status_info.append({
                'type': data_type,
                'description': config['description'],
                'exists': True,
                'file_path': str(output_file),
                'size_mb': round(size_mb, 2),
                'modified_time': mtime.isoformat(),
                'age_hours': round((datetime.datetime.now() - mtime).total_seconds() / 3600, 1)
            })
        else:
            status_info.append({
                'type': data_type,
                'description': config['description'],
                'exists': False,
                'file_path': str(output_file),
                'size_mb': 0,
                'modified_time': None,
                'age_hours': None
            })
    
    return jsonify({
        'diff_files': status_info,
        'total_count': len(status_info),
        'existing_count': sum(1 for item in status_info if item['exists'])
    })


@app.route('/api/diff/<data_type>', methods=['GET'])
def get_diff_file(data_type):
    """获取指定类型的diff文件信息"""
    try:
        # 获取查询参数
        force_regenerate = request.args.get('force', 'false').lower() == 'true'
        
        logger.info(f"请求获取 {data_type} diff文件，force={force_regenerate}")
        
        # 验证数据类型
        if data_type not in diff_api.config:
            return jsonify({
                'error': f'不支持的数据类型: {data_type}',
                'supported_types': list(diff_api.config.keys())
            }), 400
        
        # 获取diff文件
        diff_file_path = diff_api.get_diff(data_type, force_regenerate)
        
        if diff_file_path:
            # 获取文件信息
            file_path = Path(diff_file_path)
            stat = file_path.stat()
            size_mb = stat.st_size / 1024 / 1024
            mtime = datetime.datetime.fromtimestamp(stat.st_mtime)
            
            return jsonify({
                'success': True,
                'data_type': data_type,
                'description': diff_api.config[data_type]['description'],
                'file_path': diff_file_path,
                'file_name': file_path.name,
                'size_mb': round(size_mb, 2),
                'modified_time': mtime.isoformat(),
                'download_url': f'/api/diff/{data_type}/download',
                'force_regenerated': force_regenerate
            })
        else:
            return jsonify({
                'error': f'获取 {data_type} diff文件失败',
                'data_type': data_type
            }), 500
            
    except Exception as e:
        logger.error(f"处理请求时出错: {e}")
        return jsonify({
            'error': f'服务器内部错误: {str(e)}',
            'data_type': data_type
        }), 500


@app.route('/api/diff/<data_type>/download', methods=['GET'])
def download_diff_file(data_type):
    """下载指定类型的diff文件"""
    try:
        # 验证数据类型
        if data_type not in diff_api.config:
            return jsonify({
                'error': f'不支持的数据类型: {data_type}',
                'supported_types': list(diff_api.config.keys())
            }), 400
        
        # 获取diff文件路径（不强制重新生成）
        diff_file_path = diff_api.get_diff(data_type, force_regenerate=False)
        
        if diff_file_path and os.path.exists(diff_file_path):
            return send_file(
                diff_file_path,
                as_attachment=True,
                download_name=f'{data_type}_comparison_diff.csv',
                mimetype='text/csv'
            )
        else:
            return jsonify({
                'error': f'{data_type} diff文件不存在，请先生成',
                'generate_url': f'/api/diff/{data_type}?force=true'
            }), 404
            
    except Exception as e:
        logger.error(f"下载文件时出错: {e}")
        return jsonify({
            'error': f'下载失败: {str(e)}',
            'data_type': data_type
        }), 500


@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'error': '接口不存在',
        'message': '请查看根路径 / 获取API文档'
    }), 404


@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'error': '服务器内部错误',
        'message': '请联系管理员'
    }), 500


if __name__ == '__main__':
    # 开发模式运行
    print("🚀 启动 Discogs 数据比较 API 服务...")
    print("📖 API文档: http://localhost:8080/")
    print("🔍 健康检查: http://localhost:8080/api/health")
    print("📊 状态查询: http://localhost:8080/api/status")
    print("🎯 示例请求: http://localhost:8080/api/diff/release")
    
    app.run(
        host='0.0.0.0',  # 允许外部访问
        port=8080,
        debug=True
    )
