#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Discogs 数据比较批处理主脚本
支持命令行参数，自动执行数据比较并上传到阿里云OSS
"""

import sys
import argparse
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from batch_processor import BatchExecutor, BatchConfig


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='Discogs 数据比较批处理工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s release                    # 处理release数据类型
  %(prog)s master --force             # 强制重新生成master数据
  %(prog)s --all                      # 处理所有数据类型
  %(prog)s artists --no-upload        # 处理artists但不上传到OSS
  %(prog)s --status                   # 查看当前状态
  %(prog)s --config-check             # 检查配置

支持的数据类型: release, master, artists, label
        """
    )
    
    # 位置参数：数据类型
    parser.add_argument(
        'data_type',
        nargs='?',
        choices=['release', 'master', 'artists', 'label'],
        help='要处理的数据类型'
    )
    
    # 可选参数
    parser.add_argument(
        '--all',
        action='store_true',
        help='处理所有数据类型'
    )
    
    parser.add_argument(
        '--force',
        action='store_true',
        help='强制重新生成diff文件'
    )
    
    parser.add_argument(
        '--no-upload',
        action='store_true',
        help='不上传文件到OSS'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示当前状态信息'
    )
    
    parser.add_argument(
        '--config-check',
        action='store_true',
        help='检查配置文件'
    )
    
    parser.add_argument(
        '--config-dir',
        type=str,
        help='指定配置文件目录'
    )
    
    parser.add_argument(
        '--output-format',
        choices=['text', 'json'],
        default='text',
        help='输出格式 (默认: text)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出模式'
    )
    
    return parser


def print_status_report(status_report: dict, output_format: str = 'text'):
    """打印状态报告"""
    if output_format == 'json':
        print(json.dumps(status_report, indent=2, ensure_ascii=False))
        return
    
    print("=" * 60)
    print("Discogs 数据比较批处理状态报告")
    print("=" * 60)
    print(f"生成时间: {status_report.get('timestamp', 'N/A')}")
    print(f"OSS配置状态: {'已配置' if status_report.get('oss_configured') else '未配置'}")
    print(f"支持的数据类型: {', '.join(status_report.get('supported_types', []))}")
    print()
    
    print("Diff文件状态:")
    print("-" * 40)
    for file_info in status_report.get('diff_files', []):
        status_icon = "✅" if file_info.get('exists') else "❌"
        print(f"{status_icon} {file_info['type']}")
        
        if file_info.get('exists'):
            print(f"   文件大小: {file_info.get('size_mb', 0):.2f} MB")
            print(f"   修改时间: {file_info.get('modified_time', 'N/A')}")
            print(f"   文件年龄: {file_info.get('age_hours', 0):.1f} 小时")
        else:
            print(f"   状态: 文件不存在")
        print()


def print_execution_results(results: dict, output_format: str = 'text'):
    """打印执行结果"""
    if output_format == 'json':
        print(json.dumps(results, indent=2, ensure_ascii=False))
        return
    
    print("=" * 60)
    print("执行结果")
    print("=" * 60)
    
    for data_type, result in results.items():
        status_icon = "✅" if result['success'] else "❌"
        print(f"{status_icon} {data_type}: {'成功' if result['success'] else '失败'}")
        
        if result['success']:
            if result.get('local_file'):
                print(f"   本地文件: {result['local_file']}")
            if result.get('oss_key'):
                print(f"   OSS键名: {result['oss_key']}")
        print()


def check_configuration(executor: BatchExecutor, output_format: str = 'text'):
    """检查配置"""
    config_status = {
        'batch_config_valid': True,
        'oss_config_valid': executor.config.validate_oss_config(),
        'oss_client_initialized': executor.oss_client is not None,
        'supported_data_types': executor.config.get_supported_data_types(),
        'log_config': executor.config.get_log_config(),
        'execution_config': executor.config.get_execution_config()
    }
    
    if output_format == 'json':
        print(json.dumps(config_status, indent=2, ensure_ascii=False))
        return
    
    print("=" * 60)
    print("配置检查结果")
    print("=" * 60)
    print(f"批处理配置: {'✅ 有效' if config_status['batch_config_valid'] else '❌ 无效'}")
    print(f"OSS配置: {'✅ 有效' if config_status['oss_config_valid'] else '❌ 无效'}")
    print(f"OSS客户端: {'✅ 已初始化' if config_status['oss_client_initialized'] else '❌ 未初始化'}")
    print(f"支持的数据类型: {', '.join(config_status['supported_data_types'])}")
    print()
    
    if not config_status['oss_config_valid']:
        print("⚠️  OSS配置不完整，请检查以下配置项:")
        print("   - endpoint: OSS服务端点")
        print("   - access_key_id: 访问密钥ID")
        print("   - access_key_secret: 访问密钥Secret")
        print("   - bucket_name: 存储桶名称")
        print()
        print("配置文件位置: config/oss_config.yaml")


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 如果没有提供任何参数，显示帮助信息
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    try:
        # 初始化批处理执行器
        executor = BatchExecutor(args.config_dir)
        
        # 处理状态查询
        if args.status:
            status_report = executor.get_status_report()
            print_status_report(status_report, args.output_format)
            return
        
        # 处理配置检查
        if args.config_check:
            check_configuration(executor, args.output_format)
            return
        
        # 确定要处理的数据类型
        if args.all:
            # 处理所有数据类型
            if args.verbose:
                print("开始处理所有数据类型...")
            
            results = executor.execute_all_tasks(
                force_regenerate=args.force,
                upload_to_oss=not args.no_upload
            )
            
            print_execution_results(results, args.output_format)
            
        elif args.data_type:
            # 处理单个数据类型
            if args.verbose:
                print(f"开始处理数据类型: {args.data_type}")
            
            success, local_file, oss_key = executor.execute_single_task(
                args.data_type,
                force_regenerate=args.force,
                upload_to_oss=not args.no_upload
            )
            
            result = {
                args.data_type: {
                    'success': success,
                    'local_file': local_file,
                    'oss_key': oss_key
                }
            }
            
            print_execution_results(result, args.output_format)
            
            # 设置退出码
            sys.exit(0 if success else 1)
            
        else:
            print("错误: 请指定要处理的数据类型或使用 --all 处理所有类型")
            parser.print_help()
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1)
    
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
